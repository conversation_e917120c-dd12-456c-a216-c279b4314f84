#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速管道连接测试
简化版本，用于快速验证管道连接是否正常
"""

import win32pipe
import win32file
import win32api
import time
import threading

def test_pipe_quick():
    """快速管道测试"""
    pipe_name = r'\\.\pipe\poe2_auto_drink'
    
    print("🧪 快速管道连接测试")
    print("="*50)
    
    # 服务器端
    def server():
        print("📡 [服务器] 创建管道...")
        try:
            pipe_handle = win32pipe.CreateNamedPipe(
                pipe_name,
                win32pipe.PIPE_ACCESS_DUPLEX,
                win32pipe.PIPE_TYPE_BYTE | win32pipe.PIPE_WAIT,
                1, 1024, 1024, 0, None
            )
            
            if pipe_handle == -1:
                error_code = win32api.GetLastError()
                print(f"❌ [服务器] 管道创建失败: {error_code}")
                return
            
            print(f"✅ [服务器] 管道创建成功")
            print("📡 [服务器] 等待连接...")
            
            # 等待连接
            result = win32pipe.ConnectNamedPipe(pipe_handle, None)
            if result == 0:
                error_code = win32api.GetLastError()
                if error_code == 535:  # ERROR_PIPE_CONNECTED
                    print("🎉 [服务器] 客户端已连接")
                else:
                    print(f"❌ [服务器] 连接失败: {error_code}")
                    return
            else:
                print("🎉 [服务器] 客户端已连接")
            
            # 读取消息
            try:
                result = win32file.ReadFile(pipe_handle, 1024)
                if result[0] == 0:
                    message = result[1].decode('utf-8', errors='ignore')
                    print(f"📨 [服务器] 收到消息: {message}")
            except Exception as e:
                print(f"📨 [服务器] 读取消息: {e}")
            
            # 关闭管道
            win32file.CloseHandle(pipe_handle)
            print("🛑 [服务器] 管道已关闭")
            
        except Exception as e:
            print(f"❌ [服务器] 异常: {e}")
    
    # 客户端
    def client():
        time.sleep(1)  # 等待服务器启动
        print("🔌 [客户端] 等待管道...")
        
        try:
            # 等待管道可用
            wait_result = win32pipe.WaitNamedPipe(pipe_name, 5000)
            if wait_result == 0:
                error_code = win32api.GetLastError()
                print(f"❌ [客户端] 管道不可用: {error_code}")
                return
            
            print("✅ [客户端] 管道可用")
            
            # 连接管道
            pipe_handle = win32file.CreateFile(
                pipe_name,
                win32file.GENERIC_READ | win32file.GENERIC_WRITE,
                0, None,
                win32file.OPEN_EXISTING,
                0, None
            )
            
            if pipe_handle == win32file.INVALID_HANDLE_VALUE:
                error_code = win32api.GetLastError()
                print(f"❌ [客户端] 连接失败: {error_code}")
                return
            
            print("✅ [客户端] 连接成功")
            
            # 发送消息
            message = b"Hello from client!"
            result = win32file.WriteFile(pipe_handle, message)
            if result[0] == 0:
                print(f"📤 [客户端] 消息发送成功: {len(message)} 字节")
            else:
                print(f"❌ [客户端] 消息发送失败: {result[0]}")
            
            # 关闭连接
            win32file.CloseHandle(pipe_handle)
            print("🛑 [客户端] 连接已关闭")
            
        except Exception as e:
            print(f"❌ [客户端] 异常: {e}")
    
    # 启动服务器和客户端线程
    server_thread = threading.Thread(target=server, daemon=True)
    client_thread = threading.Thread(target=client, daemon=True)
    
    server_thread.start()
    client_thread.start()
    
    # 等待完成
    server_thread.join(timeout=10)
    client_thread.join(timeout=10)
    
    print("="*50)
    print("🏁 测试完成")

if __name__ == "__main__":
    test_pipe_quick()
    input("\n按回车键退出...")
