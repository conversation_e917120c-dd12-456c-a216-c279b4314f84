@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ================================================================
echo                POE2 自动喝药 - 混合架构启动器 (修复版)
echo                Python检测 + AHK硬件输入 = 最高性能
echo ================================================================
echo.

:: 切换到脚本所在目录
cd /d "%~dp0"

:: 检查管理员权限
net session >nul 2>&1
if errorlevel 1 (
    echo [错误] 需要管理员权限！
    echo [提示] 请右键点击此脚本，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)
echo [成功] 管理员权限检查通过

:: 检查Python是否安装
echo [检查] 验证Python安装...
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Python，请先安装Python 3.7+
    echo [提示] 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo [成功] Python已安装

:: 检查必要文件
echo [检查] 验证项目文件...
set "missing_files="
if not exist "start_hybrid.py" set "missing_files=!missing_files! start_hybrid.py"
if not exist "hybrid_gui.py" set "missing_files=!missing_files! hybrid_gui.py"
if not exist "detection_engine.py" set "missing_files=!missing_files! detection_engine.py"
if not exist "hardware_input.ahk" set "missing_files=!missing_files! hardware_input.ahk"
if not exist "hybrid_config.yaml" set "missing_files=!missing_files! hybrid_config.yaml"

if not "!missing_files!"=="" (
    echo [错误] 缺少必要文件：!missing_files!
    echo 请确保所有项目文件都在当前目录中。
    pause
    exit /b 1
)
echo [成功] 所有必要文件存在

:: 检查AutoHotkey
echo [检查] 验证AutoHotkey安装...
if exist "C:\Program Files\AutoHotkey\AutoHotkey.exe" (
    echo [成功] 找到AutoHotkey: C:\Program Files\AutoHotkey\AutoHotkey.exe
) else if exist "C:\Program Files (x86)\AutoHotkey\AutoHotkey.exe" (
    echo [成功] 找到AutoHotkey: C:\Program Files (x86)\AutoHotkey\AutoHotkey.exe
) else (
    echo [警告] 未找到AutoHotkey，请确保已安装
    echo [提示] 下载地址: https://www.autohotkey.com/
    echo [提示] 程序将尝试继续运行...
)

:: 检查依赖包
echo [检查] 验证Python依赖包...
python -c "import cv2, numpy, yaml, win32pipe, win32file, win32gui, tkinter" >nul 2>&1
if errorlevel 1 (
    echo [警告] 部分Python依赖包可能缺失
    echo [提示] 如果启动失败，请运行: pip install opencv-python numpy pyyaml pywin32
    echo [提示] 程序将尝试继续运行...
)

echo.
echo ================================================================
echo                        启动系统
echo ================================================================
echo.

echo [信息] 启动混合架构系统...
echo [提示] 系统将自动管理Python检测端和AHK硬件端的启动顺序
echo [提示] 如果遇到连接问题，请在AHK界面按F9手动启动服务
echo.

:: 启动Python脚本
python start_hybrid.py

if errorlevel 1 (
    echo.
    echo [错误] 系统启动失败
    echo.
    echo 故障排除步骤：
    echo   1. 确认以管理员身份运行
    echo   2. 检查防火墙和杀毒软件设置
    echo   3. 运行 test_pipe_connection.py 测试管道连接
    echo   4. 查看日志文件获取详细错误信息
    echo.
    pause
) else (
    echo.
    echo [成功] 程序已正常退出
)

echo.
echo 按任意键退出...
pause >nul
