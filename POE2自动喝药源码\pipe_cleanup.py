#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管道清理工具
用于清理残留的管道连接和进程
"""

import os
import sys
import subprocess
import psutil
import win32pipe
import win32file
import win32api

def find_python_processes():
    """查找相关的Python进程"""
    print("🔍 查找相关Python进程...")
    
    target_scripts = [
        'detection_engine.py',
        'hybrid_gui.py', 
        'start_hybrid.py',
        'test_pipe_connection.py',
        'quick_pipe_test.py'
    ]
    
    found_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline:
                    cmdline_str = ' '.join(cmdline)
                    for script in target_scripts:
                        if script in cmdline_str:
                            found_processes.append({
                                'pid': proc.info['pid'],
                                'script': script,
                                'cmdline': cmdline_str
                            })
                            break
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return found_processes

def find_ahk_processes():
    """查找AutoHotkey进程"""
    print("🔍 查找AutoHotkey进程...")
    
    found_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'autohotkey' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline:
                    cmdline_str = ' '.join(cmdline)
                    if 'hardware_input.ahk' in cmdline_str:
                        found_processes.append({
                            'pid': proc.info['pid'],
                            'script': 'hardware_input.ahk',
                            'cmdline': cmdline_str
                        })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return found_processes

def test_pipe_status():
    """测试管道状态"""
    print("🔍 测试管道状态...")
    
    pipe_name = r'\\.\pipe\poe2_auto_drink'
    
    # 测试管道是否存在
    try:
        wait_result = win32pipe.WaitNamedPipe(pipe_name, 100)  # 100ms超时
        if wait_result != 0:
            print(f"✅ 管道存在且可用: {pipe_name}")
            
            # 尝试连接
            try:
                pipe_handle = win32file.CreateFile(
                    pipe_name,
                    win32file.GENERIC_READ | win32file.GENERIC_WRITE,
                    0, None,
                    win32file.OPEN_EXISTING,
                    0, None
                )
                
                if pipe_handle != win32file.INVALID_HANDLE_VALUE:
                    print("✅ 管道可以连接")
                    win32file.CloseHandle(pipe_handle)
                else:
                    error_code = win32api.GetLastError()
                    print(f"❌ 管道连接失败: {error_code}")
            except Exception as e:
                print(f"❌ 管道连接异常: {e}")
        else:
            error_code = win32api.GetLastError()
            print(f"❌ 管道不可用: {error_code}")
    except Exception as e:
        print(f"❌ 管道测试异常: {e}")

def kill_processes(processes, process_type):
    """终止进程"""
    if not processes:
        print(f"✅ 没有找到{process_type}进程")
        return
    
    print(f"\n📋 找到{len(processes)}个{process_type}进程:")
    for proc in processes:
        print(f"  PID: {proc['pid']} - {proc['script']}")
    
    choice = input(f"\n是否终止这些{process_type}进程? (y/N): ").strip().lower()
    if choice == 'y':
        for proc in processes:
            try:
                process = psutil.Process(proc['pid'])
                process.terminate()
                print(f"✅ 已终止进程 PID: {proc['pid']}")
                
                # 等待进程结束
                try:
                    process.wait(timeout=5)
                except psutil.TimeoutExpired:
                    print(f"⚠️ 强制终止进程 PID: {proc['pid']}")
                    process.kill()
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                print(f"❌ 无法终止进程 PID: {proc['pid']} - {e}")

def main():
    """主函数"""
    print("POE2 自动喝药 - 管道清理工具")
    print("="*50)
    
    # 检查管道状态
    test_pipe_status()
    print()
    
    # 查找Python进程
    python_processes = find_python_processes()
    kill_processes(python_processes, "Python")
    
    print()
    
    # 查找AHK进程
    ahk_processes = find_ahk_processes()
    kill_processes(ahk_processes, "AutoHotkey")
    
    print()
    
    # 再次检查管道状态
    print("🔄 清理后管道状态:")
    test_pipe_status()
    
    print("\n" + "="*50)
    print("🏁 清理完成")
    print("💡 现在可以重新启动系统")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断")
    except Exception as e:
        print(f"\n❌ 清理工具异常: {e}")
    
    input("\n按回车键退出...")
