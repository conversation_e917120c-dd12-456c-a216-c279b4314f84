# -*- coding: utf-8 -*-
"""
IbInputSimulator Python版本
专注于键盘输入的硬件级别输入模拟库

支持的驱动:
- Logitech GHUB (CVE漏洞利用)
- IbInputSimulator DLL
- Razer Synapse
- DD虚拟键盘
- 系统级别输入

Author: Based on Chaoses-Ib/IbInputSimulator
Version: 1.0.0
"""

import ctypes
import ctypes.wintypes
import time
import logging
import os
from typing import Optional, Dict, Any, Union
from enum import Enum

# 设置日志
logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)

class DriverType(Enum):
    """支持的驱动类型"""
    ANY_DRIVER = 0
    SEND_INPUT = 1
    LOGITECH = 2
    RAZER = 3
    DD = 4
    MOU_CLASS_INPUT_INJECTION = 5
    LOGITECH_GHUB_NEW = 6

class IbInputSimulator:
    """IbInputSimulator主类"""
    
    def __init__(self, driver: Union[str, DriverType] = DriverType.ANY_DRIVER, 
                 dll_path: Optional[str] = None, config: Optional[Dict[str, Any]] = None):
        """
        初始化IbInputSimulator
        
        Args:
            driver: 驱动类型，可以是字符串或DriverType枚举
            dll_path: IbInputSimulator.dll的路径
            config: 配置字典
        """
        self.config = config or {}
        self.driver_type = self._parse_driver_type(driver)
        self.initialized = False
        self.dll = None
        
        # Windows API
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        
        # Logitech相关
        self.logitech_handle = None
        self.logitech_initialized = False
        
        # 初始化
        self._init_driver(dll_path)
    
    def _parse_driver_type(self, driver: Union[str, DriverType]) -> DriverType:
        """解析驱动类型"""
        if isinstance(driver, DriverType):
            return driver
        
        driver_map = {
            'AnyDriver': DriverType.ANY_DRIVER,
            'SendInput': DriverType.SEND_INPUT,
            'Logitech': DriverType.LOGITECH,
            'LogitechGHubNew': DriverType.LOGITECH_GHUB_NEW,
            'Razer': DriverType.RAZER,
            'DD': DriverType.DD,
            'MouClassInputInjection': DriverType.MOU_CLASS_INPUT_INJECTION
        }
        
        return driver_map.get(driver, DriverType.ANY_DRIVER)
    
    def _init_driver(self, dll_path: Optional[str] = None):
        """初始化驱动"""
        try:
            # 尝试加载IbInputSimulator.dll
            if self._load_dll(dll_path):
                self._init_ib_simulator()
            
            # 如果是Logitech驱动，也尝试直接初始化GHUB
            if self.driver_type in [DriverType.LOGITECH, DriverType.LOGITECH_GHUB_NEW]:
                self._init_logitech_ghub()
                
        except Exception as e:
            log.error(f"驱动初始化失败: {e}")
    
    def _load_dll(self, dll_path: Optional[str] = None) -> bool:
        """加载IbInputSimulator.dll"""
        possible_paths = [
            dll_path,
            os.path.join(os.path.dirname(__file__), "IbInputSimulator.dll"),
            r"c:\Users\<USER>\Documents\AutoHotkey\IbInputSimulator\IbInputSimulator.dll",
            r"c:\Users\<USER>\Documents\AutoHotkey\POE2自动喝药源码\IbInputSimulator.dll",
            "IbInputSimulator.dll"
        ]
        
        for path in possible_paths:
            if path is None:
                continue
            try:
                self.dll = ctypes.CDLL(path)
                log.info(f"成功加载IbInputSimulator.dll: {path}")
                return True
            except Exception as e:
                log.debug(f"无法加载 {path}: {e}")
                continue
        
        log.warning("无法找到IbInputSimulator.dll")
        return False
    
    def _init_ib_simulator(self):
        """初始化IbInputSimulator DLL"""
        if not self.dll:
            return
        
        try:
            # 调用IbSendInit
            result = self.dll.IbSendInit(self.driver_type.value, 0, None)
            
            if result == 0:
                self.initialized = True
                log.info(f"IbInputSimulator初始化成功，驱动类型: {self.driver_type.name}")
            else:
                error_messages = [
                    "InvalidArgument",
                    "LibraryNotFound", 
                    "LibraryLoadFailed",
                    "LibraryError",
                    "DeviceCreateFailed",
                    "DeviceNotFound",
                    "DeviceOpenFailed"
                ]
                error_msg = error_messages[result] if result < len(error_messages) else f"Unknown error {result}"
                log.warning(f"IbInputSimulator初始化失败: {error_msg}")
                
        except Exception as e:
            log.error(f"IbInputSimulator初始化异常: {e}")
    
    def _init_logitech_ghub(self):
        """初始化Logitech GHUB直接驱动"""
        try:
            # Logitech GHUB虚拟键盘结构体
            class KEYBOARD_IO(ctypes.Structure):
                _fields_ = [
                    ("Unk1", ctypes.c_uint32),
                    ("Key", ctypes.c_uint8),
                    ("State", ctypes.c_uint8),  # 0=释放, 1=按下
                    ("Unk2", ctypes.c_uint16)
                ]
            
            self.KEYBOARD_IO = KEYBOARD_IO
            
            # 尝试打开Logitech设备
            device_path = r"\\?\HID#VID_046D&PID_C52B&MI_00#7&2f0d1b7e&0&0000#{884b96c3-56ef-11d1-bc8c-00a0c91405dd}"
            
            handle = self.kernel32.CreateFileW(
                device_path,
                0x40000000,  # GENERIC_WRITE
                0x00000003,  # FILE_SHARE_READ | FILE_SHARE_WRITE
                None,
                0x00000003,  # OPEN_EXISTING
                0,
                None
            )
            
            if handle != -1:  # INVALID_HANDLE_VALUE
                self.logitech_handle = handle
                self.logitech_initialized = True
                log.info("Logitech GHUB直接驱动初始化成功")
            else:
                log.debug("Logitech GHUB直接驱动初始化失败")
                
        except Exception as e:
            log.debug(f"Logitech GHUB直接驱动初始化异常: {e}")
    
    def send_key(self, key: str, duration: float = 0.01) -> bool:
        """发送按键
        
        Args:
            key: 按键字符或按键名
            duration: 按键持续时间（秒）
            
        Returns:
            bool: 是否成功发送
        """
        # 按优先级尝试不同方法
        methods = [
            self._send_key_ib_simulator,
            self._send_key_logitech_ghub,
            self._send_key_system
        ]
        
        for method in methods:
            try:
                if method(key, duration):
                    return True
            except Exception as e:
                log.debug(f"发送方法失败: {e}")
                continue
        
        log.error(f"所有方法都无法发送按键: {key}")
        return False
    
    def _send_key_ib_simulator(self, key: str, duration: float) -> bool:
        """使用IbInputSimulator DLL发送按键"""
        if not self.initialized or not self.dll:
            return False
        
        try:
            vk_code = self._char_to_vk(key)
            if vk_code == 0:
                return False
            
            # 发送按键按下
            result1 = self.dll.IbSendKey(vk_code, 1)  # 1=按下
            if result1 != 0:
                return False
            
            time.sleep(duration)
            
            # 发送按键释放
            result2 = self.dll.IbSendKey(vk_code, 0)  # 0=释放
            if result2 != 0:
                return False
            
            log.debug(f"IbInputSimulator发送按键: {key} (VK: {vk_code})")
            return True
            
        except Exception as e:
            log.debug(f"IbInputSimulator发送按键失败: {e}")
            return False
    
    def _send_key_logitech_ghub(self, key: str, duration: float) -> bool:
        """使用Logitech GHUB直接驱动发送按键"""
        if not self.logitech_initialized or not self.logitech_handle:
            return False
        
        try:
            vk_code = self._char_to_vk(key)
            if vk_code == 0:
                return False
            
            # 创建键盘输入结构体
            kb_io = self.KEYBOARD_IO()
            kb_io.Unk1 = 0
            kb_io.Key = vk_code
            kb_io.State = 1  # 按下
            kb_io.Unk2 = 0
            
            # 发送按键按下
            bytes_written = ctypes.wintypes.DWORD()
            result = self.kernel32.WriteFile(
                self.logitech_handle,
                ctypes.byref(kb_io),
                ctypes.sizeof(kb_io),
                ctypes.byref(bytes_written),
                None
            )
            
            if not result:
                return False
            
            time.sleep(duration)
            
            # 发送按键释放
            kb_io.State = 0  # 释放
            result = self.kernel32.WriteFile(
                self.logitech_handle,
                ctypes.byref(kb_io),
                ctypes.sizeof(kb_io),
                ctypes.byref(bytes_written),
                None
            )
            
            log.debug(f"Logitech GHUB发送按键: {key}")
            return bool(result)
            
        except Exception as e:
            log.debug(f"Logitech GHUB发送按键失败: {e}")
            return False
    
    def _send_key_system(self, key: str, duration: float) -> bool:
        """使用系统级别API发送按键"""
        try:
            vk_code = self._char_to_vk(key)
            if vk_code == 0:
                return False
            
            # 使用keybd_event发送按键
            self.user32.keybd_event(vk_code, 0, 0, 0)  # 按下
            time.sleep(duration)
            self.user32.keybd_event(vk_code, 0, 2, 0)  # 释放
            
            log.debug(f"系统级别发送按键: {key}")
            return True
            
        except Exception as e:
            log.debug(f"系统级别发送按键失败: {e}")
            return False
    
    def send_keys(self, keys: str, interval: float = 0.05) -> bool:
        """发送多个按键
        
        Args:
            keys: 按键字符串
            interval: 按键间隔时间（秒）
            
        Returns:
            bool: 是否全部成功发送
        """
        success_count = 0
        for char in keys:
            if self.send_key(char):
                success_count += 1
            time.sleep(interval)
        
        return success_count == len(keys)
    
    def send_special_key(self, key_name: str, duration: float = 0.01) -> bool:
        """发送特殊按键
        
        Args:
            key_name: 特殊按键名称 (如 'enter', 'space', 'tab', 'f1'等)
            duration: 按键持续时间（秒）
            
        Returns:
            bool: 是否成功发送
        """
        return self.send_key(key_name, duration)
    
    def _char_to_vk(self, char: str) -> int:
        """将字符转换为虚拟键码"""
        if len(char) == 1 and char.isalnum():
            return ord(char.upper())
        
        # 特殊键映射
        special_keys = {
            'space': 0x20, ' ': 0x20,
            'enter': 0x0D, '\n': 0x0D,
            'tab': 0x09, '\t': 0x09,
            'shift': 0x10,
            'ctrl': 0x11,
            'alt': 0x12,
            'esc': 0x1B, 'escape': 0x1B,
            'backspace': 0x08,
            'delete': 0x2E, 'del': 0x2E,
            'home': 0x24,
            'end': 0x23,
            'pageup': 0x21, 'pgup': 0x21,
            'pagedown': 0x22, 'pgdn': 0x22,
            'up': 0x26, 'down': 0x28, 'left': 0x25, 'right': 0x27,
            'f1': 0x70, 'f2': 0x71, 'f3': 0x72, 'f4': 0x73,
            'f5': 0x74, 'f6': 0x75, 'f7': 0x76, 'f8': 0x77,
            'f9': 0x78, 'f10': 0x79, 'f11': 0x7A, 'f12': 0x7B,
            '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34, '5': 0x35,
            '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39, '0': 0x30
        }
        
        return special_keys.get(char.lower(), 0)
    
    def is_available(self) -> bool:
        """检查是否有可用的输入方法"""
        return self.initialized or self.logitech_initialized
    
    def get_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        return {
            'driver_type': self.driver_type.name,
            'ib_simulator_initialized': self.initialized,
            'logitech_ghub_initialized': self.logitech_initialized,
            'available': self.is_available()
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.logitech_handle:
                self.kernel32.CloseHandle(self.logitech_handle)
                self.logitech_handle = None
                
            if self.dll and self.initialized:
                try:
                    self.dll.IbSendDestroy()
                except:
                    pass
                self.initialized = False
                
        except Exception as e:
            log.error(f"清理资源时发生错误: {e}")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()

# 便捷函数
def send_key(key: str, driver: str = "AnyDriver", duration: float = 0.01) -> bool:
    """便捷函数：发送单个按键"""
    with IbInputSimulator(driver) as sim:
        return sim.send_key(key, duration)

def send_keys(keys: str, driver: str = "AnyDriver", interval: float = 0.05) -> bool:
    """便捷函数：发送多个按键"""
    with IbInputSimulator(driver) as sim:
        return sim.send_keys(keys, interval)

def send_special_key(key_name: str, driver: str = "AnyDriver", duration: float = 0.01) -> bool:
    """便捷函数：发送特殊按键"""
    with IbInputSimulator(driver) as sim:
        return sim.send_special_key(key_name, duration)

# 测试函数
if __name__ == "__main__":
    # 设置详细日志
    logging.basicConfig(level=logging.DEBUG)
    
    print("IbInputSimulator Python版本测试")
    print("=" * 40)
    
    # 测试不同驱动
    drivers = ["AnyDriver", "Logitech", "SendInput"]
    
    for driver in drivers:
        print(f"\n测试驱动: {driver}")
        try:
            with IbInputSimulator(driver) as sim:
                status = sim.get_status()
                print(f"状态: {status}")
                
                if sim.is_available():
                    print("测试发送按键 '1'...")
                    result = sim.send_key("1")
                    print(f"结果: {result}")
                    
                    time.sleep(1)
                    
                    print("测试发送特殊按键 'space'...")
                    result = sim.send_special_key("space")
                    print(f"结果: {result}")
                else:
                    print("驱动不可用")
                    
        except Exception as e:
            print(f"测试失败: {e}")
    
    print("\n测试完成")