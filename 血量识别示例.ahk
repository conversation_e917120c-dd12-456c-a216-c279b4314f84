; 这是我最推荐的版本
class OptimizedHPDetector {
    __New() {
        this.area := {x: 50, y: 250, w: 200}
        this.lastHP := 100
        this.lastCheck := 0
        this.cacheTime := 50  ; 缓存50ms
    }
    
    GetHP() {
        ; 缓存机制：避免过于频繁的检测
        now := A_TickCount
        if (now - this.lastCheck < this.cacheTime) {
            return this.lastHP
        }
        this.lastCheck := now
        
        ; 智能扫描：根据上次血量决定扫描范围
        scanStart := this.area.x
        scanEnd := this.area.x + this.area.w
        
        ; 如果上次血量在中间，从附近开始扫描
        if (this.lastHP > 10 && this.lastHP < 90) {
            expectedX := this.area.x + (this.lastHP / 100) * this.area.w
            scanStart := Max(this.area.x, expectedX - 30)
            scanEnd := Min(scanEnd, expectedX + 30)
        }
        
        ; 快速扫描
        step := 5  ; 5像素步长
        lastRedX := this.area.x
        
        x := scanStart
        while (x <= scanEnd) {
            if (this.FastIsRed(x, this.area.y)) {
                lastRedX := x
            } else if (lastRedX > this.area.x) {
                ; 已经找到边界，可以停止
                break
            }
            x += step
        }
        
        ; 精确调整
        if (step > 1 && lastRedX > this.area.x) {
            ; 在最后的红色位置附近精确查找
            endX := Min(lastRedX + step, scanEnd)
            Loop endX - lastRedX {
                x := lastRedX + A_Index
                if (this.FastIsRed(x, this.area.y)) {
                    lastRedX := x
                } else {
                    break
                }
            }
        }
        
        hp := (lastRedX - this.area.x) / this.area.w * 100
        this.lastHP := Round(hp, 1)
        return this.lastHP
    }
    
    ; 最快的红色检测
    FastIsRed(x, y) {
        c := PixelGetColor(x, y)
        ; 使用位运算和简单比较
        r := c >> 16
        gb := c & 0xFFFF
        ; 红色分量必须大于100，且大于绿蓝总和
        return r > 100 && r > (gb >> 7)
    }
}

; 全局变量
hp := OptimizedHPDetector()
lastDisplay := 0

; 定时器回调函数
UpdateHP() {
    global hp, lastDisplay
    currentHP := hp.GetHP()
    ; 只在血量变化时更新UI
    if (currentHP != lastDisplay) {
        ToolTip("HP: " currentHP "%")
        lastDisplay := currentHP
    }
}

; 游戏循环中使用
SetTimer(UpdateHP, 10)  ; 10ms检测一次，内部有缓存