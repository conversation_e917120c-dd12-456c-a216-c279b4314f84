# POE2自动喝药系统 - 管道连接故障排除指南

## 问题现状
Python检测端和AutoHotkey硬件输入端之间的命名管道连接失败，错误代码161。

## 修复内容

### 1. 代码修复
- **detection_engine.py**: 修复了配置字段名称不匹配问题，改进了管道创建和连接逻辑
- **hardware_input.ahk**: 增强了管道连接重试机制，添加了详细的错误代码说明
- **hybrid_gui.py**: 优化了启动顺序，确保Python端先完全启动
- **start_hybrid.py**: 简化了启动流程，由GUI统一管理组件启动

### 2. 新增工具
- **test_pipe_connection.py**: 管道连接测试工具
- **启动混合架构_修复版.bat**: 改进的启动脚本，包含完整的环境检查

## 故障排除步骤

### 第一步：环境检查
1. **管理员权限**
   ```
   确保以管理员身份运行所有脚本
   右键点击 -> "以管理员身份运行"
   ```

2. **依赖检查**
   ```bash
   # 检查Python依赖
   pip install opencv-python numpy pyyaml pywin32
   
   # 检查AutoHotkey安装
   确保已安装AutoHotkey v1.1或v2.0
   ```

### 第二步：使用修复版启动
1. 使用 `启动混合架构_修复版.bat` 启动系统
2. 观察启动日志，确认各组件状态
3. 如果仍有问题，继续下一步

### 第三步：管道连接测试
1. 运行管道测试工具：
   ```bash
   python test_pipe_connection.py
   ```
2. 选择测试类型：
   - 选择 "3" 进行完整测试
   - 观察测试结果

### 第四步：手动启动和调试
1. **启动Python端**：
   ```bash
   python hybrid_gui.py
   ```
   - 点击"启动Python端"
   - 观察日志中的管道创建信息

2. **启动AHK端**：
   - 双击 `hardware_input.ahk`
   - 在AHK界面按 F9 启动服务
   - 观察连接状态

### 第五步：检查连接状态
在hybrid_gui.py界面中：
1. 点击"检查连接"按钮
2. 查看详细的连接状态信息
3. 根据提示进行相应操作

## 常见错误代码说明

| 错误代码 | 说明 | 解决方案 |
|---------|------|----------|
| 2 | 管道不存在 | 确保Python端先启动并创建管道 |
| 161 | 管道路径无效 | 检查管道名称配置是否正确 |
| 231 | 管道忙碌 | 等待片刻后重试，或重启系统 |
| 5 | 访问被拒绝 | 以管理员身份运行 |

## 配置文件检查

确保 `hybrid_config.yaml` 中的管道配置正确：
```yaml
hybrid:
  communication:
    pipe_name: "\\\\.\\pipe\\poe2_auto_drink"
    timeout: 1000
    buffer_size: 1024
```

## 防火墙和安全软件
1. **Windows防火墙**：
   - 临时关闭Windows防火墙进行测试
   - 如果解决问题，添加程序例外

2. **杀毒软件**：
   - 将项目文件夹添加到白名单
   - 临时关闭实时保护进行测试

## 系统兼容性
- **Windows版本**：Windows 10/11
- **Python版本**：3.7+
- **AutoHotkey版本**：v1.1或v2.0

## 调试日志
查看以下日志文件获取详细信息：
- `detection_engine.log` - Python检测端日志
- `hybrid_gui.log` - GUI控制界面日志

## 联系支持
如果以上步骤都无法解决问题，请提供：
1. 完整的错误日志
2. 系统环境信息（Windows版本、Python版本等）
3. 管道测试工具的输出结果

## 成功标志
系统正常工作时应该看到：
- Python端：`✅ 命名管道创建成功`、`🎉 AHK端已连接`
- AHK端：`✅ 管道连接成功`、`● 已连接`（绿色状态）
- GUI界面：所有状态显示为绿色的"● 运行中"或"● 已连接"
