# POE2 自动喝药 - 混合架构方案

## 🚀 最高性能的混合架构

本方案结合了 **Python 的强大检测能力** 和 **AutoHotkey 的硬件级按键输入**，实现了最高性能的自动喝药解决方案。

### 🏗️ 架构设计

```
┌─────────────────┐    Named Pipe    ┌─────────────────┐
│   Python 检测端  │ ◄──────────────► │  AHK 硬件输入端  │
│                │                  │                │
│ • 血量/蓝量检测  │                  │ • IbInputSimulator│
│ • 策略判断      │                  │ • 硬件级按键     │
│ • 配置管理      │                  │ • reWASD兼容     │
│ • GUI界面       │                  │ • 冷却时间管理   │
└─────────────────┘                  └─────────────────┘
```

### ✨ 核心优势

1. **🎯 精准检测**: Python + OpenCV 实现像素级精确检测
2. **⚡ 硬件按键**: AHK + IbInputSimulator 实现真正的硬件级输入
3. **🔧 完美兼容**: 支持 reWASD 和各种游戏外设
4. **🚀 极致性能**: 双进程架构，检测和输入完全分离
5. **🎮 游戏友好**: 绕过大部分反作弊检测

## 📁 文件结构

```
POE2自动喝药源码/
├── 🐍 Python 检测端
│   ├── detection_engine.py      # 核心检测引擎
│   ├── hybrid_gui.py           # 统一控制界面
│   └── start_hybrid.py         # 一键启动脚本
│
├── 🔧 AHK 硬件输入端
│   ├── hardware_input.ahk      # 硬件输入管理器
│   └── IbInputSimulator.ahk    # 硬件按键库
│
├── ⚙️ 配置文件
│   ├── hybrid_config.yaml      # 统一配置文件
│   └── hybrid_performance_solution.md  # 架构说明
│
└── 📚 文档
    └── README_混合架构.md       # 本文档
```

## 🛠️ 安装与配置

### 1. 环境准备

#### Python 环境
```bash
# 安装 Python 依赖
pip install opencv-python numpy pyyaml pywin32
```

#### AutoHotkey 环境
- 下载并安装 [AutoHotkey](https://www.autohotkey.com/)
- 确保 `IbInputSimulator.ahk` 库文件存在

### 2. 硬件设备配置

#### Logitech G HUB 设置
1. 安装 Logitech G HUB 软件
2. 连接罗技游戏设备（鼠标/键盘）
3. 确保设备在 G HUB 中被识别

#### reWASD 兼容性
- 如果使用 reWASD，请在配置中启用兼容模式
- 系统会自动处理按键映射冲突

### 3. 游戏设置

#### POE2 游戏内设置
1. **药剂按键绑定**:
   - 血量药剂: `1` 键
   - 蓝量药剂: `2` 键
   - 护盾药剂: `3` 键（可选）

2. **界面设置**:
   - 确保血量/蓝量条清晰可见
   - 建议使用固定分辨率（1920x1080 或 2560x1440）

## 🚀 快速开始

### 方法一：一键启动（推荐）

1. **双击运行** `start_hybrid.py`
2. 系统会自动检查所有依赖项
3. 启动统一控制界面
4. 点击 "启动系统" 开始使用

### 方法二：手动启动

1. **启动 AHK 硬件端**:
   ```
   右键 hardware_input.ahk → 以管理员身份运行
   ```

2. **启动 Python 检测端**:
   ```bash
   python hybrid_gui.py
   ```

3. **在控制界面中点击 "启动系统"**

## ⚙️ 配置说明

### 检测区域配置

```yaml
detection:
  health:
    enable: true
    detection_area:
      x: 100        # 血量条左上角X坐标
      y: 50         # 血量条左上角Y坐标
      width: 200    # 血量条宽度
      height: 20    # 血量条高度
    threshold: 70   # 使用阈值（百分比）
    cooldown: 2.0   # 冷却时间（秒）
```

### 按键映射配置

```yaml
hardware_input:
  key_mapping:
    health:
      potion_key: "1"     # 血量药剂按键
    mana:
      potion_key: "2"     # 蓝量药剂按键
    shield:
      potion_key: "3"     # 护盾药剂按键
```

### 性能优化配置

```yaml
performance:
  detection_frequency: 10    # 检测频率（Hz）
  use_threading: true        # 启用多线程
  pixel_sampling: 5          # 像素采样间隔
  cache_enabled: true        # 启用缓存
```

## 🎮 使用指南

### 基本操作

1. **启动系统**: 点击 "启动系统" 按钮
2. **停止系统**: 点击 "停止系统" 按钮
3. **重启系统**: 点击 "重启系统" 按钮

### 快捷键（AHK端）

- `F9`: 启动硬件输入端
- `F10`: 停止硬件输入端
- `F11`: 测试按键功能

### 状态监控

控制界面实时显示：
- ✅ Python检测端状态
- ✅ AHK硬件端状态
- ✅ 管道连接状态
- ✅ 整体运行状态

### 日志查看

- 实时日志显示在控制界面底部
- 支持保存日志到文件
- 详细记录所有操作和错误信息

## 🔧 高级配置

### 多分辨率支持

```yaml
presets:
  "2K":
    resolution: [2560, 1440]
    health_area: [150, 75, 300, 30]
  "4K":
    resolution: [3840, 2160]
    health_area: [225, 112, 450, 45]
  "1080P":
    resolution: [1920, 1080]
    health_area: [100, 50, 200, 20]
```

### 颜色检测优化

```yaml
detection:
  health:
    color_ranges:
      red_primary:
        h_min: 0
        h_max: 10
        s_min: 120
        v_min: 120
      red_secondary:
        h_min: 170
        h_max: 180
        s_min: 120
        v_min: 120
```

### 硬件兼容性

```yaml
hardware_input:
  ghub_settings:
    driver_type: "LogitechGHubNew"  # 驱动类型
    sync_keys: true                 # 同步按键
    hardware_level: true            # 硬件级输入
  
  rewasd_compatibility:
    enable: true                    # 启用reWASD兼容
    bypass_detection: true          # 绕过检测
    virtual_device: false           # 虚拟设备模式
```

## 🐛 故障排除

### 常见问题

#### 1. Python检测端无法启动
```
❌ 错误: ModuleNotFoundError: No module named 'cv2'
✅ 解决: pip install opencv-python
```

#### 2. AHK硬件端无法启动
```
❌ 错误: 未找到AutoHotkey可执行文件
✅ 解决: 安装AutoHotkey或检查路径
```

#### 3. 管道连接失败
```
❌ 错误: 管道连接超时
✅ 解决: 确保两端都已启动，检查防火墙设置
```

#### 4. 硬件按键无效
```
❌ 错误: IbInputSimulator初始化失败
✅ 解决: 确保以管理员身份运行，检查G HUB连接
```

#### 5. 检测不准确
```
❌ 错误: 血量检测错误
✅ 解决: 重新配置检测区域，调整颜色范围
```

### 调试模式

启用调试模式获取更多信息：

```yaml
debugging:
  enable_logging: true
  log_level: "DEBUG"
  save_screenshots: true
  performance_monitor: true
```

### 性能优化建议

1. **降低检测频率**: 如果CPU占用过高
2. **减少检测区域**: 只检测必要的血量/蓝量条
3. **启用缓存**: 减少重复计算
4. **使用直接像素读取**: 跳过屏幕截图

## 📊 性能对比

| 方案 | 检测精度 | 响应速度 | CPU占用 | 兼容性 | 反检测 |
|------|----------|----------|---------|--------|--------|
| 纯Python | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| 纯AHK | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **混合架构** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

## 🔒 安全说明

### 反作弊兼容性

- ✅ 使用硬件级按键输入
- ✅ 模拟真实玩家操作
- ✅ 支持随机延迟
- ✅ 绕过大部分检测机制

### 使用建议

1. **合理设置冷却时间**: 避免过于频繁的操作
2. **启用随机延迟**: 模拟人类反应时间
3. **定期休息**: 避免长时间连续使用
4. **遵守游戏规则**: 仅用于辅助，不要过度依赖

## 📞 技术支持

### 获取帮助

1. **查看日志**: 控制界面底部的日志区域
2. **检查配置**: 确保所有配置项正确
3. **重启系统**: 尝试重启混合架构系统
4. **更新依赖**: 确保所有库都是最新版本

### 贡献代码

欢迎提交改进建议和代码优化！

---

## 🎉 总结

混合架构方案结合了两种技术的优势：

- **Python**: 强大的图像处理和检测能力
- **AutoHotkey**: 优秀的硬件级输入和系统兼容性

通过命名管道实现高效通信，达到了 **最高性能** 的自动喝药解决方案！

**开始使用**: 双击 `start_hybrid.py` 立即体验！ 🚀