import cv2
import numpy as np
import os

def rgb_to_hsv_manual(r, g, b):
    """手动实现RGB到HSV转换，与AutoHotkey保持一致"""
    r = r / 255.0
    g = g / 255.0
    b = b / 255.0
    
    max_val = max(r, g, b)
    min_val = min(r, g, b)
    delta = max_val - min_val
    
    v = max_val
    
    if max_val == 0:
        s = 0
    else:
        s = delta / max_val
    
    if delta == 0:
        h = 0
    elif max_val == r:
        h = 60 * (((g - b) / delta) + (6 if g < b else 0))
    elif max_val == g:
        h = 60 * (((b - r) / delta) + 2)
    else:
        h = 60 * (((r - g) / delta) + 4)
    
    if h < 0:
        h += 360
    
    return h, s, v

def check_red_color_hsv(r, g, b):
    """HSV红色检测，与AutoHotkey保持一致"""
    total_brightness = r + g + b
    if total_brightness <= 120:
        return False
    
    if r >= 240 and g >= 240 and b >= 240:
        return False
    
    if r <= 15 and g <= 15 and b <= 15:
        return False
    
    h, s, v = rgb_to_hsv_manual(r, g, b)
    
    is_red_hue = (h >= 0 and h <= 30) or (h >= 330 and h <= 360)
    has_enough_saturation = s >= 0.3
    has_enough_brightness = v >= 0.2
    
    is_orange_red = (h >= 30 and h <= 60) and s >= 0.5 and v >= 0.3
    is_dark_red = is_red_hue and s >= 0.4 and v >= 0.15
    
    return (is_red_hue and has_enough_saturation and has_enough_brightness) or is_orange_red or is_dark_red

def check_red_color_rgb_backup(r, g, b):
    """RGB备用检测，与AutoHotkey保持一致"""
    if r >= 240 and g >= 240 and b >= 240:
        return False
    
    if r <= 15 and g <= 15 and b <= 15:
        return False
    
    total_brightness = r + g + b
    if total_brightness <= 120:
        return False
    
    if abs(r - g) <= 8 and abs(r - b) <= 8 and abs(g - b) <= 8 and r <= g + 5:
        return False
    
    red_dominant = (r >= 60 and r > g + 15 and r > b + 15)
    traditional_red = (r >= 80 and r <= 200 and g <= 80 and b <= 80 and r > g + 20 and r > b + 20)
    
    return red_dominant or traditional_red

def check_red_color_combined(r, g, b):
    """组合检测：HSV + RGB备用，与AutoHotkey保持一致"""
    hsv_result = check_red_color_hsv(r, g, b)
    if hsv_result:
        return True
    return check_red_color_rgb_backup(r, g, b)

def simulate_ahk_weighted_algorithm(img_rgb, center_x):
    """模拟AutoHotkey的加权区域检测算法"""
    height = img_rgb.shape[0]
    
    # 模拟AutoHotkey的变量
    red_weighted_count = 0.0
    total_weight = 0.0
    total_height = height
    
    print(f"🔍 模拟AutoHotkey加权算法:")
    print(f"   图片高度: {height}")
    print(f"   扫描中心线: X={center_x}")
    
    # 从上到下扫描，使用加权计算
    for y in range(height):
        # 计算当前像素的权重
        relative_position = y / total_height
        
        # 边缘区域（顶部和底部10%）使用0.3权重，中间区域使用1.0权重
        if relative_position < 0.1 or relative_position > 0.9:
            weight = 0.3  # 边缘权重低，减少装饰像素影响
        else:
            weight = 1.0  # 中间权重正常
        
        total_weight += weight
        
        r, g, b = img_rgb[y, center_x]
        if check_red_color_combined(r, g, b):
            red_weighted_count += weight
    
    # 计算加权血量百分比
    if total_weight <= 0:
        hp = 0.0
    else:
        hp = (red_weighted_count / total_weight) * 100
        hp = max(0, min(100, round(hp, 1)))
    
    print(f"   总权重: {total_weight:.1f}")
    print(f"   红色加权计数: {red_weighted_count:.1f}")
    print(f"   计算血量: {hp:.1f}%")
    
    return hp

def test_ahk_implementation():
    """测试AutoHotkey实现的准确性"""
    print("🧪 AutoHotkey实现验证程序")
    print("=" * 50)
    
    screenshots_folder = "血量截图"
    test_cases = [
        ('100.png', 100),
        ('50.png', 50),
        ('10.png', 10)
    ]
    
    results = []
    
    for filename, actual_hp in test_cases:
        file_path = os.path.join(screenshots_folder, filename)
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            continue
        
        print(f"\n📊 测试 {filename} (实际血量: {actual_hp}%)")
        print("-" * 40)
        
        try:
            # 读取图片
            with open(file_path, 'rb') as f:
                img_data = f.read()
            
            img_array = np.frombuffer(img_data, np.uint8)
            img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
            
            if img is None:
                print(f"❌ 无法解码图片: {file_path}")
                continue
            
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            height, width = img_rgb.shape[:2]
            center_x = width // 2
            
            # 使用模拟的AutoHotkey算法
            calculated_hp = simulate_ahk_weighted_algorithm(img_rgb, center_x)
            error = abs(calculated_hp - actual_hp)
            
            results.append({
                'filename': filename,
                'actual': actual_hp,
                'calculated': calculated_hp,
                'error': error
            })
            
            print(f"✅ 结果: {calculated_hp:.1f}% (误差: {error:.1f}%)")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    # 总结结果
    print(f"\n🏆 AutoHotkey实现验证结果")
    print("=" * 50)
    
    if results:
        total_error = sum(r['error'] for r in results)
        avg_error = total_error / len(results)
        
        print(f"平均误差: {avg_error:.1f}%")
        print(f"测试用例:")
        
        for result in results:
            print(f"   {result['filename']}: {result['actual']}% → {result['calculated']:.1f}% (误差{result['error']:.1f}%)")
        
        # 与Python验证结果对比
        expected_avg_error = 12.7  # 来自之前的验证
        implementation_accuracy = abs(avg_error - expected_avg_error)
        
        print(f"\n📊 实现准确性分析:")
        print(f"   预期平均误差: {expected_avg_error}%")
        print(f"   实际平均误差: {avg_error:.1f}%")
        print(f"   实现偏差: {implementation_accuracy:.1f}%")
        
        if implementation_accuracy <= 2.0:
            print(f"   ✅ AutoHotkey实现非常准确！")
        elif implementation_accuracy <= 5.0:
            print(f"   ✅ AutoHotkey实现基本准确")
        else:
            print(f"   ⚠️ AutoHotkey实现可能需要调整")
        
        print(f"\n💡 建议:")
        if avg_error <= 15:
            print(f"   🎯 算法性能良好，可以投入使用")
        else:
            print(f"   🔧 建议进一步优化算法参数")
        
        print(f"   📈 50%血量检测最准确 (中等血量)")
        print(f"   📉 100%和10%血量可能有装饰像素干扰")
    
    else:
        print("❌ 没有成功的测试结果")

if __name__ == "__main__":
    test_ahk_implementation() 