@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo    POE2 自动喝药 - 混合架构 (UV版本)
echo ========================================
echo.

:: 切换到脚本所在目录
cd /d "%~dp0"

:: 检查 UV 是否安装
echo [检查] 验证 UV 安装状态...
uv --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到 UV 包管理器！
    echo.
    echo 请先安装 UV：
    echo   方法1: powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 ^| iex"
    echo   方法2: pip install uv
    echo   方法3: choco install uv
    echo   方法4: scoop install uv
    echo.
    echo 安装完成后请重新运行此脚本。
    pause
    exit /b 1
)
echo [成功] UV 已安装

:: 检查 pyproject.toml 文件
echo [检查] 验证项目配置文件...
if not exist "pyproject.toml" (
    echo [错误] 未找到 pyproject.toml 配置文件！
    echo 请确保在正确的项目目录中运行此脚本。
    pause
    exit /b 1
)
echo [成功] 项目配置文件存在

:: 检查必要的 Python 文件
echo [检查] 验证项目文件...
set "missing_files="
if not exist "start_hybrid.py" set "missing_files=!missing_files! start_hybrid.py"
if not exist "hybrid_gui.py" set "missing_files=!missing_files! hybrid_gui.py"
if not exist "detection_engine.py" set "missing_files=!missing_files! detection_engine.py"
if not exist "hardware_input.ahk" set "missing_files=!missing_files! hardware_input.ahk"
if not exist "hybrid_config.yaml" set "missing_files=!missing_files! hybrid_config.yaml"

if not "!missing_files!"=="" (
    echo [错误] 缺少必要文件：!missing_files!
    echo 请确保所有项目文件都在当前目录中。
    pause
    exit /b 1
)
echo [成功] 所有必要文件存在

:: 同步依赖
echo.
echo [安装] 同步项目依赖...
uv sync
if errorlevel 1 (
    echo [错误] 依赖安装失败！
    echo.
    echo 可能的解决方案：
    echo   1. 检查网络连接
    echo   2. 运行: uv cache clean
    echo   3. 运行: uv sync --refresh
    echo   4. 以管理员身份运行此脚本
    pause
    exit /b 1
)
echo [成功] 依赖安装完成

:: 启动应用
echo.
echo [启动] 正在启动 POE2 自动喝药程序...
echo.
uv run python start_hybrid.py

:: 检查启动结果
if errorlevel 1 (
    echo.
    echo [错误] 程序启动失败！
    echo.
    echo 故障排除步骤：
    echo   1. 检查是否以管理员身份运行
    echo   2. 确认 AutoHotkey 已安装
    echo   3. 检查防火墙和杀毒软件设置
    echo   4. 查看错误日志获取详细信息
    echo.
    echo 如需帮助，请查看 UV使用指南.md 文件
) else (
    echo.
    echo [成功] 程序已正常退出
)

echo.
echo 按任意键退出...
pause >nul