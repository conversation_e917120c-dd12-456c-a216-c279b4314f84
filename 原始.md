#Requires AutoHotkey v2.0 64-bit
#SingleInstance Force

; ===================================================================
; IbInputSimulator GHUB 硬件按键发送器 + POE2血量检测
; 专门用于通过 Logitech G HUB 驱动发送硬件按键，供 reWASD 识别和重映射
; 集成血量检测，自动喝药和技能释放
; ===================================================================

; 包含 IbInputSimulator 库
#Include "IbInputSimulator\IbInputSimulator.ahk"

; ===================================================================
; 全局变量
; ===================================================================
global ghubInitialized := false
global isRunning := false
global sendInterval := 200    ; 检测间隔 200ms
global hpDetectionInterval := 50  ; 血量检测更频繁的间隔 50ms
global mainGui := ""          ; 主GUI对象

; 血量检测相关变量
global hpDetectionEnabled := false
global lastHP := 100
global skillReleaseActive := false
global lastDrinkTime := 0
global drinkCooldown := 2000  ; 喝药冷却时间 2秒

; 技能按键时序控制
global lastSkillTime := 0
global skillInterval := 800   ; 技能间隔 800ms
global skillHoldTime := 80    ; 技能按下时间 80ms
global skillKeyPressed := false

; POE2窗口检测
global poe2WindowActive := false

; 缓存配置参数，避免每次循环读取GUI
global cachedDrinkKey := "4"
global cachedSkillKey := "5"
global cachedDrinkCooldown := 2000
global cachedSkillInterval := 800
global cachedSkillHoldTime := 80

; 血量检测配置
global hpConfig := {
    x: 1760,        ; 检测区域左上角X
    y: 865,         ; 检测区域左上角Y
    width: 25,      ; 检测区域宽度
    height: 180,    ; 检测区域高度
    scanX: 1772,    ; 扫描中心线X坐标
    topY: 865,      ; 顶部Y坐标
    bottomY: 1045   ; 底部Y坐标
}

; ===================================================================
; 创建GUI界面
; ===================================================================
CreateGUI() {
    ; 创建主窗口
    myGui := Gui("+Resize +MinSize400x500", "POE2自动喝药 + GHUB硬件按键发送器 v2.0")
    myGui.SetFont("s9", "Microsoft YaHei")
    myGui.BackColor := "0xF0F0F0"

    ; 标题
    myGui.AddText("x10 y10 w380 h30 Center", "🎮 POE2自动喝药 + GHUB硬件按键发送器").SetFont("s12 Bold")

    ; 驱动状态
    myGui.AddText("x10 y50 w100", "GHUB驱动:")
    statusLabel := myGui.AddText("x120 y50 w260 cRed", "● 未初始化")

    ; === 血量检测区域 ===
    myGui.AddText("x10 y80 w380 h2 0x10")
    myGui.AddText("x10 y90 w380 h20 Center", "🩸 血量检测与自动控制").SetFont("s11 Bold")

    ; 血量显示
    myGui.AddText("x20 y120 w80 h25", "当前血量:")
    hpText := myGui.AddText("x110 y120 w100 h25 c0x008000 Center Border", "100.0%")
    hpText.SetFont("s12 Bold")

    ; 血量检测开关
    hpEnableChk := myGui.AddCheckbox("x230 y120 w140 h25", "启用血量检测")
    hpEnableChk.OnEvent("Click", ToggleHPDetection)

    ; 自动功能状态
    myGui.AddText("x20 y150 w80", "自动喝药:")
    drinkStatusText := myGui.AddText("x110 y150 w100 c0x666666", "🔴 未启用")
    myGui.AddText("x230 y150 w80", "技能释放:")
    skillStatusText := myGui.AddText("x320 y150 w50 c0x666666", "🔴 停止")

    ; 血量检测区域配置
    myGui.AddText("x20 y180 w25 h20", "X:")
    hpXEdit := myGui.AddEdit("x45 y177 w50 h23 Number", hpConfig.x)
    myGui.AddText("x105 y180 w25 h20", "Y:")
    hpYEdit := myGui.AddEdit("x130 y177 w50 h23 Number", hpConfig.y)
    myGui.AddText("x190 y180 w25 h20", "W:")
    hpWEdit := myGui.AddEdit("x215 y177 w40 h23 Number", hpConfig.width)
    myGui.AddText("x265 y180 w25 h20", "H:")
    hpHEdit := myGui.AddEdit("x290 y177 w40 h23 Number", hpConfig.height)

    ; 血量检测按钮
    drawHPBtn := myGui.AddButton("x20 y205 w80 h25", "🔲 绘制区域")
    drawHPBtn.OnEvent("Click", DrawHPArea)
    testHPBtn := myGui.AddButton("x110 y205 w80 h25", "🧪 测试检测")
    testHPBtn.OnEvent("Click", TestHPDetection)
    applyHPBtn := myGui.AddButton("x200 y205 w80 h25", "✅ 应用设置")
    applyHPBtn.OnEvent("Click", ApplyHPSettings)

    ; === 控制按钮区域 ===
    myGui.AddText("x10 y240 w380 h2 0x10")
    myGui.AddText("x10 y250 w380 h20 Center", "🎮 GHUB控制面板").SetFont("s11 Bold")

    ; 初始化和控制按钮
    initBtn := myGui.AddButton("x20 y280 w110 h35", "🔧 初始化GHUB")
    initBtn.OnEvent("Click", InitializeGHUB)

    testBtn := myGui.AddButton("x140 y280 w110 h35", "🧪 测试按键")
    testBtn.OnEvent("Click", TestKeyInput)

    toggleBtn := myGui.AddButton("x260 y280 w110 h35", "▶️ 启动系统")
    toggleBtn.OnEvent("Click", ToggleSystem)

    ; === 按键设置区域 ===
    myGui.AddText("x10 y325 w380 h20 Center", "⚙️ 按键设置").SetFont("s11 Bold")

    ; 喝药按键设置
    myGui.AddText("x20 y350 w80", "喝药按键:")
    drinkKeyEdit := myGui.AddEdit("x110 y347 w40 h23", cachedDrinkKey)
    myGui.AddText("x160 y350 w80", "技能按键:")
    skillKeyEdit := myGui.AddEdit("x250 y347 w40 h23", cachedSkillKey)
    
    ; 添加说明文字
    myGui.AddText("x20 y375 w350 h15 c0x666666", "💡 使用键盘上方一排的数字键 (1-9,0)，不是小键盘数字键")

    ; 冷却时间设置
    myGui.AddText("x20 y395 w100", "喝药冷却(ms):")
    cooldownEdit := myGui.AddEdit("x130 y392 w60 h23", cachedDrinkCooldown)
    myGui.AddText("x200 y395 w80", "检测间隔:")
    intervalEdit := myGui.AddEdit("x290 y392 w60 h23", sendInterval)
    
    ; 技能时序设置
    myGui.AddText("x20 y420 w100", "技能按下(ms):")
    skillHoldEdit := myGui.AddEdit("x130 y417 w60 h23", cachedSkillHoldTime)
    myGui.AddText("x200 y420 w80", "技能间隔(ms):")
    skillIntervalEdit := myGui.AddEdit("x290 y417 w60 h23", cachedSkillInterval)

    ; === 状态信息区域 ===
    myGui.AddText("x10 y450 w380 h20 Center", "📝 运行日志").SetFont("s11 Bold")
    infoText := myGui.AddEdit("x10 y475 w380 h80 ReadOnly VScroll")

    ; 快捷键说明
    myGui.AddText("x10 y565 w380 h30",
        "快捷键: F1=启动/停止 | F2=测试按键 | F3=初始化GHUB | F4=状态 | F12=退出")

    ; 存储控件引用
    myGui.statusLabel := statusLabel
    myGui.toggleBtn := toggleBtn
    myGui.infoText := infoText
    myGui.hpText := hpText
    myGui.hpEnableChk := hpEnableChk
    myGui.drinkStatusText := drinkStatusText
    myGui.skillStatusText := skillStatusText
    myGui.hpXEdit := hpXEdit
    myGui.hpYEdit := hpYEdit
    myGui.hpWEdit := hpWEdit
    myGui.hpHEdit := hpHEdit
    myGui.drinkKeyEdit := drinkKeyEdit
    myGui.skillKeyEdit := skillKeyEdit
    myGui.cooldownEdit := cooldownEdit
    myGui.intervalEdit := intervalEdit
    myGui.skillHoldEdit := skillHoldEdit
    myGui.skillIntervalEdit := skillIntervalEdit

    ; 设置关闭事件
    myGui.OnEvent("Close", (*) => ExitApp())

    return myGui
}

; ===================================================================
; 初始化 Logitech G HUB 驱动
; ===================================================================
InitializeGHUB(*) {
    global ghubInitialized, mainGui
    
    AddInfo("正在初始化 Logitech G HUB 驱动...")
    
    try {
        ; 使用 LogitechGHubNew 驱动，模式1（接管AHK输入）
        IbSendInit("LogitechGHubNew", 1)
        ghubInitialized := true
        
        ; 更新状态显示
        mainGui.statusLabel.Text := "● Logitech G HUB 已初始化"
        mainGui.statusLabel.Opt("cGreen")
        
        AddInfo("✅ Logitech G HUB 驱动初始化成功！")
        AddInfo("📋 驱动信息: LogitechGHubNew (硬件级输入)")
        AddInfo("🎯 reWASD 现在可以识别并重映射这些按键")
        
        ; 同步按键状态
        try {
            IbSyncKeyStates()
            AddInfo("🔄 按键状态已同步")
        } catch {
            AddInfo("⚠️ 按键状态同步失败（可忽略）")
        }
        
        TrayTip("GHUB 驱动", "Logitech G HUB 驱动初始化成功", 2)
        
    } catch Error as e {
        AddInfo("❌ Logitech G HUB 驱动初始化失败: " . e.Message)
        AddInfo("💡 请确保:")
        AddInfo("   1. 以管理员身份运行此脚本")
        AddInfo("   2. 已安装 Logitech G HUB 软件")
        AddInfo("   3. G HUB 软件正在运行")
        
        MsgBox("Logitech G HUB 驱动初始化失败！`n`n" .
               "错误信息: " . e.Message . "`n`n" .
               "请确保:`n" .
               "1. 以管理员身份运行此脚本`n" .
               "2. 已安装 Logitech G HUB 软件`n" .
               "3. G HUB 软件正在运行", "初始化失败", 16)
    }
}

; ===================================================================
; 测试按键输入
; ===================================================================
TestKeyInput(*) {
    global ghubInitialized, mainGui

    if (!ghubInitialized) {
        MsgBox("请先初始化 Logitech G HUB 驱动！", "错误", 16)
        return
    }

    ; 获取当前配置的按键并转换为主键盘按键
    drinkKey := ConvertToMainKeyboardKey(mainGui.drinkKeyEdit.Text)
    skillKey := ConvertToMainKeyboardKey(mainGui.skillKeyEdit.Text)

    AddInfo("🧪 开始测试按键输入...")
    AddInfo("📝 将在3秒后发送测试按键: " . drinkKey . " 和 " . skillKey . " (主键盘数字键)")
    AddInfo("💡 请切换到记事本或其他文本编辑器查看效果")

    ; 倒计时
    Loop 3 {
        AddInfo("⏰ " . (4-A_Index) . " 秒后开始测试...")
        Sleep(1000)
    }

    try {
        ; 发送测试按键序列
        AddInfo("📤 发送测试序列 (硬件级输入 - 主键盘数字键):")

        ; 测试喝药按键
        AddInfo("  发送喝药按键: " . drinkKey . " (主键盘)")
        IbSend(drinkKey)
        Sleep(500)
        
        ; 测试技能按键
        AddInfo("  发送技能按键: " . skillKey . " (主键盘)")
        IbSend(skillKey)
        Sleep(500)

        AddInfo("✅ 测试完成！如果在目标程序中看到按键输出，说明 GHUB 驱动工作正常")
        AddInfo("🎯 reWASD 应该能够识别并重映射这些主键盘数字键")

    } catch Error as e {
        AddInfo("❌ 测试失败: " . e.Message)
        MsgBox("按键测试失败！`n`n错误信息: " . e.Message, "测试失败", 16)
    }
}

; ===================================================================
; 启动/停止系统
; ===================================================================
ToggleSystem(*) {
    global isRunning, ghubInitialized, mainGui, hpDetectionEnabled, skillReleaseActive
    global sendInterval, hpDetectionInterval
    
    if (!ghubInitialized) {
        MsgBox("请先初始化 Logitech G HUB 驱动！", "错误", 16)
        return
    }
    
    if (!isRunning) {
        ; 启动系统
        isRunning := true
        mainGui.toggleBtn.Text := "⏹️ 停止系统"
        mainGui.toggleBtn.Opt("cRed")

        ; 更新缓存配置
        UpdateCachedConfig()

        AddInfo("▶️ POE2自动系统已启动")
        AddInfo("🩸 血量检测: " . (hpDetectionEnabled ? "启用 (50ms间隔)" : "禁用"))
        AddInfo("💊 自动喝药: 血量 < 75% 时发送按键" . cachedDrinkKey)
        AddInfo("⚔️ 技能释放: 血量 > 30% 时发送按键" . cachedSkillKey)
        AddInfo("🛡️ 技能停止: 血量 ≤ 30% 时停止发送按键" . cachedSkillKey)

        ; 启动血量检测循环 (高频)
        if (hpDetectionEnabled) {
            SetTimer(HPDetectionLoop, hpDetectionInterval)
        }
        
        ; 启动按键操作循环 (正常频率)
        SetTimer(KeyActionLoop, sendInterval)

        TrayTip("系统状态", "POE2自动系统已启动", 1)
        
    } else {
        ; 停止系统
        isRunning := false
        mainGui.toggleBtn.Text := "▶️ 启动系统"
        mainGui.toggleBtn.Opt("cGreen")
        
        ; 停止所有定时器
        SetTimer(HPDetectionLoop, 0)
        SetTimer(KeyActionLoop, 0)
        
        ; 强制释放所有可能按下的按键
        try {
            ; 强制释放喝药和技能按键
            IbSend("{" . cachedDrinkKey . " up}")
            IbSend("{" . cachedSkillKey . " up}")
            AddInfo("🛡️ 系统停止：强制释放所有按键")
        } catch {
            ; 忽略释放错误
        }
        
        ; 重置所有状态
        skillReleaseActive := false
        skillKeyPressed := false
        mainGui.drinkStatusText.Text := hpDetectionEnabled ? "🟡 待命中" : "🔴 未启用"
        if (hpDetectionEnabled) {
            mainGui.drinkStatusText.Opt("c0xFF8000")  ; 橙色
        } else {
            mainGui.drinkStatusText.Opt("cRed")
        }
        mainGui.skillStatusText.Text := "🔴 停止"
        mainGui.skillStatusText.Opt("cRed")
        
        AddInfo("⏹️ POE2自动系统已完全停止")
        TrayTip("系统状态", "POE2自动系统已停止", 1)
    }
}

; ===================================================================
; 更新缓存配置参数
; ===================================================================
UpdateCachedConfig() {
    global cachedDrinkKey, cachedSkillKey, cachedDrinkCooldown
    global cachedSkillInterval, cachedSkillHoldTime, mainGui
    
    try {
        cachedDrinkKey := ConvertToMainKeyboardKey(mainGui.drinkKeyEdit.Text)
        cachedSkillKey := ConvertToMainKeyboardKey(mainGui.skillKeyEdit.Text)
        cachedDrinkCooldown := Integer(mainGui.cooldownEdit.Text)
        cachedSkillInterval := Integer(mainGui.skillIntervalEdit.Text)
        cachedSkillHoldTime := Integer(mainGui.skillHoldEdit.Text)
        
        AddInfo("⚙️ 配置已缓存更新")
    } catch Error as e {
        AddInfo("❌ 配置更新失败: " . e.Message)
    }
}

; ===================================================================
; 血量检测循环 (高频率，仅检测和显示)
; ===================================================================
HPDetectionLoop() {
    global isRunning, hpDetectionEnabled, lastHP
    
    if (!isRunning || !hpDetectionEnabled) {
        return
    }
    
    try {
        ; 检测血量
        currentHP := GetHP()
        lastHP := currentHP
        
        ; 更新血量显示
        UpdateHPDisplay(currentHP)
        
    } catch Error as e {
        AddInfo("❌ 血量检测出错: " . e.Message)
    }
}

; ===================================================================
; 按键操作循环 (正常频率，处理按键逻辑)
; ===================================================================
KeyActionLoop() {
    global isRunning, ghubInitialized, hpDetectionEnabled, lastHP, skillReleaseActive
    global lastDrinkTime, lastSkillTime, skillKeyPressed, mainGui
    global cachedDrinkKey, cachedSkillKey, cachedDrinkCooldown
    global cachedSkillInterval, cachedSkillHoldTime

    if (!isRunning || !ghubInitialized || !hpDetectionEnabled) {
        return
    }

    try {
        ; 检查POE2窗口是否激活
        poe2WindowActive := IsPOE2Active()
        if (!poe2WindowActive) {
            ; POE2窗口未激活，不发送按键
            return
        }
        
        currentTime := A_TickCount
        
        ; 使用最新的血量值进行判断
        currentHP := lastHP
        
        ; 自动喝药逻辑：血量 < 75%
        if (currentHP < 75 && (currentTime - lastDrinkTime) >= cachedDrinkCooldown) {
            ; 按下喝药按键80ms后释放
            IbSend("{" . cachedDrinkKey . " down}")
            lastDrinkTime := currentTime
            mainGui.drinkStatusText.Text := "🟢 已喝药"
            mainGui.drinkStatusText.Opt("cGreen")
            AddInfo("💊 自动喝药: 血量" . currentHP . "% < 75%, 按下按键" . cachedDrinkKey . " (80ms)")
            
            ; 设置80ms后释放按键的定时器
            SetTimer(() => ReleaseDrinkKey(cachedDrinkKey), 80)
            
            ; 2秒后重置状态显示
            SetTimer(ResetDrinkStatus, 2000)
        }
        
        ; 技能释放逻辑
        if (currentHP > 30) {
            ; 血量 > 30%，开始/继续释放技能
            if (!skillReleaseActive) {
                skillReleaseActive := true
                mainGui.skillStatusText.Text := "🟢 释放中"
                mainGui.skillStatusText.Opt("cGreen")
                AddInfo("⚔️ 开始技能释放: 血量" . currentHP . "% > 30%")
            }
            
            ; 技能按键时序控制：按下设定时间，释放，设定间隔
            if (!skillKeyPressed && (currentTime - lastSkillTime) >= cachedSkillInterval) {
                ; 按下技能按键
                IbSend("{" . cachedSkillKey . " down}")
                skillKeyPressed := true
                lastSkillTime := currentTime
                AddInfo("⚔️ 技能按键按下: " . cachedSkillKey . " (" . cachedSkillHoldTime . "ms)")
                
                ; 设置指定时间后释放按键的定时器
                SetTimer(() => ReleaseSkillKey(cachedSkillKey), cachedSkillHoldTime)
            }
            
        } else {
            ; 血量 ≤ 30%，停止释放技能
            if (skillReleaseActive) {
                skillReleaseActive := false
                skillKeyPressed := false
                mainGui.skillStatusText.Text := "🔴 停止"
                mainGui.skillStatusText.Opt("cRed")
                AddInfo("🛡️ 停止技能释放: 血量" . currentHP . "% ≤ 30%")
                
                ; 如果按键还在按下状态，立即释放
                try {
                    IbSend("{" . cachedSkillKey . " up}")
                    AddInfo("🛡️ 强制释放技能按键: " . cachedSkillKey)
                } catch {
                    ; 忽略释放错误
                }
            }
        }

    } catch Error as e {
        ; 出错时停止系统
        ToggleSystem()
        AddInfo("❌ 系统执行出错，已自动停止: " . e.Message)
        MsgBox("系统执行出错！`n`n错误信息: " . e.Message . "`n`n系统已自动停止。", "执行错误", 16)
    }
}

; ===================================================================
; 检测POE2窗口是否激活
; ===================================================================
IsPOE2Active() {
    ; 获取当前激活窗口的标题
    try {
        activeTitle := WinGetTitle("A")
        ; 检测POE2相关的窗口标题
        if (InStr(activeTitle, "Path of Exile 2") || 
            InStr(activeTitle, "POE2") || 
            InStr(activeTitle, "PathOfExile2") ||
            InStr(activeTitle, "Path of Exile II")) {
            return true
        }
    } catch {
        ; 如果获取窗口标题失败，返回false
        return false
    }
    return false
}

; ===================================================================
; 按键转换函数 - 确保发送主键盘数字键
; ===================================================================
ConvertToMainKeyboardKey(key) {
    ; 将数字键转换为主键盘数字键格式
    switch key {
        case "1": return "1"
        case "2": return "2"
        case "3": return "3"
        case "4": return "4"
        case "5": return "5"
        case "6": return "6"
        case "7": return "7"
        case "8": return "8"
        case "9": return "9"
        case "0": return "0"
        default: return key  ; 其他按键保持不变
    }
}

; ===================================================================
; 添加信息到状态显示
; ===================================================================
AddInfo(text) {
    global mainGui

    ; 获取当前时间
    currentTime := FormatTime(, "HH:mm:ss")

    ; 添加时间戳
    newText := "[" . currentTime . "] " . text . "`r`n"
    
    ; 添加到信息框
    mainGui.infoText.Text .= newText
    
    ; 滚动到底部
    mainGui.infoText.Focus()
    Send("^{End}")
}

; ===================================================================
; 显示状态信息
; ===================================================================
ShowStatus(*) {
    global ghubInitialized, isRunning, hpDetectionEnabled, lastHP, skillReleaseActive
    global drinkCooldown, sendInterval, mainGui

    statusText := "🎮 POE2自动喝药 + GHUB硬件按键发送器状态`n`n"
    statusText .= "系统状态:`n"
    statusText .= "• GHUB 驱动: " . (ghubInitialized ? "✅ 已初始化" : "❌ 未初始化") . "`n"
    statusText .= "• 系统运行状态: " . (isRunning ? "🟢 运行中" : "🔴 已停止") . "`n"
    statusText .= "• 血量检测: " . (hpDetectionEnabled ? "✅ 启用" : "❌ 禁用") . "`n"
    statusText .= "• 当前血量: " . lastHP . "%`n"
    statusText .= "• 技能释放: " . (skillReleaseActive ? "🟢 释放中" : "🔴 停止") . "`n`n"

    statusText .= "当前设置:`n"
    statusText .= "• 喝药按键: " . mainGui.drinkKeyEdit.Text . " (主键盘数字键)`n"
    statusText .= "• 技能按键: " . mainGui.skillKeyEdit.Text . " (主键盘数字键)`n"
    statusText .= "• 喝药冷却: " . drinkCooldown . " ms`n"
    statusText .= "• 检测间隔: " . sendInterval . " ms`n"
    statusText .= "• 技能按下: " . mainGui.skillHoldEdit.Text . " ms`n"
    statusText .= "• 技能间隔: " . mainGui.skillIntervalEdit.Text . " ms`n`n"

    statusText .= "自动逻辑:`n"
    statusText .= "• 血量 < 75%: 自动喝药 (按下80ms，释放)`n"
    statusText .= "• 血量 > 30%: 释放技能 (按下" . mainGui.skillHoldEdit.Text . "ms，间隔" . mainGui.skillIntervalEdit.Text . "ms)`n"
    statusText .= "• 血量 ≤ 30%: 停止技能释放`n"
    statusText .= "• 智能检测: 仅在POE2窗口激活时发送按键`n`n"

    statusText .= "快捷键:`n"
    statusText .= "• F1: 启动/停止系统`n"
    statusText .= "• F2: 测试按键输入`n"
    statusText .= "• F3: 重新初始化驱动`n"
    statusText .= "• F4: 显示状态信息`n"
    statusText .= "• F12: 退出程序`n`n"

    statusText .= "reWASD 配置建议:`n"
    statusText .= "• 在 reWASD 中选择 'Logitech Virtual G-series Keyboard'`n"
    statusText .= "• 将主键盘数字键映射为控制器按键`n"
    statusText .= "• 例如: 主键盘 '" . mainGui.drinkKeyEdit.Text . "' → 控制器按键 (喝药)`n"
    statusText .= "• 例如: 主键盘 '" . mainGui.skillKeyEdit.Text . "' → 控制器按键 (技能)`n"
    statusText .= "• 注意: 使用的是键盘上方一排的数字键，不是小键盘数字键`n"

    MsgBox(statusText, "系统状态", 64)
}

; ===================================================================
; 快捷键设置
; ===================================================================
F1::ToggleSystem()
F2::TestKeyInput()
F3::InitializeGHUB()
F4::ShowStatus()        ; 显示状态信息
F12::ExitApp()

; ===================================================================
; 程序入口
; ===================================================================
; 检查管理员权限
if (!A_IsAdmin) {
    MsgBox("此程序需要管理员权限才能正常工作！`n`n请右键点击脚本文件，选择'以管理员身份运行'。", "需要管理员权限", 48)
    ExitApp()
}

; 创建并显示GUI
mainGui := CreateGUI()
mainGui.Show("w400 h605")

; 显示启动信息
AddInfo("🚀 POE2自动喝药 + GHUB硬件按键发送器已启动")
AddInfo("🎮 专为 reWASD 重映射设计")
AddInfo("🩸 集成血量检测，自动喝药和技能释放")
AddInfo("💊 喝药时序: 按下80ms，释放")
AddInfo("⚔️ 技能时序: 按下80ms，释放，间隔800ms")
AddInfo("🎯 智能窗口检测: 仅在POE2激活时发送按键")
AddInfo("💡 请先点击'初始化GHUB'按钮，然后启用血量检测")

; 设置托盘图标和菜单
A_IconTip := "POE2自动喝药 + GHUB硬件按键发送器"

; ===================================================================
; 血量检测核心功能
; ===================================================================

; 检测血量百分比
GetHP() {
    global hpConfig
    
    try {
        scanX := hpConfig.scanX
        topY := hpConfig.topY
        bottomY := hpConfig.bottomY
        
        ; 在垂直线上找红色区域顶部
        redTopY := bottomY  ; 默认空血
        
        ; 从顶部向下扫描
        y := topY
        while (y <= bottomY) {
            if (IsRedColor(scanX, y)) {
                redTopY := y
                break
            }
            y += 2  ; 使用较小步长提高精度
        }
        
        ; 计算血量百分比
        totalHeight := bottomY - topY
        redHeight := bottomY - redTopY
        hp := (redHeight / totalHeight) * 100
        
        return Max(0, Min(100, Round(hp, 1)))
        
    } catch Error as e {
        AddInfo("❌ 血量检测出错: " . e.message)
        return 0
    }
}

; 红色检测
IsRedColor(x, y) {
    c := PixelGetColor(x, y)
    r := (c >> 16) & 0xFF
    g := (c >> 8) & 0xFF
    b := c & 0xFF
    
    return (r >= 80 && r <= 255 && 
            g >= 0 && g <= 100 && 
            b >= 0 && b <= 100 && 
            r > g + 20 && r > b + 20)
}

; 切换血量检测
ToggleHPDetection(*) {
    global hpDetectionEnabled, mainGui, skillReleaseActive
    
    hpDetectionEnabled := mainGui.hpEnableChk.Value
    
    if (hpDetectionEnabled) {
        AddInfo("🩸 血量检测已启用")
        mainGui.drinkStatusText.Text := "🟡 待命中"
        mainGui.drinkStatusText.Opt("c0xFF8000")  ; 橙色
    } else {
        AddInfo("🩸 血量检测已禁用")
        mainGui.drinkStatusText.Text := "🔴 未启用"
        mainGui.drinkStatusText.Opt("cRed")
        mainGui.skillStatusText.Text := "🔴 停止"
        mainGui.skillStatusText.Opt("cRed")
        skillReleaseActive := false
    }
}

; 测试血量检测
TestHPDetection(*) {
    global hpDetectionEnabled
    
    if (!hpDetectionEnabled) {
        MsgBox("请先启用血量检测！", "提示", 48)
        return
    }
    
    AddInfo("🧪 开始测试血量检测...")
    hp := GetHP()
    AddInfo("📊 当前检测到的血量: " . hp . "%")
    
    ; 更新显示
    UpdateHPDisplay(hp)
}

; 更新血量显示
UpdateHPDisplay(hp) {
    global mainGui
    
    ; 更新血量文本和颜色
    if (hp <= 15) {
        mainGui.hpText.Text := Format("⚠️ {:.1f}%", hp)
        mainGui.hpText.Opt("cRed")
    } else if (hp <= 30) {
        mainGui.hpText.Text := Format("⚡ {:.1f}%", hp)
        mainGui.hpText.Opt("c0xFF8000")
    } else if (hp <= 75) {
        mainGui.hpText.Text := Format("💛 {:.1f}%", hp)
        mainGui.hpText.Opt("c0xFF8000")
    } else {
        mainGui.hpText.Text := Format("💚 {:.1f}%", hp)
        mainGui.hpText.Opt("cGreen")
    }
}

; 绘制血量检测区域
DrawHPArea(*) {
    global hpConfig
    
    try {
        ; 创建边框覆盖
        overlayGui := Gui("+AlwaysOnTop +ToolWindow -Caption", "")
        overlayGui.BackColor := "Red"
        
        ; 显示红色边框
        x := hpConfig.x
        y := hpConfig.y
        w := hpConfig.width
        h := hpConfig.height
        
        overlayGui.Show("x" . x . " y" . y . " w" . w . " h3 NoActivate")
        WinSetTransparent(150, overlayGui)
        
        AddInfo("🔲 血量检测区域已绘制，3秒后自动关闭")
        
        ; 3秒后自动关闭
        SetTimer(() => overlayGui.Destroy(), 3000)
        
    } catch Error as e {
        AddInfo("❌ 绘制失败: " . e.message)
    }
}

; 应用血量检测设置
ApplyHPSettings(*) {
    global hpConfig, mainGui
    
    try {
        hpConfig.x := Integer(mainGui.hpXEdit.Text)
        hpConfig.y := Integer(mainGui.hpYEdit.Text)
        hpConfig.width := Integer(mainGui.hpWEdit.Text)
        hpConfig.height := Integer(mainGui.hpHEdit.Text)
        
        ; 重新计算扫描参数
        hpConfig.scanX := hpConfig.x + (hpConfig.width / 2)
        hpConfig.topY := hpConfig.y
        hpConfig.bottomY := hpConfig.y + hpConfig.height
        
        AddInfo("✅ 血量检测设置已更新")
        AddInfo("📍 检测区域: X=" . hpConfig.x . " Y=" . hpConfig.y . " W=" . hpConfig.width . " H=" . hpConfig.height)
        
    } catch Error as e {
        MsgBox("❌ 设置错误: " . e.message, "错误", 16)
    }
}

; 重置喝药状态
ResetDrinkStatus() {
    global hpDetectionEnabled, mainGui
    
    ; 停止定时器
    SetTimer(ResetDrinkStatus, 0)
    
    if (hpDetectionEnabled) {
        mainGui.drinkStatusText.Text := "🟡 待命中"
        mainGui.drinkStatusText.Opt("c0xFF8000")  ; 橙色
    }
}

; 释放技能按键
ReleaseSkillKey(key) {
    global skillKeyPressed, mainGui
    
    if (skillKeyPressed) {
        skillKeyPressed := false
        IbSend("{" . key . " up}")
        AddInfo("⚔️ 技能按键释放: " . key)
    }
}

; 释放喝药按键
ReleaseDrinkKey(key) {
    IbSend("{" . key . " up}")
    AddInfo("💊 喝药按键释放: " . key)
}