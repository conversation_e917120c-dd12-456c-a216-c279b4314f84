; POE2血量球检测器 - 精简版
class HPDetector {
    __New() {
        ; 血量球坐标配置 - 使用精确检测区域
        this.config := {
            x: 1760,        ; 检测区域左上角X
            y: 865,         ; 检测区域左上角Y
            width: 25,      ; 检测区域宽度
            height: 180,    ; 检测区域高度
            scanX: 1760 + 12,  ; 扫描中心线X坐标 (区域中心)
            topY: 865,         ; 顶部Y坐标
            bottomY: 865 + 180 ; 底部Y坐标 (865 + 180 = 1045)
        }
        this.lastHP := 100
        this.isRunning := false
        this.debugMode := false
        this.CreateGUI()
    }
    
    ; 核心检测算法
    GetHP() {
        scanX := this.config.scanX
        topY := this.config.topY
        bottomY := this.config.bottomY
        
        ; 在垂直线上找红色区域顶部
        redTopY := bottomY  ; 默认空血
        
        ; 从顶部向下扫描
        y := topY
        while (y <= bottomY) {
            if (this.IsRedColor(scanX, y)) {
                redTopY := y
                break
            }
            y += 2  ; 使用较小步长提高精度
        }
        
        ; 计算血量百分比
        totalHeight := bottomY - topY
        redHeight := bottomY - redTopY
        hp := (redHeight / totalHeight) * 100
        
        this.lastHP := Max(0, Min(100, Round(hp, 1)))
        return this.lastHP
    }
    
    ; 红色检测
    IsRedColor(x, y) {
        c := PixelGetColor(x, y)
        r := (c >> 16) & 0xFF
        g := (c >> 8) & 0xFF
        b := c & 0xFF
        
        return (r >= 80 && r <= 255 && 
                g >= 0 && g <= 100 && 
                b >= 0 && b <= 100 && 
                r > g + 20 && r > b + 20)
    }
    
    ; 创建简化GUI
    CreateGUI() {
        this.gui := Gui("+Resize", "POE2血量检测器 v4.0 - 优化版")
        this.gui.SetFont("s9", "Microsoft YaHei")
        this.gui.BackColor := "0xF0F0F0"
        
        ; === 状态显示区域 ===
        this.gui.Add("GroupBox", "x10 y10 w360 h90", "🩸 血量状态")
        
        ; 血量显示 - 大号字体
        this.gui.Add("Text", "x25 y35 w80 h25", "当前血量:")
        this.hpText := this.gui.Add("Text", "x110 y35 w100 h25 c0x008000 Center Border", "100.0%")
        this.hpText.SetFont("s14 Bold")
        
        ; 状态和控制
        this.gui.Add("Text", "x25 y65 w60 h20", "运行状态:")
        this.statusText := this.gui.Add("Text", "x90 y65 w120 h20 c0x666666", "未启动")
        
        ; 快速控制按钮
        this.startBtn := this.gui.Add("Button", "x230 y35 w60 h25 c0x008000", "▶ 开始")
        this.stopBtn := this.gui.Add("Button", "x300 y35 w60 h25 c0x800000", "⏸ 停止")
        this.testBtn := this.gui.Add("Button", "x230 y65 w60 h25", "🔍 测试")
        this.debugBtn := this.gui.Add("Button", "x300 y65 w60 h25", "🐛 调试")
        
        ; === 检测区域配置 ===
        this.gui.Add("GroupBox", "x10 y110 w360 h120", "📍 检测区域配置")
        
        ; 区域参数 - 第一行
        this.gui.Add("Text", "x25 y135 w25 h20 Center", "X:")
        this.xEdit := this.gui.Add("Edit", "x50 y132 w55 h23 Number Center", this.config.x)
        this.gui.Add("Text", "x115 y135 w25 h20 Center", "Y:")
        this.yEdit := this.gui.Add("Edit", "x140 y132 w55 h23 Number Center", this.config.y)
        this.gui.Add("Text", "x205 y135 w25 h20 Center", "W:")
        this.wEdit := this.gui.Add("Edit", "x230 y132 w55 h23 Number Center", this.config.width)
        this.gui.Add("Text", "x295 y135 w25 h20 Center", "H:")
        this.hEdit := this.gui.Add("Edit", "x320 y132 w40 h23 Number Center", this.config.height)
        
        ; 扫描参数 - 第二行
        this.gui.Add("Text", "x25 y165 w60 h20", "扫描线X:")
        this.scanXEdit := this.gui.Add("Edit", "x85 y162 w55 h23 Number Center", this.config.scanX)
        this.gui.Add("Text", "x150 y165 w40 h20", "顶部Y:")
        this.topYEdit := this.gui.Add("Edit", "x190 y162 w55 h23 Number Center", this.config.topY)
        this.gui.Add("Text", "x255 y165 w40 h20", "底部Y:")
        this.bottomYEdit := this.gui.Add("Edit", "x295 y162 w55 h23 Number Center", this.config.bottomY)
        
        ; 操作按钮 - 第三行
        this.applyBtn := this.gui.Add("Button", "x25 y195 w80 h25", "✅ 应用设置")
        this.getBtn := this.gui.Add("Button", "x115 y195 w80 h25", "🎯 获取位置")
        this.drawBtn := this.gui.Add("Button", "x205 y195 w80 h25", "🔲 绘制区域")
        this.closeBtn := this.gui.Add("Button", "x295 y195 w65 h25", "❌ 关闭")
        
        ; === 快捷操作 ===
        this.gui.Add("GroupBox", "x10 y240 w360 h60", "⚡ 快捷操作")
        
        ; 预设按钮
        this.gui.Add("Text", "x25 y265 w60 h20", "快速设置:")
        this.preset1Btn := this.gui.Add("Button", "x90 y262 w60 h25", "血量球1")
        this.preset2Btn := this.gui.Add("Button", "x160 y262 w60 h25", "血量球2")
        this.preset3Btn := this.gui.Add("Button", "x230 y262 w60 h25", "蓝量球")
        this.resetBtn := this.gui.Add("Button", "x300 y262 w60 h25", "🔄 重置")
        
        ; === 日志区域 ===
        this.gui.Add("GroupBox", "x10 y310 w360 h120", "📝 运行日志")
        this.logEdit := this.gui.Add("Edit", "x20 y335 w340 h85 ReadOnly VScroll", "")
        
        ; === 热键提示 ===
        this.gui.Add("Text", "x10 y440 w360 h20 c0x666666 Center", "热键: F9=开始 | F10=停止 | F11=查看血量 | ESC=退出")
        
        ; 设置按钮样式
        this.startBtn.SetFont("s9 Bold")
        this.stopBtn.SetFont("s9 Bold")
        this.applyBtn.SetFont("s8 Bold")
        this.drawBtn.SetFont("s8 Bold")
        
        ; 绑定事件
        this.startBtn.OnEvent("Click", (*) => this.Start())
        this.stopBtn.OnEvent("Click", (*) => this.Stop())
        this.testBtn.OnEvent("Click", (*) => this.Test())
        this.debugBtn.OnEvent("Click", (*) => this.Debug())
        this.applyBtn.OnEvent("Click", (*) => this.ApplySettings())
        this.getBtn.OnEvent("Click", (*) => this.GetMousePos())
        this.drawBtn.OnEvent("Click", (*) => this.DrawArea())
        this.closeBtn.OnEvent("Click", (*) => this.CloseOverlay())
        this.preset1Btn.OnEvent("Click", (*) => this.SetPreset1())
        this.preset2Btn.OnEvent("Click", (*) => this.SetPreset2())
        this.preset3Btn.OnEvent("Click", (*) => this.SetPreset3())
        this.resetBtn.OnEvent("Click", (*) => this.ResetToDefault())
        this.gui.OnEvent("Close", (*) => ExitApp())
        
        this.gui.Show("w380 h470")
        this.AddLog("🚀 POE2血量检测器已启动")
        this.AddLog("📍 检测区域: X=" . this.config.x . " Y=" . this.config.y . " W=" . this.config.width . " H=" . this.config.height)
        this.AddLog("💡 血量机制: 满血=全红, 半血=上黑下红, 空血=全黑")
        
        ; 启动GUI更新
        this.updateMethod := ObjBindMethod(this, "UpdateGUI")
        SetTimer(this.updateMethod, 100)
    }
    
    ; 更新GUI
    UpdateGUI() {
        if (!this.gui) 
            return
        
        hp := this.GetHP()
        this.hpText.Text := Format("{:.1f}%", hp)
        
        ; 设置颜色
        if (hp <= 15) {
            this.hpText.SetFont("c0xFF0000")  ; 红色
            this.hpText.Text := Format("⚠️ {:.1f}%", hp)
        } else if (hp <= 30) {
            this.hpText.SetFont("c0xFF8000")  ; 橙色
            this.hpText.Text := Format("⚡ {:.1f}%", hp)
        } else {
            this.hpText.SetFont("c0x008000")  ; 绿色
            this.hpText.Text := Format("💚 {:.1f}%", hp)
        }
        
        this.statusText.Text := this.isRunning ? "🟢 检测中" : "🔴 已停止"
    }
    
    ; 开始检测
    Start() {
        this.isRunning := true
        this.hpMethod := ObjBindMethod(this, "CheckHP")
        SetTimer(this.hpMethod, 50)
        this.AddLog("▶️ 开始血量检测")
    }
    
    ; 停止检测
    Stop() {
        this.isRunning := false
        if (this.hpMethod) {
            SetTimer(this.hpMethod, 0)
            this.hpMethod := ""
        }
        this.AddLog("⏸️ 停止血量检测")
    }
    
    ; 检测循环
    CheckHP() {
        if (!this.isRunning) 
            return
        
        hp := this.GetHP()
        if (hp <= 15) {
            SoundBeep(2000, 100)
            this.AddLog(Format("🚨 危险! 血量: {:.1f}%", hp))
        }
    }
    
    ; 颜色测试
    Test() {
        MouseGetPos(&mx, &my)
        c := PixelGetColor(mx, my)
        r := (c >> 16) & 0xFF
        g := (c >> 8) & 0xFF
        b := c & 0xFF
        isRed := this.IsRedColor(mx, my)
        
        result := Format("位置: ({}, {})`n颜色: R={} G={} B={}`n是否为红色: {}", 
            mx, my, r, g, b, isRed ? "是" : "否")
        MsgBox(result, "颜色测试")
    }
    
    ; 调试信息
    Debug() {
        scanX := this.config.scanX
        topY := this.config.topY
        bottomY := this.config.bottomY
        
        result := "=== 调试信息 ===`n"
        result .= Format("扫描位置: X{} Y{}-{}`n", scanX, topY, bottomY)
        
        ; 测试关键位置
        testPoints := [topY + 20, (topY + bottomY) / 2, bottomY - 20]
        for y in testPoints {
            c := PixelGetColor(scanX, y)
            r := (c >> 16) & 0xFF
            g := (c >> 8) & 0xFF
            b := c & 0xFF
            isRed := this.IsRedColor(scanX, y)
            result .= Format("Y{}: R={} G={} B={} -> {}`n", 
                y, r, g, b, isRed ? "红色" : "黑色")
        }
        
        hp := this.GetHP()
        result .= Format("`n当前血量: {:.1f}%", hp)
        
        MsgBox(result, "调试信息")
    }
    
    ; 应用设置
    ApplySettings() {
        try {
            this.config.x := Integer(this.xEdit.Text)
            this.config.y := Integer(this.yEdit.Text)
            this.config.width := Integer(this.wEdit.Text)
            this.config.height := Integer(this.hEdit.Text)
            this.config.scanX := Integer(this.scanXEdit.Text)
            this.config.topY := Integer(this.topYEdit.Text)
            this.config.bottomY := Integer(this.bottomYEdit.Text)
            
            ; 自动计算扫描线位置（区域中心）
            this.config.scanX := this.config.x + (this.config.width / 2)
            this.scanXEdit.Text := this.config.scanX
            
            this.AddLog("✅ 设置已更新")
            this.AddLog("📍 检测区域: X=" . this.config.x . " Y=" . this.config.y . " W=" . this.config.width . " H=" . this.config.height)
        } catch {
            MsgBox("❌ 设置错误，请检查输入", "错误")
        }
    }
    
    ; 获取鼠标位置
    GetMousePos() {
        MouseGetPos(&mx, &my)
        ; 以鼠标位置为中心设置检测区域
        this.xEdit.Text := mx - 12  ; 区域左边界
        this.yEdit.Text := my - 90  ; 区域上边界
        this.wEdit.Text := 25       ; 固定宽度
        this.hEdit.Text := 180      ; 固定高度
        this.scanXEdit.Text := mx   ; 扫描线在鼠标位置
        this.topYEdit.Text := my - 90
        this.bottomYEdit.Text := my + 90
        this.AddLog(Format("🎯 获取鼠标位置: ({}, {})", mx, my))
    }
    
    ; 绘制检测区域
    DrawArea() {
        try {
            ; 关闭之前的覆盖窗口
            if (this.overlayGui) {
                this.overlayGui.Destroy()
            }
            
            ; 创建边框覆盖
            this.overlayGui := Gui("+AlwaysOnTop +ToolWindow -Caption", "")
            this.overlayGui.BackColor := "Red"
            
            ; 显示红色边框
            borderWidth := 2
            x := this.config.x
            y := this.config.y
            w := this.config.width
            h := this.config.height
            
            ; 创建4个边框
            this.overlayGui.Show("x" . x . " y" . y . " w" . w . " h" . borderWidth . " NoActivate")
            WinSetTransparent(150, this.overlayGui)
            
            this.AddLog("🔲 检测区域已绘制，3秒后自动关闭")
            
            ; 3秒后自动关闭
            this.closeMethod := ObjBindMethod(this, "CloseOverlay")
            SetTimer(this.closeMethod, 3000)
            
        } catch Error as e {
            this.AddLog("❌ 绘制失败: " . e.message)
        }
    }
    
    ; 关闭覆盖窗口
    CloseOverlay() {
        try {
            if (this.overlayGui) {
                this.overlayGui.Destroy()
                this.overlayGui := ""
            }
            if (this.closeMethod) {
                SetTimer(this.closeMethod, 0)
                this.closeMethod := ""
            }
            this.AddLog("🔲 检测区域显示已关闭")
        } catch {
            this.overlayGui := ""
            this.closeMethod := ""
        }
    }
    
    ; 预设1 - 血量球位置1 (左侧血量球)
    SetPreset1() {
        this.xEdit.Text := 150
        this.yEdit.Text := 950
        this.wEdit.Text := 25
        this.hEdit.Text := 180
        this.ApplySettings()
        this.AddLog("🎯 已设置预设1: 左侧血量球")
    }
    
    ; 预设2 - 血量球位置2 (当前设置)
    SetPreset2() {
        this.xEdit.Text := 1760
        this.yEdit.Text := 865
        this.wEdit.Text := 25
        this.hEdit.Text := 180
        this.ApplySettings()
        this.AddLog("🎯 已设置预设2: 右侧血量球")
    }
    
    ; 预设3 - 蓝量球位置
    SetPreset3() {
        this.xEdit.Text := 1710
        this.yEdit.Text := 950
        this.wEdit.Text := 25
        this.hEdit.Text := 180
        this.ApplySettings()
        this.AddLog("🎯 已设置预设3: 蓝量球位置")
    }
    
    ; 重置为默认设置
    ResetToDefault() {
        this.xEdit.Text := this.config.x
        this.yEdit.Text := this.config.y
        this.wEdit.Text := this.config.width
        this.hEdit.Text := this.config.height
        this.ApplySettings()
        this.AddLog("🔄 已重置为默认设置")
    }
    
    ; 添加日志
    AddLog(message) {
        timestamp := FormatTime(A_Now, "HH:mm:ss")
        this.logEdit.Text .= Format("[{}] {}`r`n", timestamp, message)
        this.logEdit.Focus()
        Send("^{End}")
    }
}

; 创建检测器实例
detector := HPDetector()

; 热键
F9::detector.Start()
F10::detector.Stop()
F11::MsgBox("当前血量: " detector.GetHP() "%")
Esc::ExitApp()