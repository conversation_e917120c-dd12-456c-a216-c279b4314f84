import sys
import yaml
import subprocess
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QPushButton, QLabel, QSpinBox, 
                           QComboBox, QGroupBox, QCheckBox, QMessageBox, QFileDialog)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont
import auto_drink
import numpy as np
import keyboard
import winsound
import os

class AutoDrinkThread(QThread):
    """自动喝药线程"""
    def __init__(self):
        super().__init__()
        
    def run(self):
        # 确保config已加载
        if auto_drink.config is None:
            auto_drink.config = auto_drink.load_config()
        auto_drink.main_loop()

class AutoDrinkGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('自动喝药助手')
        self.resize(650, 800)
        self.setMinimumSize(500, 650)
        font = QFont("微软雅黑", 13)
        self.setFont(font)
        # 启动时自动加载last_config.txt记录的配置文件
        self.config_file = self.load_last_config_file()
        self.config = self.load_config_from_file(self.config_file)
        self.running = False
        self.auto_drink_thread = None
        auto_drink.config = self.config
        
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        # 状态显示区域
        status_group = QGroupBox("状态")
        status_layout = QVBoxLayout()
        
        self.health_label = QLabel("血量: 0%")
        self.mana_label = QLabel("蓝量: 0%")
        self.status_label = QLabel("状态: 已停止")
        
        status_layout.addWidget(self.health_label)
        status_layout.addWidget(self.mana_label)
        status_layout.addWidget(self.status_label)
        status_group.setLayout(status_layout)
        
        # 控制按钮区域
        control_group = QGroupBox("控制")
        control_layout = QHBoxLayout()
        
        self.start_button = QPushButton("启动(F5)")
        self.stop_button = QPushButton("停止(F5)")
        self.stop_button.setEnabled(False)
        
        self.start_button.clicked.connect(self.start_auto_drink)
        self.stop_button.clicked.connect(self.stop_auto_drink)
        
        control_layout.addWidget(self.start_button)
        control_layout.addWidget(self.stop_button)
        control_group.setLayout(control_layout)
        
        # 配置区域
        config_group = QGroupBox("配置")
        config_layout = QVBoxLayout()
        
        # 当前配置文件标签
        self.config_file_label = QLabel(f"当前配置文件: {os.path.basename(self.config_file)}")
        config_layout.addWidget(self.config_file_label)
        
        # 血量设置
        health_layout = QHBoxLayout()
        health_layout.addWidget(QLabel("血量按键:"))
        self.health_key = QComboBox()
        self.health_key.addItems(['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'])
        self.health_key.setCurrentText(self.config['health']['potion_key'])
        health_layout.addWidget(self.health_key)
        health_layout.addWidget(QLabel("阈值:"))
        self.health_threshold = QSpinBox()
        self.health_threshold.setRange(1, 99)
        self.health_threshold.setValue(self.config['health']['threshold'])
        health_layout.addWidget(self.health_threshold)
        # 启用自动喝红药
        self.health_enable_checkbox = QCheckBox("启用自动喝红药")
        self.health_enable_checkbox.setChecked(self.config['health'].get('enable', True))
        health_layout.addWidget(self.health_enable_checkbox)
        config_layout.addLayout(health_layout)
        
        # 蓝量设置
        mana_layout = QHBoxLayout()
        mana_layout.addWidget(QLabel("蓝量按键:"))
        self.mana_key = QComboBox()
        self.mana_key.addItems(['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'])
        self.mana_key.setCurrentText(self.config['mana']['potion_key'])
        mana_layout.addWidget(self.mana_key)
        mana_layout.addWidget(QLabel("阈值:"))
        self.mana_threshold = QSpinBox()
        self.mana_threshold.setRange(1, 99)
        self.mana_threshold.setValue(self.config['mana']['threshold'])
        mana_layout.addWidget(self.mana_threshold)
        # 启用自动喝蓝药
        self.mana_enable_checkbox = QCheckBox("启用自动喝蓝药")
        self.mana_enable_checkbox.setChecked(self.config['mana'].get('enable', True))
        mana_layout.addWidget(self.mana_enable_checkbox)
        config_layout.addLayout(mana_layout)
        
        # 护盾设置
        shield_layout = QHBoxLayout()
        shield_layout.addWidget(QLabel("护盾阈值:"))
        self.shield_threshold = QSpinBox()
        self.shield_threshold.setRange(1, 99)
        self.shield_threshold.setValue(self.config.get('shield', {}).get('threshold', 60))
        shield_layout.addWidget(self.shield_threshold)
        # 启用自动喝护盾
        self.shield_enable_checkbox = QCheckBox("启用自动喝护盾")
        self.shield_enable_checkbox.setChecked(self.config.get('shield', {}).get('enable', False))
        shield_layout.addWidget(self.shield_enable_checkbox)
        config_layout.addLayout(shield_layout)
        
        # 直接像素读取选项已删除，改为保存当前配置按钮
        save_current_button = QPushButton("保存当前配置")
        save_current_button.clicked.connect(self.save_config)
        config_layout.addWidget(save_current_button)
        
        # 保存配置按钮
        load_button = QPushButton("加载配置")
        load_button.clicked.connect(self.load_config_file)
        config_layout.addWidget(load_button)
        # 刷新配置按钮
        refresh_button = QPushButton("刷新配置")
        refresh_button.clicked.connect(self.refresh_config_file)
        config_layout.addWidget(refresh_button)
        
        # 添加校准按钮
        calibrate_button = QPushButton("启动校准工具")
        calibrate_button.clicked.connect(self.start_calibrate)
        config_layout.addWidget(calibrate_button)
        
        config_group.setLayout(config_layout)
        
        # 添加所有组件到主布局
        layout.addWidget(status_group)
        layout.addWidget(control_group)
        layout.addWidget(config_group)
        
        # 设置定时器更新状态
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_status)
        self.timer.start(1000)  # 每秒更新一次
        
        # 绑定信号实现自动同步和互斥
        self.health_enable_checkbox.stateChanged.connect(self.on_health_enable_changed)
        self.shield_enable_checkbox.stateChanged.connect(self.on_shield_enable_changed)
        self.health_key.currentIndexChanged.connect(self.sync_config)
        self.health_threshold.valueChanged.connect(self.sync_config)
        self.mana_key.currentIndexChanged.connect(self.sync_config)
        self.mana_threshold.valueChanged.connect(self.sync_config)
        self.mana_enable_checkbox.stateChanged.connect(self.sync_config)
        self.shield_threshold.valueChanged.connect(self.sync_config)
        
        # 注册全局热键
        keyboard.add_hotkey('f5', self.toggle_auto_drink)
        keyboard.add_hotkey('f6', self.hotkey_close)
        
    def start_auto_drink(self):
        self.running = True
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.status_label.setText("状态: 运行中")
        
        # 启动自动喝药线程
        auto_drink.running = True
        auto_drink.exit_program = False
        self.auto_drink_thread = AutoDrinkThread()
        self.auto_drink_thread.start()
        winsound.PlaySound('开启喝药.wav', winsound.SND_FILENAME)
        
    def stop_auto_drink(self):
        self.running = False
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("状态: 已停止")
        
        # 停止自动喝药线程
        auto_drink.running = False
        auto_drink.exit_program = True
        if self.auto_drink_thread and self.auto_drink_thread.isRunning():
            self.auto_drink_thread.wait()
            winsound.PlaySound('关闭喝药.wav', winsound.SND_FILENAME)
        
    def detect_resource_percentage(self, img, resource_type):
        """检测资源百分比，直接调用auto_drink.detect_resource_percentage，保证算法唯一"""
        try:
            auto_drink.config = self.config  # 确保配置同步
            return auto_drink.detect_resource_percentage(img, resource_type)
        except Exception as e:
            print(f"检测{resource_type}时发生错误: {e}")
            return 0
        
    def update_status(self):
        if self.running:
            try:
                if auto_drink.config is None:
                    auto_drink.config = self.config
                # 只显示护盾或血量
                if self.config.get('shield', {}).get('enable', False):
                    value = self.detect_resource_percentage(None, 'shield')
                    self.health_label.setText(f"护盾量: {value:.1f}%")
                else:
                    value = self.detect_resource_percentage(None, 'health')
                    self.health_label.setText(f"血量: {value:.1f}%")
                mana_percent = self.detect_resource_percentage(None, 'mana')
                self.mana_label.setText(f"蓝量: {mana_percent:.1f}%")
            except Exception as e:
                self.health_label.setText("血量: 检测错误")
                self.mana_label.setText("蓝量: 检测错误")
        else:
            self.health_label.setText("血量: --")
            self.mana_label.setText("蓝量: --")
        
    def load_last_config_file(self):
        try:
            with open('last_config.txt', 'r', encoding='utf-8') as f:
                fname = f.read().strip()
                if fname and os.path.exists(fname):
                    return fname
        except Exception:
            pass
        return 'config.yaml'

    def save_last_config_file(self):
        try:
            with open('last_config.txt', 'w', encoding='utf-8') as f:
                f.write(self.config_file)
        except Exception as e:
            pass

    def save_config(self):
        # 更新配置
        self.config['health']['potion_key'] = self.health_key.currentText()
        self.config['health']['threshold'] = self.health_threshold.value()
        self.config['health']['enable'] = self.health_enable_checkbox.isChecked()
        self.config['mana']['potion_key'] = self.mana_key.currentText()
        self.config['mana']['threshold'] = self.mana_threshold.value()
        self.config['mana']['enable'] = self.mana_enable_checkbox.isChecked()
        # 护盾参数
        if 'shield' not in self.config:
            self.config['shield'] = {}
        self.config['shield']['potion_key'] = self.health_key.currentText()
        self.config['shield']['threshold'] = self.shield_threshold.value()
        self.config['shield']['enable'] = self.shield_enable_checkbox.isChecked()
        # 直接覆盖所有配置文件
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, allow_unicode=True)
            auto_drink.config = self.config
            QMessageBox.information(self, "成功", "配置已保存")
        except Exception as e:
            pass

    def refresh_config_file(self):
        if self.config_file and os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f)
                self.save_last_config_file()
                self.refresh_ui_from_config()
                self.config_file_label.setText(f"当前配置文件: {os.path.basename(self.config_file)}")
                QMessageBox.information(self, "成功", f"已刷新配置: {self.config_file}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"刷新配置失败: {e}")
        else:
            QMessageBox.warning(self, "警告", "当前配置文件不存在，无法刷新。")

    def start_calibrate(self):
        """启动校准工具"""
        import sys
        import os
        import subprocess
        if self.auto_drink_thread and self.auto_drink_thread.isRunning():
            self.stop_auto_drink()
        try:
            if getattr(sys, 'frozen', False):
                exe_path = os.path.join(os.path.dirname(sys.executable), 'calibrate_gui.exe')
                subprocess.Popen([exe_path])
            else:
                py_path = os.path.join(os.path.dirname(__file__), 'calibrate_gui.py')
                subprocess.Popen([sys.executable, py_path])
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动校准工具失败: {str(e)}")
            
    def sync_config(self):
        self.config['health']['potion_key'] = self.health_key.currentText()
        self.config['health']['threshold'] = self.health_threshold.value()
        self.config['health']['enable'] = self.health_enable_checkbox.isChecked()
        self.config['mana']['potion_key'] = self.mana_key.currentText()
        self.config['mana']['threshold'] = self.mana_threshold.value()
        self.config['mana']['enable'] = self.mana_enable_checkbox.isChecked()
        # 护盾参数
        if 'shield' not in self.config:
            self.config['shield'] = {}
        self.config['shield']['potion_key'] = self.health_key.currentText()
        self.config['shield']['threshold'] = self.shield_threshold.value()
        self.config['shield']['enable'] = self.shield_enable_checkbox.isChecked()
        auto_drink.config = self.config

    def on_health_enable_changed(self, state):
        if state == Qt.Checked:
            if self.shield_enable_checkbox.isChecked():
                self.shield_enable_checkbox.blockSignals(True)
                self.shield_enable_checkbox.setChecked(False)
                self.shield_enable_checkbox.blockSignals(False)
        self.sync_config()
        self.update_status()

    def on_shield_enable_changed(self, state):
        if state == Qt.Checked:
            if self.health_enable_checkbox.isChecked():
                self.health_enable_checkbox.blockSignals(True)
                self.health_enable_checkbox.setChecked(False)
                self.health_enable_checkbox.blockSignals(False)
        self.sync_config()
        self.update_status()

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.running:
            self.stop_auto_drink()
        event.accept()

    def toggle_auto_drink(self):
        if self.running:
            self.stop_auto_drink()
            # winsound.PlaySound('关闭喝药.wav', winsound.SND_FILENAME)
        else:
            self.start_auto_drink()
            # winsound.PlaySound('开启喝药.wav', winsound.SND_FILENAME)
            
        

    def hotkey_close(self):
        
        self.close()

    def load_config_file(self):
        fname, _ = QFileDialog.getOpenFileName(self, '加载配置文件', '.', 'YAML Files (*.yaml)')
        if fname:
            try:
                with open(fname, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f)
                self.config_file = fname
                self.save_last_config_file()
                self.refresh_ui_from_config()
                self.config_file_label.setText(f"当前配置文件: {os.path.basename(self.config_file)}")
                QMessageBox.information(self, "成功", f"已加载配置: {fname}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"加载配置失败: {e}")

    def refresh_ui_from_config(self):
        # 刷新界面参数控件
        self.health_key.setCurrentText(self.config['health']['potion_key'])
        self.health_threshold.setValue(self.config['health']['threshold'])
        self.health_enable_checkbox.setChecked(self.config['health'].get('enable', True))
        self.mana_key.setCurrentText(self.config['mana']['potion_key'])
        self.mana_threshold.setValue(self.config['mana']['threshold'])
        self.mana_enable_checkbox.setChecked(self.config['mana'].get('enable', True))
        self.shield_threshold.setValue(self.config.get('shield', {}).get('threshold', 60))
        self.shield_enable_checkbox.setChecked(self.config.get('shield', {}).get('enable', False))
        self.config_file_label.setText(f"当前配置文件: {os.path.basename(self.config_file)}")
        self.save_last_config_file()

    def save_config_as(self):
        fname, _ = QFileDialog.getSaveFileName(self, '另存为配置文件', '.', 'YAML Files (*.yaml)')
        if fname:
            try:
                with open(fname, 'w', encoding='utf-8') as f:
                    yaml.dump(self.config, f, allow_unicode=True)
                self.config_file = fname
                self.config_file_label.setText(f"当前配置文件: {os.path.basename(self.config_file)}")
                QMessageBox.information(self, "成功", f"配置已另存为: {fname}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"另存为失败: {e}")

    def load_config_from_file(self, fname):
        with open(fname, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = AutoDrinkGUI()
    window.show()
    sys.exit(app.exec_()) 