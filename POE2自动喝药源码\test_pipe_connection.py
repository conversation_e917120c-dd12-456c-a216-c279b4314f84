#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管道连接测试工具
用于诊断Python端和AHK端之间的管道连接问题
"""

import win32pipe
import win32file
import win32api
import time
import threading
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class PipeConnectionTester:
    """管道连接测试器"""
    
    def __init__(self):
        self.pipe_name = r'\\.\pipe\poe2_auto_drink'
        self.pipe_handle = None
        self.running = False
    
    def test_pipe_server(self):
        """测试管道服务器创建"""
        logger.info("🧪 测试管道服务器创建...")
        
        try:
            # 创建命名管道
            self.pipe_handle = win32pipe.CreateNamedPipe(
                self.pipe_name,
                win32pipe.PIPE_ACCESS_DUPLEX,
                win32pipe.PIPE_TYPE_BYTE | win32pipe.PIPE_WAIT,
                1, 1024, 1024, 0, None
            )
            
            if self.pipe_handle == -1:
                error_code = win32api.GetLastError()
                logger.error(f"❌ 管道创建失败，错误代码: {error_code}")
                return False
            
            logger.info(f"✅ 管道服务器创建成功: {self.pipe_name}")
            
            # 等待连接
            logger.info("📡 等待客户端连接...")
            
            import win32event
            overlapped = win32file.OVERLAPPED()
            overlapped.hEvent = win32event.CreateEvent(None, 0, 0, None)
            
            result = win32pipe.ConnectNamedPipe(self.pipe_handle, overlapped)
            
            if result == 0:  # ERROR_IO_PENDING
                wait_result = win32event.WaitForSingleObject(overlapped.hEvent, 10000)
                if wait_result == win32event.WAIT_TIMEOUT:
                    logger.warning("⏰ 等待连接超时")
                    return False
                elif wait_result == win32event.WAIT_OBJECT_0:
                    logger.info("🎉 客户端已连接")
                    return True
            else:
                logger.info("🎉 客户端已连接")
                return True
                
        except Exception as e:
            logger.error(f"❌ 管道服务器测试失败: {e}")
            return False
        finally:
            if self.pipe_handle:
                try:
                    win32file.CloseHandle(self.pipe_handle)
                except:
                    pass
    
    def test_pipe_client(self):
        """测试管道客户端连接"""
        logger.info("🧪 测试管道客户端连接...")

        try:
            # 等待管道可用
            logger.info("⏳ 等待管道可用...")
            wait_result = win32pipe.WaitNamedPipe(self.pipe_name, 8000)  # 增加等待时间

            # WaitNamedPipe返回非零值表示成功
            if wait_result == 0:
                error_code = win32api.GetLastError()
                logger.error(f"❌ 管道不可用，错误代码: {error_code}")

                # 详细错误说明
                if error_code == 2:
                    logger.error("💡 错误说明: 管道不存在，请确保服务器端已启动")
                elif error_code == 121:
                    logger.error("💡 错误说明: 等待超时，服务器可能未响应")
                else:
                    logger.error(f"💡 错误说明: 未知错误 {error_code}")

                return False

            logger.info("✅ 管道可用，尝试连接...")

            # 连接管道
            pipe_handle = win32file.CreateFile(
                self.pipe_name,
                win32file.GENERIC_READ | win32file.GENERIC_WRITE,
                0, None,
                win32file.OPEN_EXISTING,
                0, None
            )

            # CreateFile返回INVALID_HANDLE_VALUE (-1)表示失败
            if pipe_handle == win32file.INVALID_HANDLE_VALUE:
                error_code = win32api.GetLastError()
                logger.error(f"❌ 管道连接失败，错误代码: {error_code}")

                # 详细错误说明
                if error_code == 2:
                    logger.error("💡 错误说明: 管道不存在")
                elif error_code == 5:
                    logger.error("💡 错误说明: 访问被拒绝，请以管理员身份运行")
                elif error_code == 161:
                    logger.error("💡 错误说明: 管道路径无效")
                elif error_code == 231:
                    logger.error("💡 错误说明: 管道忙碌，请稍后重试")
                else:
                    logger.error(f"💡 错误说明: 未知错误 {error_code}")

                return False

            logger.info("✅ 管道客户端连接成功")

            # 发送测试消息
            test_msg = b"TEST_MESSAGE_FROM_CLIENT"
            try:
                result = win32file.WriteFile(pipe_handle, test_msg)
                if result[0] == 0:
                    logger.info(f"📤 测试消息发送成功: {len(test_msg)} 字节")
                else:
                    logger.error(f"❌ 测试消息发送失败，错误代码: {result[0]}")
            except Exception as e:
                logger.error(f"❌ 发送消息异常: {e}")

            # 关闭连接
            win32file.CloseHandle(pipe_handle)
            return True

        except Exception as e:
            logger.error(f"❌ 管道客户端测试失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def run_server_test(self):
        """运行服务器端测试"""
        logger.info("="*60)
        logger.info("管道连接测试 - 服务器端")
        logger.info("="*60)
        
        success = self.test_pipe_server()
        
        if success:
            logger.info("✅ 管道服务器测试通过")
        else:
            logger.error("❌ 管道服务器测试失败")
        
        return success

    def test_pipe_server_with_sync(self, ready_event):
        """测试管道服务器创建（带同步事件）"""
        logger.info("🧪 测试管道服务器创建（同步版本）...")

        try:
            # 创建命名管道
            self.pipe_handle = win32pipe.CreateNamedPipe(
                self.pipe_name,
                win32pipe.PIPE_ACCESS_DUPLEX,
                win32pipe.PIPE_TYPE_BYTE | win32pipe.PIPE_WAIT,
                1, 1024, 1024, 0, None
            )

            if self.pipe_handle == -1:
                error_code = win32api.GetLastError()
                logger.error(f"❌ 管道创建失败，错误代码: {error_code}")
                return False

            logger.info(f"✅ 管道服务器创建成功: {self.pipe_name}")

            # 通知客户端服务器已准备就绪
            ready_event.set()
            logger.info("📡 服务器已准备就绪，等待客户端连接...")

            import win32event
            overlapped = win32file.OVERLAPPED()
            overlapped.hEvent = win32event.CreateEvent(None, 0, 0, None)

            result = win32pipe.ConnectNamedPipe(self.pipe_handle, overlapped)

            if result == 0:  # ERROR_IO_PENDING
                wait_result = win32event.WaitForSingleObject(overlapped.hEvent, 15000)
                if wait_result == win32event.WAIT_TIMEOUT:
                    logger.warning("⏰ 等待连接超时")
                    return False
                elif wait_result == win32event.WAIT_OBJECT_0:
                    logger.info("🎉 客户端已连接")

                    # 尝试读取客户端消息
                    try:
                        result = win32file.ReadFile(self.pipe_handle, 1024)
                        if result[0] == 0:
                            message = result[1].decode('utf-8', errors='ignore')
                            logger.info(f"📨 收到客户端消息: {message}")
                    except Exception as e:
                        logger.info(f"📨 收到客户端数据: {e}")

                    return True
            else:
                logger.info("🎉 客户端已连接")
                return True

        except Exception as e:
            logger.error(f"❌ 管道服务器测试失败: {e}")
            return False
        finally:
            if self.pipe_handle:
                try:
                    win32file.CloseHandle(self.pipe_handle)
                except:
                    pass

    def run_client_test(self):
        """运行客户端测试"""
        logger.info("="*60)
        logger.info("管道连接测试 - 客户端")
        logger.info("="*60)
        
        success = self.test_pipe_client()
        
        if success:
            logger.info("✅ 管道客户端测试通过")
        else:
            logger.error("❌ 管道客户端测试失败")
        
        return success

def main():
    """主函数"""
    print("POE2 自动喝药 - 管道连接测试工具")
    print("="*60)
    print("1. 服务器端测试（模拟Python端）")
    print("2. 客户端测试（模拟AHK端）")
    print("3. 完整测试（先服务器后客户端）")
    print("="*60)
    
    choice = input("请选择测试类型 (1/2/3): ").strip()
    
    tester = PipeConnectionTester()
    
    if choice == "1":
        tester.run_server_test()
    elif choice == "2":
        tester.run_client_test()
    elif choice == "3":
        # 完整测试
        logger.info("开始完整管道连接测试...")

        # 使用事件同步服务器和客户端
        import threading
        server_ready = threading.Event()

        # 在单独线程中运行服务器
        def server_thread():
            logger.info("🚀 启动服务器线程...")
            success = tester.test_pipe_server_with_sync(server_ready)
            if success:
                logger.info("✅ 管道服务器测试通过")
            else:
                logger.error("❌ 管道服务器测试失败")

        server_t = threading.Thread(target=server_thread, daemon=True)
        server_t.start()

        # 等待服务器准备就绪
        logger.info("⏳ 等待服务器准备就绪...")
        if server_ready.wait(timeout=10):
            logger.info("✅ 服务器已准备就绪，启动客户端...")
            # 稍等一下确保服务器完全准备好
            time.sleep(0.5)
            # 运行客户端测试
            tester.run_client_test()
        else:
            logger.error("❌ 服务器启动超时")

        # 等待服务器线程结束
        server_t.join(timeout=5)
    else:
        print("无效选择")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
