# 多按键循环功能说明

## 🎯 新增功能概述

现在 IbInputSimulator GHUB 硬件按键发送器支持**多按键循环发送**功能！您可以设置多个按键，让程序依次循环发送，或者只发送第一个按键。

## 🔧 功能特性

### 1. 多按键支持
- **按键列表**: 支持设置多个按键，用逗号分隔
- **循环模式**: 可以启用/禁用循环发送
- **灵活配置**: 实时修改按键列表和模式

### 2. 两种工作模式

#### 🔄 循环模式（启用）
- 依次发送列表中的所有按键
- 发送完最后一个按键后，回到第一个按键
- 适合需要多个技能循环的场景

#### 🎯 单按键模式（禁用循环）
- 只发送列表中的第一个按键
- 适合只需要重复一个按键的场景

## 📋 使用方法

### 1. 设置按键列表
在 **"按键列表"** 输入框中输入按键，用逗号分隔：
```
示例 1: 1,2,3,4
示例 2: q,w,e,r
示例 3: F1,F2,F3
示例 4: Space,Tab,Enter
```

### 2. 选择工作模式
- ✅ **勾选"启用多按键循环"**: 循环发送所有按键
- ❌ **取消勾选**: 只发送第一个按键

### 3. 设置时间参数
- **间隔(ms)**: 每次发送按键的间隔时间
- **持续(ms)**: 每个按键的按下持续时间

## 🎮 实际应用示例

### 示例 1: POE2 技能循环
```
按键列表: 1,2,3,4
循环模式: ✅ 启用
间隔: 880ms
持续: 80ms

效果: 依次发送 1→2→3→4→1→2→3→4...
reWASD 映射: 
- 键盘 "1" → 控制器 "RT"
- 键盘 "2" → 控制器 "RB" 
- 键盘 "3" → 控制器 "X"
- 键盘 "4" → 控制器 "Y"
```

### 示例 2: 单技能重复
```
按键列表: 1
循环模式: ❌ 禁用
间隔: 500ms
持续: 50ms

效果: 重复发送 1→1→1→1...
reWASD 映射: 键盘 "1" → 控制器 "RT"
```

### 示例 3: 药水和技能组合
```
按键列表: 1,3,1,1
循环模式: ✅ 启用
间隔: 1000ms
持续: 80ms

效果: 技能→药水→技能→技能→技能→药水...
reWASD 映射:
- 键盘 "1" → 控制器 "RT" (技能)
- 键盘 "3" → 控制器 "X" (药水)
```

## 🔍 状态监控

### 实时日志显示
程序会在状态窗口显示详细信息：
```
🔄 循环发送按键: 2 (第2/4个)
📋 按键列表: 1,2,3,4 | 间隔: 880ms | 持续: 80ms
🔄 循环模式: 启用 (依次发送所有按键)
```

### F4 状态查看
按 F4 可以查看完整状态：
- 当前按键列表和数量
- 循环模式状态
- 时间参数设置
- reWASD 配置建议

## ⚙️ 高级设置

### 动态修改
- 可以在宏运行时修改按键列表
- 修改后立即生效
- 循环索引会自动重置

### 错误处理
- 自动清理空格和空项
- 格式错误时恢复默认值
- 详细的错误提示

## 🎯 reWASD 配置建议

### 多按键映射
为每个按键设置不同的控制器映射：
```
键盘 "1" → 控制器 "RT" (右扳机)
键盘 "2" → 控制器 "RB" (右肩键)
键盘 "3" → 控制器 "X" 按钮
键盘 "4" → 控制器 "Y" 按钮
键盘 "q" → 控制器 "LT" (左扳机)
键盘 "e" → 控制器 "A" 按钮
```

### 时间调优
根据游戏需求调整参数：
- **快速技能**: 间隔 300-500ms
- **普通技能**: 间隔 800-1000ms
- **慢速技能**: 间隔 1500-2000ms

## 🚀 快捷键

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| **F1** | 启动/停止宏 | 开始或停止多按键循环 |
| **F2** | 测试按键 | 测试所有按键是否正常 |
| **F3** | 重新初始化 | 重新初始化 GHUB 驱动 |
| **F4** | 显示状态 | 查看详细状态信息 |
| **F5** | 重置设置 | 恢复默认设置 (1,2) |
| **F12** | 退出程序 | 安全退出 |

## 💡 使用技巧

### 1. 按键命名
支持多种按键格式：
- 数字: `1,2,3,4`
- 字母: `q,w,e,r`
- 功能键: `F1,F2,F3`
- 特殊键: `Space,Tab,Enter,Shift`

### 2. 时间优化
- **间隔时间**: 根据游戏 GCD (全局冷却) 设置
- **持续时间**: 一般 50-100ms 即可
- **测试调优**: 使用 F2 测试最佳参数

### 3. 循环策略
- **技能循环**: 启用循环模式
- **单技能**: 禁用循环模式
- **混合模式**: 在列表中重复某些按键

## ⚠️ 注意事项

1. **按键格式**: 使用英文逗号分隔
2. **空格处理**: 程序会自动清理多余空格
3. **实时生效**: 修改设置后立即生效
4. **索引重置**: 切换模式时循环索引会重置
5. **错误恢复**: 格式错误时自动恢复默认值

---

**更新日期**: 2024年12月  
**版本**: 2.0  
**新功能**: ✅ 多按键循环支持
