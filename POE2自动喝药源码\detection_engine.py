#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
POE2 自动喝药 - 高性能检测引擎
负责血量/蓝量/护盾检测和策略判断
通过命名管道与AHK硬件输入端通信
"""

import cv2
import numpy as np
import yaml
import time
import threading
import win32gui
import win32ui
import win32con
import win32pipe
import win32file
import win32api
import struct
import logging
import queue
from contextlib import contextmanager
from dataclasses import dataclass
from typing import Dict, Optional, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler('detection_engine.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class DetectionResult:
    """检测结果数据类"""
    resource_type: str
    percentage: float
    timestamp: float
    should_use_potion: bool

class PerformanceDetectionEngine:
    """高性能检测引擎"""
    
    def __init__(self, config_file='hybrid_config.yaml'):
        self.config = self.load_config(config_file)
        self.running = False
        self.detection_threads = {}
        self.last_drink_time = {'health': 0, 'mana': 0, 'shield': 0}
        self.pipe_handle = None
        self.command_queue = queue.Queue()
        
        # 性能优化缓存
        self.area_cache = {}
        self.color_cache = {}
        
        logger.info("检测引擎初始化完成")
    
    def load_config(self, config_file: str) -> dict:
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"配置文件加载成功: {config_file}")
            return config
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            # 返回默认配置
            return self.get_default_config()
    
    def get_default_config(self) -> dict:
        """获取默认配置"""
        return {
            'hybrid': {
                'communication': {
                    'pipe_name': r'\\.\pipe\poe2_auto_drink',
                    'timeout': 1000,
                    'buffer_size': 1024
                },
                'performance': {
                    'detection_frequency': 20,
                    'max_threads': 4,
                    'cache_enabled': True
                }
            },
            'detection': {
                'health': {
                    'detection_area': {'x': 1760, 'y': 865, 'width': 25, 'height': 180},
                    'color_lower': [0, 50, 50],
                    'color_upper': [10, 255, 255],
                    'threshold': 75,
                    'enable': True
                },
                'mana': {
                    'detection_area': {'x': 1790, 'y': 865, 'width': 25, 'height': 180},
                    'color_lower': [100, 50, 50],
                    'color_upper': [130, 255, 255],
                    'threshold': 50,
                    'enable': True
                },
                'shield': {
                    'detection_area': {'x': 1820, 'y': 865, 'width': 25, 'height': 180},
                    'color_lower': [200, 50, 50],
                    'color_upper': [220, 255, 255],
                    'threshold': 30,
                    'enable': False
                }
            }
        }
    
    def start(self):
        """启动检测引擎"""
        if self.running:
            logger.warning("检测引擎已在运行")
            return
        
        self.running = True
        logger.info("启动检测引擎...")
        
        # 启动命名管道通信
        self.start_pipe_communication()
        
        # 启动检测线程
        self.start_detection_threads()
        
        logger.info("检测引擎启动完成")
    
    def stop(self):
        """停止检测引擎"""
        if not self.running:
            return
        
        self.running = False
        logger.info("停止检测引擎...")
        
        # 等待线程结束
        for thread in self.detection_threads.values():
            if thread.is_alive():
                thread.join(timeout=1.0)
        
        # 关闭管道
        if self.pipe_handle:
            try:
                win32file.CloseHandle(self.pipe_handle)
            except:
                pass
        
        logger.info("检测引擎已停止")
    
    def start_pipe_communication(self):
        """启动命名管道通信"""
        pipe_name = self.config['hybrid']['communication']['pipe_name']

        def pipe_thread():
            retry_count = 0
            max_retries = 10

            while self.running and retry_count < max_retries:
                try:
                    # 创建命名管道
                    logger.info(f"正在创建命名管道: {pipe_name} (尝试 {retry_count + 1}/{max_retries})")
                    self.pipe_handle = win32pipe.CreateNamedPipe(
                        pipe_name,
                        win32pipe.PIPE_ACCESS_DUPLEX,
                        win32pipe.PIPE_TYPE_BYTE | win32pipe.PIPE_WAIT,
                        1, 1024, 1024, 0, None
                    )

                    if self.pipe_handle == -1:
                        error_code = win32api.GetLastError()
                        logger.error(f"创建命名管道失败，错误代码: {error_code}")
                        retry_count += 1
                        time.sleep(2)
                        continue

                    logger.info(f"✅ 命名管道创建成功: {pipe_name}")
                    logger.info(f"📡 等待AHK端连接管道...")

                    # 设置超时等待连接
                    import win32event
                    overlapped = win32file.OVERLAPPED()
                    overlapped.hEvent = win32event.CreateEvent(None, 0, 0, None)

                    result = win32pipe.ConnectNamedPipe(self.pipe_handle, overlapped)

                    if result == 0:  # ERROR_IO_PENDING
                        # 等待连接，设置30秒超时
                        logger.info("📡 管道服务器准备就绪，等待AHK端连接...")
                        wait_result = win32event.WaitForSingleObject(overlapped.hEvent, 30000)
                        if wait_result == win32event.WAIT_TIMEOUT:
                            logger.warning("⏰ 等待AHK连接超时，重新创建管道")
                            win32file.CloseHandle(self.pipe_handle)
                            win32event.CloseHandle(overlapped.hEvent)
                            self.pipe_handle = None
                            retry_count += 1
                            time.sleep(2)
                            continue
                        elif wait_result == win32event.WAIT_OBJECT_0:
                            logger.info("🎉 AHK端已连接")
                        else:
                            logger.error(f"等待连接失败，错误代码: {wait_result}")
                            win32file.CloseHandle(self.pipe_handle)
                            win32event.CloseHandle(overlapped.hEvent)
                            self.pipe_handle = None
                            retry_count += 1
                            time.sleep(2)
                            continue
                    else:
                        # 立即连接成功
                        error_code = win32api.GetLastError()
                        if error_code == 535:  # ERROR_PIPE_CONNECTED
                            logger.info("🎉 AHK端已连接")
                        else:
                            logger.info("🎉 AHK端已连接")

                    # 清理事件句柄
                    if 'overlapped' in locals():
                        win32event.CloseHandle(overlapped.hEvent)

                    # 重置重试计数
                    retry_count = 0

                    # 处理通信
                    self.handle_pipe_communication()

                except Exception as e:
                    logger.error(f"管道通信错误: {e}")
                    if hasattr(self, 'pipe_handle') and self.pipe_handle:
                        try:
                            win32file.CloseHandle(self.pipe_handle)
                        except:
                            pass
                        self.pipe_handle = None
                    retry_count += 1
                    time.sleep(2)

            if retry_count >= max_retries:
                logger.error("❌ 管道连接重试次数已达上限，停止尝试")

        thread = threading.Thread(target=pipe_thread, daemon=True)
        thread.start()
    
    def handle_pipe_communication(self):
        """处理管道通信"""
        while self.running:
            try:
                # 发送待处理的命令
                while not self.command_queue.empty():
                    command = self.command_queue.get_nowait()
                    self.send_command(command)
                
                # 接收AHK端的响应
                try:
                    result = win32file.ReadFile(self.pipe_handle, 64)
                    if result[0] == 0:  # 成功读取
                        self.process_ahk_response(result[1])
                except:
                    pass  # 非阻塞读取，忽略超时
                
                time.sleep(0.001)  # 1ms间隔
                
            except Exception as e:
                logger.error(f"管道处理错误: {e}")
                break
    
    def send_command(self, command: dict):
        """发送命令到AHK端"""
        try:
            # 构建二进制命令包
            cmd_id = command.get('id', 0x01)
            resource_type = command.get('resource_type', 'health')
            value = command.get('value', 0)
            
            # 资源类型映射
            resource_map = {'health': 0x01, 'mana': 0x02, 'shield': 0x03}
            resource_byte = resource_map.get(resource_type, 0x01)
            
            # 打包数据
            data = struct.pack('<BBfH', cmd_id, resource_byte, value, 0x1234)  # 简单校验和
            
            win32file.WriteFile(self.pipe_handle, data)
            
        except Exception as e:
            logger.error(f"发送命令失败: {e}")
    
    def process_ahk_response(self, data: bytes):
        """处理AHK端响应"""
        try:
            if len(data) >= 4:
                status = struct.unpack('<I', data[:4])[0]
                if status == 0x01:  # 成功
                    logger.debug("AHK端命令执行成功")
        except Exception as e:
            logger.error(f"处理AHK响应失败: {e}")
    
    def start_detection_threads(self):
        """启动检测线程"""
        detection_config = self.config['detection']
        
        for resource_type, config in detection_config.items():
            if config.get('enable', False):
                thread = threading.Thread(
                    target=self.detection_loop,
                    args=(resource_type,),
                    daemon=True
                )
                thread.start()
                self.detection_threads[resource_type] = thread
                logger.info(f"启动{resource_type}检测线程")
    
    def detection_loop(self, resource_type: str):
        """检测循环"""
        frequency = self.config['hybrid']['performance']['detection_frequency']
        interval = 1.0 / frequency
        
        logger.info(f"{resource_type}检测循环启动，频率: {frequency}Hz")
        
        while self.running:
            try:
                start_time = time.time()
                
                # 执行检测
                result = self.detect_resource(resource_type)
                
                # 判断是否需要使用药剂
                if result.should_use_potion:
                    self.request_potion_use(resource_type, result.percentage)
                
                # 控制检测频率
                elapsed = time.time() - start_time
                sleep_time = max(0, interval - elapsed)
                if sleep_time > 0:
                    time.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"{resource_type}检测错误: {e}")
                time.sleep(0.1)
    
    def detect_resource(self, resource_type: str) -> DetectionResult:
        """检测资源百分比"""
        config = self.config['detection'][resource_type]
        area = config['detection_area']
        
        # 使用直接像素读取获取最高性能
        percentage = self.fast_detect_percentage(area, config)
        
        # 判断是否需要使用药剂
        threshold = config['threshold']
        current_time = time.time()
        last_drink = self.last_drink_time[resource_type]
        
        # 冷却时间检查（从配置获取，默认2秒）
        cooldown = config.get('cooldown', 2.0)
        should_use = (
            percentage < threshold and 
            (current_time - last_drink) >= cooldown
        )
        
        return DetectionResult(
            resource_type=resource_type,
            percentage=percentage,
            timestamp=current_time,
            should_use_potion=should_use
        )
    
    def fast_detect_percentage(self, area: dict, config: dict) -> float:
        """快速检测百分比 - 直接像素读取"""
        x, y, w, h = area['x'], area['y'], area['width'], area['height']
        
        # 获取桌面DC
        hdc = win32gui.GetDC(0)
        
        try:
            # 从底部向上扫描，找到最大连续有效行数
            valid_lines = 0
            total_lines = h
            
            for row in range(h):
                scan_y = y + h - 1 - row  # 从底部开始
                line_valid = False
                
                # 检查这一行是否有有效颜色
                for scan_x in range(x, x + w, 2):  # 每2像素采样一次，提高性能
                    color = win32gui.GetPixel(hdc, scan_x, scan_y)
                    if self.is_valid_color(color, config):
                        line_valid = True
                        break
                
                if line_valid:
                    valid_lines += 1
                else:
                    break  # 遇到无效行就停止
            
            percentage = (valid_lines / total_lines) * 100
            return min(100.0, max(0.0, percentage))
            
        finally:
            win32gui.ReleaseDC(0, hdc)
    
    def is_valid_color(self, color: int, config: dict) -> bool:
        """检查颜色是否有效"""
        # 提取RGB分量
        r = color & 0xFF
        g = (color >> 8) & 0xFF
        b = (color >> 16) & 0xFF

        # 快速排除明显无效的颜色
        if r < 20 and g < 20 and b < 20:  # 太暗
            return False
        if r > 240 and g > 240 and b > 240:  # 太亮
            return False

        # 转换为HSV进行精确检测
        hsv = self.rgb_to_hsv(r, g, b)

        # 修复配置字段名称匹配问题
        if 'color_range' in config:
            lower = config['color_range']['lower']
            upper = config['color_range']['upper']
        else:
            # 兼容旧配置格式
            lower = config.get('color_lower', [0, 50, 50])
            upper = config.get('color_upper', [10, 255, 255])

        # HSV范围检查
        h_valid = lower[0] <= hsv[0] <= upper[0]
        s_valid = lower[1] <= hsv[1] <= upper[1]
        v_valid = lower[2] <= hsv[2] <= upper[2]

        return h_valid and s_valid and v_valid
    
    def rgb_to_hsv(self, r: int, g: int, b: int) -> Tuple[float, float, float]:
        """RGB转HSV"""
        r, g, b = r/255.0, g/255.0, b/255.0
        
        max_val = max(r, g, b)
        min_val = min(r, g, b)
        diff = max_val - min_val
        
        # Value
        v = max_val * 255
        
        # Saturation
        s = 0 if max_val == 0 else (diff / max_val) * 255
        
        # Hue
        if diff == 0:
            h = 0
        elif max_val == r:
            h = (60 * ((g - b) / diff) + 360) % 360
        elif max_val == g:
            h = (60 * ((b - r) / diff) + 120) % 360
        else:
            h = (60 * ((r - g) / diff) + 240) % 360
        
        return (h, s, v)
    
    def request_potion_use(self, resource_type: str, percentage: float):
        """请求使用药剂"""
        command = {
            'id': 0x01,  # 使用药剂命令
            'resource_type': resource_type,
            'value': percentage
        }
        
        self.command_queue.put(command)
        self.last_drink_time[resource_type] = time.time()
        
        logger.info(f"请求使用{resource_type}药剂，当前百分比: {percentage:.1f}%")
    
    def get_status(self) -> dict:
        """获取当前状态"""
        status = {
            'running': self.running,
            'pipe_connected': self.pipe_handle is not None,
            'active_threads': len([t for t in self.detection_threads.values() if t.is_alive()]),
            'last_drink_time': self.last_drink_time.copy()
        }
        return status

def main():
    """主函数"""
    engine = PerformanceDetectionEngine()
    
    try:
        engine.start()
        
        # 保持运行
        while True:
            time.sleep(1)
            status = engine.get_status()
            logger.info(f"引擎状态: {status}")
            
    except KeyboardInterrupt:
        logger.info("收到停止信号")
    finally:
        engine.stop()

if __name__ == '__main__':
    main()