; 统一的主循环 - 避免多个定时器冲突
MainLoop() {
    global isRunning, hpDetectionEnabled, lastHP
    static frameCounter := 0
    static lastGUIUpdate := 0
    static lastHPDetection := 0
    static lastKeyAction := 0
    
    if (!isRunning) {
        return
    }
    
    currentTime := A_TickCount
    frameCounter++
    
    ; 血量检测（每100ms一次）
    if (hpDetectionEnabled && (currentTime - lastHPDetection) >= 100) {
        try {
            newHP := GetHP()
            if (Abs(newHP - lastHP) > 0.5) {  ; 只有变化超过0.5%才更新
                lastHP := newHP
            }
            lastHPDetection := currentTime
        } catch Error as e {
            AddInfo("❌ 血量检测失败: " . e.message)
        }
    }
    
    ; 按键操作（每200ms一次）
    if (hpDetectionEnabled && (currentTime - lastKeyAction) >= 200) {
        ProcessKeyActions()
        lastKeyAction := currentTime
    }
    
    ; GUI更新（每500ms一次，减少性能消耗）
    if ((currentTime - lastGUIUpdate) >= 500) {
        UpdateHPDisplay(lastHP)
        lastGUIUpdate := currentTime
    }
    
    ; 每10秒输出一次心跳信息（可选）
    if (Mod(frameCounter, 200) == 0) {  ; 50ms * 200 = 10秒
        AddInfo("💓 系统运行正常 - 血量: " . lastHP . "%")
    }
}

; 独立的按键处理逻辑
ProcessKeyActions() {
    global lastHP, skillReleaseActive, lastDrinkTime, lastSkillTime
    global cachedDrinkKey, cachedSkillKey, cachedDrinkCooldown
    global cachedSkillInterval, cachedSkillHoldTime
    
    if (!IsPOE2Active()) {
        return  ; 游戏窗口未激活时不执行任何操作
    }
    
    currentTime := A_TickCount
    
    ; 使用状态机管理按键操作
    static drinkState := "idle"  ; idle, pressing, cooldown
    static skillState := "idle"  ; idle, pressing, waiting
    static stateChangeTime := 0
    
    ; 喝药状态机
    switch drinkState {
        case "idle":
            if (lastHP < 75 && (currentTime - lastDrinkTime) >= cachedDrinkCooldown) {
                ExecuteDrinkAction()
                drinkState := "pressing"
                stateChangeTime := currentTime
            }
        case "pressing":
            if ((currentTime - stateChangeTime) >= 80) {  ; 按下80ms后释放
                ReleaseDrinkKey()
                drinkState := "cooldown"
                stateChangeTime := currentTime
            }
        case "cooldown":
            if ((currentTime - stateChangeTime) >= 1000) {  ; 冷却1秒后回到idle
                drinkState := "idle"
            }
    }
    
    ; 技能状态机
    switch skillState {
        case "idle":
            if (lastHP > 30 && !skillReleaseActive) {
                skillReleaseActive := true
                ExecuteSkillAction()
                skillState := "pressing"
                stateChangeTime := currentTime
            } else if (lastHP <= 30 && skillReleaseActive) {
                skillReleaseActive := false
                skillState := "idle"
                UpdateStatusDisplay()
            }
        case "pressing":
            if ((currentTime - stateChangeTime) >= cachedSkillHoldTime) {
                ReleaseSkillKey()
                skillState := "waiting"
                stateChangeTime := currentTime
            }
        case "waiting":
            if (lastHP <= 30) {  ; 血量过低，停止技能
                skillReleaseActive := false
                skillState := "idle"
            } else if ((currentTime - stateChangeTime) >= cachedSkillInterval) {
                ExecuteSkillAction()
                skillState := "pressing"
                stateChangeTime := currentTime
            }
    }
}

; 执行喝药操作
ExecuteDrinkAction() {
    global cachedDrinkKey, lastDrinkTime, mainGui
    
    try {
        IbSend("{" . cachedDrinkKey . " down}")
        lastDrinkTime := A_TickCount
        mainGui.drinkStatusText.Text := "🟢 已喝药"
        mainGui.drinkStatusText.Opt("cGreen")
        AddInfo("💊 自动喝药: 血量" . lastHP . "% < 75%")
    } catch Error as e {
        AddInfo("❌ 喝药失败: " . e.message)
    }
}

; 执行技能操作
ExecuteSkillAction() {
    global cachedSkillKey, lastSkillTime, mainGui
    
    try {
        IbSend("{" . cachedSkillKey . " down}")
        lastSkillTime := A_TickCount
        mainGui.skillStatusText.Text := "🟢 释放中"
        mainGui.skillStatusText.Opt("cGreen")
        AddInfo("⚔️ 技能释放: " . cachedSkillKey)
    } catch Error as e {
        AddInfo("❌ 技能释放失败: " . e.message)
    }
}



; 配置文件管理系统
class ConfigManager {
    static configFile := A_ScriptDir . "\config.ini"
    
    ; 默认配置
    static defaultConfig := {
        ; 血量检测配置
        hp_x: 1760,
        hp_y: 865,
        hp_width: 25,
        hp_height: 180,
        hp_enabled: false,
        
        ; 按键配置
        drink_key: "4",
        skill_key: "5",
        
        ; 时序配置
        drink_cooldown: 2000,
        skill_interval: 800,
        skill_hold_time: 80,
        detection_interval: 200,
        
        ; 血量阈值
        drink_threshold: 75,
        skill_stop_threshold: 30,
        
        ; 显示配置
        gui_update_interval: 500,
        log_max_lines: 100
    }
    
    ; 加载配置
    static LoadConfig() {
        config := this.defaultConfig.Clone()
        
        if (!FileExist(this.configFile)) {
            this.SaveConfig(config)
            return config
        }
        
        try {
            ; 读取血量检测配置
            config.hp_x := IniRead(this.configFile, "BloodDetection", "x", config.hp_x)
            config.hp_y := IniRead(this.configFile, "BloodDetection", "y", config.hp_y)
            config.hp_width := IniRead(this.configFile, "BloodDetection", "width", config.hp_width)
            config.hp_height := IniRead(this.configFile, "BloodDetection", "height", config.hp_height)
            config.hp_enabled := IniRead(this.configFile, "BloodDetection", "enabled", config.hp_enabled)
            
            ; 读取按键配置
            config.drink_key := IniRead(this.configFile, "Keys", "drink_key", config.drink_key)
            config.skill_key := IniRead(this.configFile, "Keys", "skill_key", config.skill_key)
            
            ; 读取时序配置
            config.drink_cooldown := IniRead(this.configFile, "Timing", "drink_cooldown", config.drink_cooldown)
            config.skill_interval := IniRead(this.configFile, "Timing", "skill_interval", config.skill_interval)
            config.skill_hold_time := IniRead(this.configFile, "Timing", "skill_hold_time", config.skill_hold_time)
            config.detection_interval := IniRead(this.configFile, "Timing", "detection_interval", config.detection_interval)
            
            ; 读取阈值配置
            config.drink_threshold := IniRead(this.configFile, "Thresholds", "drink_threshold", config.drink_threshold)
            config.skill_stop_threshold := IniRead(this.configFile, "Thresholds", "skill_stop_threshold", config.skill_stop_threshold)
            
        } catch Error as e {
            AddInfo("⚠️ 配置文件读取失败，使用默认配置: " . e.message)
        }
        
        return config
    }
    
    ; 保存配置
    static SaveConfig(config) {
        try {
            ; 保存血量检测配置
            IniWrite(config.hp_x, this.configFile, "BloodDetection", "x")
            IniWrite(config.hp_y, this.configFile, "BloodDetection", "y")
            IniWrite(config.hp_width, this.configFile, "BloodDetection", "width")
            IniWrite(config.hp_height, this.configFile, "BloodDetection", "height")
            IniWrite(config.hp_enabled, this.configFile, "BloodDetection", "enabled")
            
            ; 保存按键配置
            IniWrite(config.drink_key, this.configFile, "Keys", "drink_key")
            IniWrite(config.skill_key, this.configFile, "Keys", "skill_key")
            
            ; 保存时序配置
            IniWrite(config.drink_cooldown, this.configFile, "Timing", "drink_cooldown")
            IniWrite(config.skill_interval, this.configFile, "Timing", "skill_interval")
            IniWrite(config.skill_hold_time, this.configFile, "Timing", "skill_hold_time")
            IniWrite(config.detection_interval, this.configFile, "Timing", "detection_interval")
            
            ; 保存阈值配置
            IniWrite(config.drink_threshold, this.configFile, "Thresholds", "drink_threshold")
            IniWrite(config.skill_stop_threshold, this.configFile, "Thresholds", "skill_stop_threshold")
            
            AddInfo("✅ 配置已保存到: " . this.configFile)
            
        } catch Error as e {
            AddInfo("❌ 配置保存失败: " . e.message)
        }
    }
    
    ; 验证配置的合理性
    static ValidateConfig(config) {
        errors := []
        
        ; 检查坐标范围
        if (config.hp_x < 0 || config.hp_x > A_ScreenWidth) {
            errors.Push("血量检测X坐标超出屏幕范围")
        }
        
        if (config.hp_y < 0 || config.hp_y > A_ScreenHeight) {
            errors.Push("血量检测Y坐标超出屏幕范围")
        }
        
        ; 检查时序参数
        if (config.drink_cooldown < 100) {
            errors.Push("喝药冷却时间过短（最小100ms）")
        }
        
        if (config.skill_hold_time < 10 || config.skill_hold_time > 1000) {
            errors.Push("技能按下时间不合理（应在10-1000ms之间）")
        }
        
        ; 检查阈值
        if (config.drink_threshold <= config.skill_stop_threshold) {
            errors.Push("喝药阈值应大于技能停止阈值")
        }
        
        if (errors.Length > 0) {
            errorMsg := "配置验证失败:`n" . errors.Join("`n")
            MsgBox(errorMsg, "配置错误", 48)
            return false
        }
        
        return true
    }
    
    ; 自动检测分辨率并调整坐标
    static AutoAdjustForResolution(config) {
        ; 基准分辨率 1920x1080
        baseWidth := 1920
        baseHeight := 1080
        
        ; 当前分辨率
        currentWidth := A_ScreenWidth
        currentHeight := A_ScreenHeight
        
        ; 计算缩放比例
        scaleX := currentWidth / baseWidth
        scaleY := currentHeight / baseHeight
        
        ; 调整坐标
        config.hp_x := Round(config.hp_x * scaleX)
        config.hp_y := Round(config.hp_y * scaleY)
        config.hp_width := Round(config.hp_width * scaleX)
        config.hp_height := Round(config.hp_height * scaleY)
        
        AddInfo("📐 分辨率自适应调整: " . currentWidth . "x" . currentHeight)
        AddInfo("🎯 血量检测区域已调整为: " . config.hp_x . "," . config.hp_y . " " . config.hp_width . "x" . config.hp_height)
        
        return config
    }
}