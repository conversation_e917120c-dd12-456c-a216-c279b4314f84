#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
POE2 自动喝药 - 混合架构启动脚本
一键启动混合架构系统
"""

import sys
import os
import subprocess
import time
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖项"""
    logger.info("检查依赖项...")
    
    # 检查Python依赖
    required_packages = [
        'cv2', 'numpy', 'yaml', 'win32pipe', 'win32file', 
        'win32gui', 'win32ui', 'win32con', 'tkinter'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'tkinter':
                import tkinter
            else:
                __import__(package)
            logger.info(f"✅ {package} - 已安装")
        except ImportError:
            missing_packages.append(package)
            logger.error(f"❌ {package} - 未安装")
    
    if missing_packages:
        logger.error(f"缺少依赖包: {missing_packages}")
        print("\n请安装缺少的依赖包:")
        if 'cv2' in missing_packages:
            print("pip install opencv-python")
        if 'numpy' in missing_packages:
            print("pip install numpy")
        if 'yaml' in missing_packages:
            print("pip install pyyaml")
        if any(pkg.startswith('win32') for pkg in missing_packages):
            print("pip install pywin32")
        return False
    
    return True

def check_files():
    """检查必要文件"""
    logger.info("检查必要文件...")
    
    current_dir = Path(__file__).parent
    required_files = [
        'hybrid_gui.py',
        'detection_engine.py', 
        'hardware_input.ahk',
        'hybrid_config.yaml'
    ]
    
    missing_files = []
    for file in required_files:
        file_path = current_dir / file
        if file_path.exists():
            logger.info(f"✅ {file} - 存在")
        else:
            missing_files.append(file)
            logger.error(f"❌ {file} - 不存在")
    
    if missing_files:
        logger.error(f"缺少必要文件: {missing_files}")
        return False
    
    return True

def find_autohotkey():
    """查找AutoHotkey可执行文件"""
    logger.info("查找AutoHotkey...")
    
    possible_paths = [
        r"C:\Program Files\AutoHotkey\AutoHotkey.exe",
        r"C:\Program Files (x86)\AutoHotkey\AutoHotkey.exe",
        r"C:\Program Files\AutoHotkey\v2\AutoHotkey64.exe",
        r"C:\Program Files\AutoHotkey\v2\AutoHotkey32.exe",
        "AutoHotkey.exe"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"✅ 找到AutoHotkey: {path}")
            return path
    
    # 尝试在PATH中查找
    try:
        result = subprocess.run(["where", "AutoHotkey.exe"], 
                              capture_output=True, text=True, shell=True)
        if result.returncode == 0 and result.stdout.strip():
            path = result.stdout.strip().split('\n')[0]
            logger.info(f"✅ 在PATH中找到AutoHotkey: {path}")
            return path
    except:
        pass
    
    logger.error("❌ 未找到AutoHotkey")
    print("\n请安装AutoHotkey: https://www.autohotkey.com/")
    return None

def check_admin_rights():
    """检查管理员权限"""
    try:
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if is_admin:
            logger.info("✅ 具有管理员权限")
        else:
            logger.warning("⚠️ 没有管理员权限，某些功能可能无法正常工作")
        return is_admin
    except:
        logger.warning("⚠️ 无法检查管理员权限")
        return False

def start_ahk_script():
    """启动AutoHotkey脚本"""
    logger.info("启动AutoHotkey脚本...")
    
    try:
        current_dir = Path(__file__).parent
        ahk_script = current_dir / 'hardware_input.ahk'
        
        if not ahk_script.exists():
            logger.error(f"❌ AHK脚本不存在: {ahk_script}")
            return None
        
        logger.info(f"启动AHK脚本: {ahk_script}")
        # 直接双击运行AHK文件
        process = subprocess.Popen([str(ahk_script)], shell=True)
        logger.info(f"✅ AHK脚本已启动，进程ID: {process.pid}")
        return process
        
    except Exception as e:
        logger.error(f"启动AHK脚本失败: {e}")
        return None

def start_hybrid_system():
    """启动混合架构系统"""
    logger.info("启动混合架构系统...")
    
    try:
        # 启动GUI控制界面
        current_dir = Path(__file__).parent
        gui_script = current_dir / 'hybrid_gui.py'
        
        logger.info("启动GUI控制界面...")
        subprocess.run([sys.executable, str(gui_script)])
        
    except Exception as e:
        logger.error(f"启动失败: {e}")
        input("按回车键退出...")

def main():
    """主函数"""
    print("="*60)
    print("POE2 自动喝药 - 混合架构启动器")
    print("Python检测 + AHK硬件输入 = 最高性能方案")
    print("="*60)
    print()
    
    # 检查系统环境
    logger.info("开始系统检查...")
    
    # 检查依赖项
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    # 检查文件
    if not check_files():
        input("\n按回车键退出...")
        return
    
    # 检查AutoHotkey
    if not find_autohotkey():
        input("\n按回车键退出...")
        return
    
    # 检查管理员权限
    check_admin_rights()
    
    print("\n" + "="*60)
    print("✅ 系统检查完成，所有依赖项就绪")
    print("="*60)
    print()
    
    # 启动系统
    start_hybrid_system()

def main():
    """主函数 - UV脚本入口点"""
    print("正在启动 POE2 自动喝药混合架构系统...")
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        sys.exit(1)
    
    # 检查文件
    if not check_files():
        input("按回车键退出...")
        sys.exit(1)
    
    # 注意：现在直接运行AHK文件，不需要检查AutoHotkey.exe路径
    
    # 检查管理员权限
    if not check_admin_rights():
        print("\n⚠️  建议以管理员身份运行以获得最佳性能")
        print("某些功能可能需要管理员权限才能正常工作")
    
    # 启动GUI（GUI会负责启动Python检测端和AHK端）
    try:
        print("\n🚀 启动混合架构控制界面...")
        print("💡 控制界面将自动管理Python检测端和AHK硬件端的启动顺序")
        subprocess.run([sys.executable, "hybrid_gui.py"], check=True)
        print("\n✅ 程序已正常退出")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 启动失败: {e}")
        input("按回车键退出...")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断程序")
        sys.exit(0)


if __name__ == "__main__":
    main()