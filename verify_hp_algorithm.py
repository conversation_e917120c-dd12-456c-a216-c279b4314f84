import cv2
import numpy as np
import os
from pathlib import Path

def rgb_to_hsv_manual(r, g, b):
    """手动实现RGB到HSV转换"""
    r = r / 255.0
    g = g / 255.0
    b = b / 255.0
    
    max_val = max(r, g, b)
    min_val = min(r, g, b)
    delta = max_val - min_val
    
    v = max_val
    
    if max_val == 0:
        s = 0
    else:
        s = delta / max_val
    
    if delta == 0:
        h = 0
    elif max_val == r:
        h = 60 * (((g - b) / delta) + (6 if g < b else 0))
    elif max_val == g:
        h = 60 * (((b - r) / delta) + 2)
    else:
        h = 60 * (((r - g) / delta) + 4)
    
    if h < 0:
        h += 360
    
    return h, s, v

def check_red_color_hsv(r, g, b):
    """HSV红色检测"""
    total_brightness = r + g + b
    if total_brightness <= 120:
        return False
    
    if r >= 240 and g >= 240 and b >= 240:
        return False
    
    if r <= 15 and g <= 15 and b <= 15:
        return False
    
    h, s, v = rgb_to_hsv_manual(r, g, b)
    
    is_red_hue = (h >= 0 and h <= 30) or (h >= 330 and h <= 360)
    has_enough_saturation = s >= 0.3
    has_enough_brightness = v >= 0.2
    
    is_orange_red = (h >= 30 and h <= 60) and s >= 0.5 and v >= 0.3
    is_dark_red = is_red_hue and s >= 0.4 and v >= 0.15
    
    return (is_red_hue and has_enough_saturation and has_enough_brightness) or is_orange_red or is_dark_red

def check_red_color_rgb_backup(r, g, b):
    """RGB备用检测"""
    if r >= 240 and g >= 240 and b >= 240:
        return False
    
    if r <= 15 and g <= 15 and b <= 15:
        return False
    
    total_brightness = r + g + b
    if total_brightness <= 120:
        return False
    
    if abs(r - g) <= 8 and abs(r - b) <= 8 and abs(g - b) <= 8 and r <= g + 5:
        return False
    
    red_dominant = (r >= 60 and r > g + 15 and r > b + 15)
    traditional_red = (r >= 80 and r <= 200 and g <= 80 and b <= 80 and r > g + 20 and r > b + 20)
    
    return red_dominant or traditional_red

def check_red_color_combined(r, g, b):
    """组合检测：HSV + RGB备用"""
    hsv_result = check_red_color_hsv(r, g, b)
    if hsv_result:
        return True
    return check_red_color_rgb_backup(r, g, b)

def algorithm_1_simple_percentage(img_rgb, center_x):
    """算法1：简单像素百分比"""
    height = img_rgb.shape[0]
    red_count = 0
    
    for y in range(height):
        r, g, b = img_rgb[y, center_x]
        if check_red_color_combined(r, g, b):
            red_count += 1
    
    return (red_count / height) * 100

def algorithm_2_exclude_borders(img_rgb, center_x, border_pixels=3):
    """算法2：排除边框像素"""
    height = img_rgb.shape[0]
    effective_height = height - 2 * border_pixels
    red_count = 0
    
    for y in range(border_pixels, height - border_pixels):
        r, g, b = img_rgb[y, center_x]
        if check_red_color_combined(r, g, b):
            red_count += 1
    
    return (red_count / effective_height) * 100

def algorithm_3_weighted_regions(img_rgb, center_x):
    """算法3：加权区域检测"""
    height = img_rgb.shape[0]
    red_count = 0
    total_weight = 0
    
    for y in range(height):
        r, g, b = img_rgb[y, center_x]
        
        # 给中间区域更高权重，边缘区域较低权重
        if y < height * 0.1 or y > height * 0.9:
            weight = 0.3  # 边缘权重低
        else:
            weight = 1.0  # 中间权重正常
        
        total_weight += weight
        if check_red_color_combined(r, g, b):
            red_count += weight
    
    return (red_count / total_weight) * 100

def algorithm_4_continuous_regions(img_rgb, center_x):
    """算法4：最大连续红色区域"""
    height = img_rgb.shape[0]
    max_continuous = 0
    current_continuous = 0
    
    for y in range(height):
        r, g, b = img_rgb[y, center_x]
        if check_red_color_combined(r, g, b):
            current_continuous += 1
        else:
            max_continuous = max(max_continuous, current_continuous)
            current_continuous = 0
    
    max_continuous = max(max_continuous, current_continuous)
    return (max_continuous / height) * 100

def algorithm_5_density_based(img_rgb, center_x):
    """算法5：基于密度的检测"""
    height = img_rgb.shape[0]
    red_pixels = []
    
    for y in range(height):
        r, g, b = img_rgb[y, center_x]
        if check_red_color_combined(r, g, b):
            red_pixels.append(y)
    
    if len(red_pixels) == 0:
        return 0.0
    
    # 计算红色像素的密集区域
    span = red_pixels[-1] - red_pixels[0] + 1
    density = len(red_pixels) / span
    
    # 如果密度高，使用跨度比例；如果密度低，使用像素比例
    if density > 0.7:
        return (span / height) * 100
    else:
        return (len(red_pixels) / height) * 100

def test_algorithm(image_path, hp_percentage, algorithm_func, algorithm_name):
    """测试单个算法"""
    try:
        with open(image_path, 'rb') as f:
            img_data = f.read()
        
        img_array = np.frombuffer(img_data, np.uint8)
        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        
        if img is None:
            return None
        
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        height, width = img_rgb.shape[:2]
        center_x = width // 2
        
        calculated_hp = algorithm_func(img_rgb, center_x)
        error = abs(calculated_hp - hp_percentage)
        
        return {
            'algorithm': algorithm_name,
            'calculated': calculated_hp,
            'actual': hp_percentage,
            'error': error,
            'accuracy': 100 - (error / hp_percentage * 100) if hp_percentage > 0 else 0
        }
    
    except Exception as e:
        print(f"❌ 测试 {algorithm_name} 失败: {e}")
        return None

def main():
    """主函数：测试所有算法"""
    print("🧪 血量检测算法验证程序")
    print("=" * 60)
    
    screenshots_folder = "血量截图"
    test_cases = [
        ('100.png', 100),
        ('50.png', 50),
        ('10.png', 10)
    ]
    
    algorithms = [
        (algorithm_1_simple_percentage, "算法1: 简单像素百分比"),
        (algorithm_2_exclude_borders, "算法2: 排除边框像素"),
        (algorithm_3_weighted_regions, "算法3: 加权区域检测"),
        (algorithm_4_continuous_regions, "算法4: 最大连续区域"),
        (algorithm_5_density_based, "算法5: 基于密度检测")
    ]
    
    results = []
    
    for filename, actual_hp in test_cases:
        file_path = os.path.join(screenshots_folder, filename)
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            continue
        
        print(f"\n📊 测试 {filename} (实际血量: {actual_hp}%)")
        print("-" * 40)
        
        for algorithm_func, algorithm_name in algorithms:
            result = test_algorithm(file_path, actual_hp, algorithm_func, algorithm_name)
            if result:
                results.append(result)
                print(f"{algorithm_name}:")
                print(f"   计算结果: {result['calculated']:.1f}%")
                print(f"   误差: {result['error']:.1f}%")
                print(f"   准确度: {result['accuracy']:.1f}%")
    
    # 分析最佳算法
    print("\n🏆 算法性能总结")
    print("=" * 60)
    
    algorithm_stats = {}
    for result in results:
        algo_name = result['algorithm']
        if algo_name not in algorithm_stats:
            algorithm_stats[algo_name] = {
                'total_error': 0,
                'count': 0,
                'results': []
            }
        
        algorithm_stats[algo_name]['total_error'] += result['error']
        algorithm_stats[algo_name]['count'] += 1
        algorithm_stats[algo_name]['results'].append(result)
    
    # 计算平均误差
    for algo_name, stats in algorithm_stats.items():
        avg_error = stats['total_error'] / stats['count']
        print(f"\n{algo_name}:")
        print(f"   平均误差: {avg_error:.1f}%")
        
        # 显示各个测试用例的结果
        for result in stats['results']:
            print(f"   {result['actual']}%血量: {result['calculated']:.1f}% (误差{result['error']:.1f}%)")
    
    # 找出最佳算法
    best_algorithm = min(algorithm_stats.items(), 
                        key=lambda x: x[1]['total_error'] / x[1]['count'])
    
    print(f"\n🥇 最佳算法: {best_algorithm[0]}")
    print(f"   平均误差: {best_algorithm[1]['total_error'] / best_algorithm[1]['count']:.1f}%")
    
    # 推荐参数
    print(f"\n💡 推荐配置:")
    print(f"   使用算法: {best_algorithm[0]}")
    print(f"   预期精度: ±{best_algorithm[1]['total_error'] / best_algorithm[1]['count']:.1f}%")
    
    # 生成AutoHotkey代码建议
    print(f"\n🔧 AutoHotkey实现建议:")
    if "算法2" in best_algorithm[0]:
        print("   建议在AutoHotkey中排除顶部和底部3个像素")
    elif "算法3" in best_algorithm[0]:
        print("   建议在AutoHotkey中对边缘像素使用较低权重")
    elif "算法4" in best_algorithm[0]:
        print("   建议在AutoHotkey中寻找最大连续红色区域")
    elif "算法5" in best_algorithm[0]:
        print("   建议在AutoHotkey中使用密度自适应算法")
    else:
        print("   当前的简单像素百分比算法已经足够好")

if __name__ == "__main__":
    main() 