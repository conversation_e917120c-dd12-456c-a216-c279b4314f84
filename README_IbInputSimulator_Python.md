# IbInputSimulator Python版本

基于 [Chaoses-Ib/IbInputSimulator](https://github.com/Chaoses-Ib/IbInputSimulator) 的Python实现，专注于键盘输入的硬件级别输入模拟。

## 特性

- 🎯 **专注键盘输入** - 移除鼠标功能，专注于键盘按键模拟
- 🔧 **多驱动支持** - 支持多种硬件级别输入驱动
- 🚀 **简单易用** - 提供简洁的API接口
- 🛡️ **自动回退** - 智能选择可用的最佳驱动
- 📝 **完整文档** - 详细的使用说明和示例
- 🔄 **资源管理** - 支持上下文管理器，自动清理资源

## 支持的驱动

| 驱动类型 | 描述 | 硬件要求 | 兼容性 |
|---------|------|----------|--------|
| **AnyDriver** | 自动选择最佳驱动 | 无 | 高 |
| **Logitech** | Logitech GHUB驱动 | 无需硬件 | 高 |
| **LogitechGHubNew** | 新版GHUB驱动 | 无需硬件 | 中 |
| **Razer** | Razer Synapse驱动 | 可能需要硬件 | 中 |
| **DD** | DD虚拟键盘驱动 | 无 | 低 |
| **SendInput** | Windows系统API | 无 | 高 |

## 安装

### 前置要求

- Python 3.7+
- Windows操作系统
- IbInputSimulator.dll (可选，用于硬件级别输入)

### 文件结构

```
AutoHotkey/
├── ib_input_simulator.py          # 主库文件
├── ib_input_simulator_example.py  # 使用示例
├── IbInputSimulator.dll           # 原始DLL文件
└── README_IbInputSimulator_Python.md
```

### 快速开始

1. 确保 `ib_input_simulator.py` 在你的项目目录中
2. 可选：将 `IbInputSimulator.dll` 放在同一目录下以获得最佳性能

## 使用方法

### 基本用法

```python
from ib_input_simulator import IbInputSimulator

# 创建实例（自动选择驱动）
sim = IbInputSimulator()

# 发送单个按键
sim.send_key("a")

# 发送字符串
sim.send_keys("Hello World!")

# 发送特殊按键
sim.send_special_key("enter")
sim.send_special_key("space")
sim.send_special_key("f1")

# 清理资源
sim.cleanup()
```

### 使用上下文管理器（推荐）

```python
from ib_input_simulator import IbInputSimulator

# 自动管理资源
with IbInputSimulator(driver="Logitech") as sim:
    if sim.is_available():
        sim.send_keys("12345", interval=0.2)
        sim.send_special_key("enter")
```

### 便捷函数

```python
from ib_input_simulator import send_key, send_keys, send_special_key

# 直接使用，无需管理实例
send_key("1")
send_keys("Hello", interval=0.1)
send_special_key("space")
```

### 指定特定驱动

```python
# 使用Logitech驱动
sim = IbInputSimulator(driver="Logitech")

# 使用Razer驱动
sim = IbInputSimulator(driver="Razer")

# 使用系统API
sim = IbInputSimulator(driver="SendInput")
```

## API参考

### IbInputSimulator类

#### 构造函数

```python
IbInputSimulator(driver="AnyDriver", dll_path=None, config=None)
```

- `driver`: 驱动类型，可选值见上表
- `dll_path`: IbInputSimulator.dll的自定义路径
- `config`: 配置字典（可选）

#### 主要方法

##### send_key(key, duration=0.01)
发送单个按键
- `key`: 按键字符或按键名
- `duration`: 按键持续时间（秒）
- 返回: `bool` - 是否成功

##### send_keys(keys, interval=0.05)
发送多个按键
- `keys`: 按键字符串
- `interval`: 按键间隔时间（秒）
- 返回: `bool` - 是否全部成功

##### send_special_key(key_name, duration=0.01)
发送特殊按键
- `key_name`: 特殊按键名称
- `duration`: 按键持续时间（秒）
- 返回: `bool` - 是否成功

##### is_available()
检查是否有可用的输入方法
- 返回: `bool`

##### get_status()
获取当前驱动状态
- 返回: `dict` - 包含驱动信息的字典

##### cleanup()
清理资源

### 支持的按键

#### 普通字符
- 字母: `a-z`, `A-Z`
- 数字: `0-9`

#### 特殊按键
- `space` - 空格键
- `enter` - 回车键
- `tab` - Tab键
- `esc`, `escape` - Esc键
- `backspace` - 退格键
- `delete`, `del` - Delete键
- `home` - Home键
- `end` - End键
- `pageup`, `pgup` - Page Up键
- `pagedown`, `pgdn` - Page Down键
- `up`, `down`, `left`, `right` - 方向键
- `f1` - `f12` - 功能键
- `shift`, `ctrl`, `alt` - 修饰键

## 使用示例

### 游戏自动化

```python
with IbInputSimulator(driver="Logitech") as sim:
    if sim.is_available():
        # 游戏技能循环
        skills = ["1", "2", "3", "4"]
        for skill in skills:
            sim.send_key(skill)
            time.sleep(1.5)  # 技能冷却
```

### 文本输入

```python
with IbInputSimulator() as sim:
    # 输入用户名和密码
    sim.send_keys("username")
    sim.send_special_key("tab")
    sim.send_keys("password")
    sim.send_special_key("enter")
```

### 批量操作

```python
with IbInputSimulator(driver="AnyDriver") as sim:
    for i in range(10):
        sim.send_key("1")  # 使用物品
        time.sleep(0.5)
        sim.send_special_key("space")  # 确认
        time.sleep(2)
```

## 故障排除

### 常见问题

1. **没有可用的驱动**
   - 确保IbInputSimulator.dll在正确位置
   - 尝试以管理员权限运行
   - 检查是否安装了Logitech GHUB或Razer Synapse

2. **按键发送失败**
   - 检查目标窗口是否处于前台
   - 尝试不同的驱动类型
   - 增加按键间隔时间

3. **DLL加载失败**
   - 确保使用64位Python
   - 检查DLL文件是否损坏
   - 尝试指定完整的DLL路径

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 现在会显示详细的调试信息
with IbInputSimulator() as sim:
    status = sim.get_status()
    print(f"驱动状态: {status}")
```

### 测试驱动兼容性

```python
from ib_input_simulator import IbInputSimulator

drivers = ["AnyDriver", "Logitech", "Razer", "SendInput"]
for driver in drivers:
    with IbInputSimulator(driver=driver) as sim:
        print(f"{driver}: {'可用' if sim.is_available() else '不可用'}")
```

## 性能优化

1. **重用实例**: 避免频繁创建和销毁IbInputSimulator实例
2. **选择合适的驱动**: Logitech驱动通常性能最好
3. **调整时间间隔**: 根据应用需求调整按键间隔
4. **使用上下文管理器**: 确保资源正确释放

## 安全注意事项

- 本库可以绕过某些反作弊系统，请遵守游戏规则
- 仅在合法和授权的环境中使用
- 不要用于恶意软件或未授权的自动化

## 许可证

基于原始IbInputSimulator项目的许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个Python版本。

## 更新日志

### v1.0.0
- 初始版本
- 支持多种硬件驱动
- 专注键盘输入功能
- 完整的API和文档

---

**注意**: 这是基于Chaoses-Ib/IbInputSimulator的Python重写版本，专门针对键盘输入进行了优化。