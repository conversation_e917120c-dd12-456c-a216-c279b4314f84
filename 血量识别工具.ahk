; POE2 血量识别工具 - 带GUI界面
; 使用说明：运行脚本后会显示控制面板

; 全局变量
isMonitoring := false
monitoringTimer := 0
lastResult := ""

; 创建主界面
CreateGUI()

; 热键保持原有功能
F6::AnalyzeColor()

; 创建GUI函数
CreateGUI() {
    global
    
    ; 创建主窗口
    mainGui := Gui("+Resize +MinSize400x300", "POE2 血量识别工具 v2.0")
    mainGui.SetFont("s10", "Microsoft YaHei")
    
    ; 添加说明文本
    mainGui.Add("Text", "x10 y10 w380 h40 Center", 
        "将鼠标移动到血量条上，然后点击分析按钮或按F6键")
    
    ; 添加按钮组
    btnAnalyze := mainGui.Add("Button", "x10 y60 w120 h35", "分析颜色 (F6)")
    btnMonitor := mainGui.Add("Button", "x140 y60 w120 h35", "开始监控")
    btnClear := mainGui.Add("Button", "x270 y60 w120 h35", "清空结果")
    
    ; 添加状态显示
    statusText := mainGui.Add("Text", "x10 y105 w380 h20", "状态: 就绪")
    
    ; 添加结果显示区域
    mainGui.Add("Text", "x10 y130 w100 h20", "分析结果:")
    resultEdit := mainGui.Add("Edit", "x10 y150 w380 h200 ReadOnly VScroll", "")
    
    ; 绑定事件
    btnAnalyze.OnEvent("Click", (*) => AnalyzeColor())
    btnMonitor.OnEvent("Click", (*) => ToggleMonitoring())
    btnClear.OnEvent("Click", (*) => ClearResults())
    
    ; 窗口关闭事件
    mainGui.OnEvent("Close", (*) => ExitApp())
    
    ; 显示窗口
    mainGui.Show("w400 h370")
    
    ; 保存控件引用到全局变量
    global gMainGui := mainGui
    global gStatusText := statusText
    global gResultEdit := resultEdit
    global gBtnMonitor := btnMonitor
}

; 分析颜色函数（原有功能）
AnalyzeColor() {
    global
    
    UpdateStatus("正在分析颜色...")
    
    ; 采集鼠标周围5x5区域的颜色
    MouseGetPos(&mx, &my)
    colors := []
    
    ; 获取屏幕分辨率
    screenWidth := A_ScreenWidth
    screenHeight := A_ScreenHeight
    
    ; 收集颜色数据
    Loop 5 {
        y := my - 2 + A_Index - 1
        Loop 5 {
            x := mx - 2 + A_Index - 1
            
            ; 边界检查
            if (x < 0 || x >= screenWidth || y < 0 || y >= screenHeight) {
                continue  ; 跳过超出屏幕的点
            }
            
            try {
                color := PixelGetColor(x, y)
                colors.Push({
                    x: x, y: y, 
                    color: color,
                    r: (color >> 16) & 0xFF,
                    g: (color >> 8) & 0xFF,
                    b: color & 0xFF
                })
            } catch {
                ; 忽略获取颜色失败的情况
                continue
            }
        }
    }
    
    ; 检查是否有有效的颜色数据
    if (colors.Length == 0) {
        UpdateStatus("错误: 无法获取颜色数据")
        MsgBox("无法获取颜色数据，请检查鼠标位置", "错误")
        return
    }
    
    ; 分析颜色特征
    avgR := 0, avgG := 0, avgB := 0
    minR := 255, maxR := 0
    minG := 255, maxG := 0
    minB := 255, maxB := 0
    
    for c in colors {
        avgR += c.r
        avgG += c.g
        avgB += c.b
        minR := Min(minR, c.r)
        maxR := Max(maxR, c.r)
        minG := Min(minG, c.g)
        maxG := Max(maxG, c.g)
        minB := Min(minB, c.b)
        maxB := Max(maxB, c.b)
    }
    
    avgR /= colors.Length
    avgG /= colors.Length
    avgB /= colors.Length
    
    ; 计算标准差以改进建议范围
    varR := 0, varG := 0, varB := 0
    for c in colors {
        varR += (c.r - avgR) ** 2
        varG += (c.g - avgG) ** 2
        varB += (c.b - avgB) ** 2
    }
    stdR := Sqrt(varR / colors.Length)
    stdG := Sqrt(varG / colors.Length)
    stdB := Sqrt(varB / colors.Length)
    
    ; 使用标准差来计算更合理的范围
    rangeR := Max(10, Round(stdR * 2))
    rangeG := Max(10, Round(stdG * 2))
    rangeB := Max(10, Round(stdB * 2))
    
    ; 生成分析结果
    result := "=== " . FormatTime(A_Now, "HH:mm:ss") . " 颜色分析 ===`n"
    result .= Format("位置: ({}, {})`n", mx, my)
    result .= Format("采样: {} / 25 点`n", colors.Length)
    result .= Format("平均: R={:.0f} G={:.0f} B={:.0f}`n", avgR, avgG, avgB)
    result .= Format("标准差: R={:.1f} G={:.1f} B={:.1f}`n", stdR, stdG, stdB)
    result .= Format("范围: R({}-{}) G({}-{}) B({}-{})`n", 
        minR, maxR, minG, maxG, minB, maxB)
    result .= Format("建议检测条件:`n")
    result .= Format("r>={} && r<={} &&`n", Max(0, Round(avgR - rangeR)), Min(255, Round(avgR + rangeR)))
    result .= Format("g>={} && g<={} &&`n", Max(0, Round(avgG - rangeG)), Min(255, Round(avgG + rangeG)))
    result .= Format("b>={} && b<={}`n", Max(0, Round(avgB - rangeB)), Min(255, Round(avgB + rangeB)))
    result .= "`n"
    
    ; 更新界面
    lastResult := result
    AppendResult(result)
    UpdateStatus("分析完成 - 位置: " . mx . "," . my)
    
    ; 复制到剪贴板
    A_Clipboard := result
}

; 切换监控状态
ToggleMonitoring() {
    global
    
    if (!isMonitoring) {
        ; 开始监控
        isMonitoring := true
        gBtnMonitor.Text := "停止监控"
        UpdateStatus("监控中... (每2秒分析一次)")
        
        ; 设置定时器
        monitoringTimer := SetTimer(MonitoringLoop, 2000)
    } else {
        ; 停止监控
        isMonitoring := false
        gBtnMonitor.Text := "开始监控"
        UpdateStatus("监控已停止")
        
        ; 清除定时器
        if (monitoringTimer) {
            SetTimer(monitoringTimer, 0)
            monitoringTimer := 0
        }
    }
}

; 监控循环
MonitoringLoop() {
    global
    if (isMonitoring) {
        AnalyzeColor()
    }
}

; 清空结果
ClearResults() {
    global
    gResultEdit.Text := ""
    UpdateStatus("结果已清空")
}

; 更新状态文本
UpdateStatus(text) {
    global
    gStatusText.Text := "状态: " . text
}

; 添加结果到显示区域
AppendResult(text) {
    global
    currentText := gResultEdit.Text
    gResultEdit.Text := currentText . text
    
    ; 滚动到底部
    gResultEdit.Focus()
    Send("^{End}")
}

; ESC键退出
Esc::ExitApp()