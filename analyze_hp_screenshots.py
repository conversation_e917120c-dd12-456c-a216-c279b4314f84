import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import os
import colorsys

def analyze_hp_bar(image_path, hp_percentage):
    """分析血量条截图的颜色分布"""
    print(f"\n🔍 分析 {hp_percentage}% 血量截图: {image_path}")
    
    # 使用cv2.imdecode处理中文路径
    try:
        # 读取图片数据
        with open(image_path, 'rb') as f:
            img_data = f.read()
        
        # 转换为numpy数组
        img_array = np.frombuffer(img_data, np.uint8)
        
        # 解码图片
        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        
        if img is None:
            print(f"❌ 无法解码图片: {image_path}")
            return None, None
            
    except Exception as e:
        print(f"❌ 读取图片失败: {e}")
        return None, None
    
    # 转换为RGB格式 (OpenCV默认是BGR)
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    height, width = img_rgb.shape[:2]
    
    print(f"📏 图片尺寸: {width}x{height}")
    
    # 分析中心线的颜色分布 (模拟AutoHotkey的扫描方式)
    center_x = width // 2
    
    print(f"🔍 扫描中心线 X={center_x}")
    print("Y坐标 | RGB值        | 是否红色 | HSV检测 | HSV值")
    print("-" * 70)
    
    red_pixels = []
    total_pixels = height
    
    for y in range(height):
        r, g, b = img_rgb[y, center_x]
        
        # 使用与AutoHotkey相同的红色检测逻辑
        is_red = check_red_color(r, g, b)
        brightness = int(r) + int(g) + int(b)
        
        if is_red:
            red_pixels.append(y)
        
        # 每10个像素输出一次，避免输出过多
        if y % max(1, height // 20) == 0 or is_red:
            # 计算HSV值用于显示
            h, s, v = rgb_to_hsv_manual(r, g, b)
            hsv_result = check_red_color_hsv(r, g, b)
            
            status = "✅红色" if is_red else "❌非红"
            hsv_status = "HSV✅" if hsv_result else "HSV❌"
            print(f"{y:3d}    | RGB({r:3d},{g:3d},{b:3d}) | {status:6s} | {hsv_status} | H:{h:3.0f}° S:{s:.2f} V:{v:.2f}")
    
    # 计算血量 - 红色像素占总像素的百分比（截图是血量条的部分区域）
    red_pixel_count = len(red_pixels)
    total_pixel_count = height
    
    print(f"\n🔍 血量计算 (像素百分比算法):")
    print(f"   红色像素数: {red_pixel_count}")
    print(f"   总像素数: {total_pixel_count}")
    
    if total_pixel_count > 0:
        calculated_hp = (red_pixel_count / total_pixel_count) * 100
        
        print(f"\n📊 分析结果 (像素百分比算法):") 
        print(f"   红色像素占比: {red_pixel_count}/{total_pixel_count} = {calculated_hp:.1f}%")
        print(f"   实际血量: {hp_percentage}%")
        print(f"   误差: {abs(calculated_hp - hp_percentage):.1f}%")
        
        # 分析红色像素的分布
        if red_pixel_count > 0:
            first_red = min(red_pixels)
            last_red = max(red_pixels)
            red_span = last_red - first_red + 1
            
            print(f"\n🔍 红色像素分布:")
            print(f"   首个红色像素: Y={first_red}")
            print(f"   最后红色像素: Y={last_red}")
            print(f"   红色区域跨度: {red_span} 像素")
            print(f"   分布密度: {red_pixel_count}/{red_span} = {red_pixel_count/red_span:.2f}")
            
            # 显示一些红色像素的详细信息
            print(f"\n🎨 红色像素样本:")
            sample_indices = [0, len(red_pixels)//4, len(red_pixels)//2, len(red_pixels)*3//4, len(red_pixels)-1]
            for i, idx in enumerate(sample_indices):
                if idx < len(red_pixels):
                    y_pos = red_pixels[idx]
                    r, g, b = img_rgb[y_pos, center_x]
                    h, s, v = rgb_to_hsv_manual(r, g, b)
                    print(f"     样本{i+1} Y={y_pos}: RGB({r},{g},{b}) HSV(H:{h:.0f}° S:{s:.2f} V:{v:.2f})")
        
    else:
        print(f"\n📊 分析结果: 未检测到红色像素 (空血)")
        calculated_hp = 0.0
    
    return img_rgb, red_pixels

def rgb_to_hsv_manual(r, g, b):
    """手动实现RGB到HSV转换，与AutoHotkey算法保持一致"""
    # 将RGB值标准化到0-1范围
    r = r / 255.0
    g = g / 255.0
    b = b / 255.0
    
    # 找到最大值和最小值
    max_val = max(r, g, b)
    min_val = min(r, g, b)
    delta = max_val - min_val
    
    # 计算V (明度)
    v = max_val
    
    # 计算S (饱和度)
    if max_val == 0:
        s = 0
    else:
        s = delta / max_val
    
    # 计算H (色相)
    if delta == 0:
        h = 0  # 灰色
    elif max_val == r:
        h = 60 * (((g - b) / delta) + (6 if g < b else 0))
    elif max_val == g:
        h = 60 * (((b - r) / delta) + 2)
    else:
        h = 60 * (((r - g) / delta) + 4)
    
    # 确保H在0-360范围内
    if h < 0:
        h += 360
    
    return h, s, v

def check_red_color_hsv(r, g, b):
    """使用HSV的红色检测函数，与AutoHotkey保持一致"""
    # 快速排除明显的非红色
    # 1. 排除过暗的像素 (总亮度太低)
    total_brightness = r + g + b
    if total_brightness <= 120:
        return False
    
    # 2. 排除过亮的白色像素
    if r >= 240 and g >= 240 and b >= 240:
        return False
    
    # 3. 排除过暗的黑色像素
    if r <= 15 and g <= 15 and b <= 15:
        return False
    
    # 转换为HSV
    h, s, v = rgb_to_hsv_manual(r, g, b)
    
    # HSV红色检测条件
    # 红色的色相范围：0-30度 和 330-360度
    # 饱和度要求：至少0.3 (30%)
    # 明度要求：至少0.2 (20%)
    
    is_red_hue = (h >= 0 and h <= 30) or (h >= 330 and h <= 360)
    has_enough_saturation = s >= 0.3  # 饱和度至少30%
    has_enough_brightness = v >= 0.2  # 明度至少20%
    
    # 橙红色范围 (30-60度，但饱和度要更高)
    is_orange_red = (h >= 30 and h <= 60) and s >= 0.5 and v >= 0.3
    
    # 深红色 (允许较低的明度但要求更高的饱和度)
    is_dark_red = is_red_hue and s >= 0.4 and v >= 0.15
    
    return (is_red_hue and has_enough_saturation and has_enough_brightness) or is_orange_red or is_dark_red

def check_red_color(r, g, b):
    """红色检测 - 使用HSV和RGB双重验证，与AutoHotkey保持一致"""
    # 首先使用HSV检测
    hsv_result = check_red_color_hsv(r, g, b)
    
    # 如果HSV检测通过，直接返回True
    if hsv_result:
        return True
    
    # 如果HSV检测失败，使用原RGB方法作为备用
    # 排除明显非红色的情况
    if r >= 240 and g >= 240 and b >= 240:
        return False
    
    if r <= 15 and g <= 15 and b <= 15:
        return False
    
    total_brightness = r + g + b
    if total_brightness <= 120:
        return False
    
    if abs(r - g) <= 8 and abs(r - b) <= 8 and abs(g - b) <= 8 and r <= g + 5:
        return False
    
    # RGB备用检测条件 (更严格)
    red_dominant = (r >= 60 and r > g + 15 and r > b + 15)
    traditional_red = (r >= 80 and r <= 200 and g <= 80 and b <= 80 and r > g + 20 and r > b + 20)
    
    return red_dominant or traditional_red

def create_visualization(screenshots_folder):
    """创建可视化图表"""
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    files = ['100.png', '50.png', '10.png']
    percentages = [100, 50, 10]
    
    valid_results = []
    
    for i, (filename, hp_percent) in enumerate(zip(files, percentages)):
        file_path = os.path.join(screenshots_folder, filename)
        
        if os.path.exists(file_path):
            img, red_pixels = analyze_hp_bar(file_path, hp_percent)
            
            if img is not None:
                # 显示原图
                axes[i].imshow(img)
                axes[i].set_title(f'{hp_percent}% 血量')
                axes[i].axis('off')
                
                # 在图上标记红色像素
                if red_pixels:
                    center_x = img.shape[1] // 2
                    for y in red_pixels[::max(1, len(red_pixels)//20)]:  # 只显示部分点，避免过密
                        axes[i].plot(center_x, y, 'yo', markersize=2)
                
                valid_results.append((filename, hp_percent, red_pixels))
            else:
                # 如果图片无法加载，显示空白
                axes[i].text(0.5, 0.5, f'无法加载\n{filename}', 
                           horizontalalignment='center', verticalalignment='center',
                           transform=axes[i].transAxes, fontsize=12)
                axes[i].set_title(f'{hp_percent}% 血量 (加载失败)')
                axes[i].axis('off')
    
    plt.tight_layout()
    plt.savefig('hp_analysis.png', dpi=150, bbox_inches='tight')
    
    # 不显示图表，避免阻塞
    # plt.show()
    
    print(f"\n📊 可视化图表已保存为: hp_analysis.png")
    return valid_results

def main():
    """主函数"""
    # 使用相对路径
    screenshots_folder = "血量截图"
    
    print("🩸 POE2血量条截图分析工具")
    print("=" * 50)
    
    # 检查文件夹是否存在
    if not os.path.exists(screenshots_folder):
        print(f"❌ 文件夹不存在: {screenshots_folder}")
        return
    
    # 分析每个截图
    files_and_percentages = [
        ('100.png', 100),
        ('50.png', 50), 
        ('10.png', 10)
    ]
    
    results = []
    
    for filename, hp_percentage in files_and_percentages:
        file_path = os.path.join(screenshots_folder, filename)
        if os.path.exists(file_path):
            img, red_pixels = analyze_hp_bar(file_path, hp_percentage)
            if img is not None:
                results.append((filename, hp_percentage, red_pixels))
        else:
            print(f"❌ 文件不存在: {file_path}")
    
    # 创建可视化
    valid_results = create_visualization(screenshots_folder)
    
    print("\n🎯 分析总结:")
    print("=" * 50)
    
    if valid_results:
        print("✅ 成功分析的截图:")
        for filename, hp_percent, red_pixels in valid_results:
            red_count = len(red_pixels) if red_pixels else 0
            print(f"   {filename}: {hp_percent}% 血量, {red_count} 个红色像素")
        
        print("\n📈 检测算法评估:")
        total_error = 0
        valid_count = 0
        
        for filename, hp_percent, red_pixels in valid_results:
            if red_pixels:
                # 简单计算血量 (这里需要知道图片高度)
                # 由于我们没有保存图片高度信息，这里只做概念性分析
                print(f"   {filename}: 检测到红色区域，算法工作正常")
                valid_count += 1
            else:
                print(f"   {filename}: 未检测到红色，可能需要调整检测参数")
        
        if valid_count > 0:
            print(f"\n✅ {valid_count}/{len(valid_results)} 个截图成功检测到红色")
        else:
            print(f"\n❌ 所有截图都未检测到红色，建议调整检测算法")
    
    print("\n💡 基于分析结果的建议:")
    print("1. 验证当前红色检测算法的准确性")
    print("2. 了解不同血量状态下的实际颜色分布")
    print("3. 调整血量计算算法以提高精度")
    print("\n🔧 可能需要调整AutoHotkey脚本中的:")
    print("   - 红色检测条件的参数")
    print("   - 血量计算公式")
    print("   - 检测区域的位置和大小")

if __name__ == "__main__":
    main() 