#Requires AutoHotkey v2.0 64-bit
#SingleInstance Force

; ===================================================================
; 性能优化版本 - 主要优化点：
; 1. 分离定时器，避免单一循环负载过重
; 2. 采样检测替代逐像素扫描
; 3. 简化颜色检测算法
; 4. 缓存机制减少重复计算
; 5. 批量GUI更新
; ===================================================================

; 包含 IbInputSimulator 库
#Include "IbInputSimulator\IbInputSimulator.ahk"

; ===================================================================
; 配置文件管理系统（保持不变）
; ===================================================================
class ConfigManager {
    static configFile := A_ScriptDir . "\poe2_config.ini"
    
    static defaultConfig := {
        hp_x: 1760,
        hp_y: 865,
        hp_width: 25,
        hp_height: 180,
        hp_enabled: false,
        drink_key: "4",
        skill_key: "5",
        drink_cooldown: 2000,
        skill_interval: 800,
        skill_hold_time: 80,
        detection_interval: 200,
        drink_threshold: 75,
        skill_stop_threshold: 30
    }
    
    static LoadConfig() {
        config := this.defaultConfig.Clone()
        
        if (!FileExist(this.configFile)) {
            this.SaveConfig(config)
            return config
        }
        
        try {
            config.hp_x := Integer(IniRead(this.configFile, "BloodDetection", "x", config.hp_x))
            config.hp_y := Integer(IniRead(this.configFile, "BloodDetection", "y", config.hp_y))
            config.hp_width := Integer(IniRead(this.configFile, "BloodDetection", "width", config.hp_width))
            config.hp_height := Integer(IniRead(this.configFile, "BloodDetection", "height", config.hp_height))
            config.hp_enabled := (IniRead(this.configFile, "BloodDetection", "enabled", config.hp_enabled) = "true")
            config.drink_key := IniRead(this.configFile, "Keys", "drink_key", config.drink_key)
            config.skill_key := IniRead(this.configFile, "Keys", "skill_key", config.skill_key)
            config.drink_cooldown := Integer(IniRead(this.configFile, "Timing", "drink_cooldown", config.drink_cooldown))
            config.skill_interval := Integer(IniRead(this.configFile, "Timing", "skill_interval", config.skill_interval))
            config.skill_hold_time := Integer(IniRead(this.configFile, "Timing", "skill_hold_time", config.skill_hold_time))
            config.detection_interval := Integer(IniRead(this.configFile, "Timing", "detection_interval", config.detection_interval))
            config.drink_threshold := Integer(IniRead(this.configFile, "Thresholds", "drink_threshold", config.drink_threshold))
            config.skill_stop_threshold := Integer(IniRead(this.configFile, "Thresholds", "skill_stop_threshold", config.skill_stop_threshold))
        } catch Error as e {
            ; 使用默认配置
        }
        
        return config
    }
    
    static SaveConfig(config) {
        try {
            IniWrite(config.hp_x, this.configFile, "BloodDetection", "x")
            IniWrite(config.hp_y, this.configFile, "BloodDetection", "y")
            IniWrite(config.hp_width, this.configFile, "BloodDetection", "width")
            IniWrite(config.hp_height, this.configFile, "BloodDetection", "height")
            IniWrite(config.hp_enabled ? "true" : "false", this.configFile, "BloodDetection", "enabled")
            IniWrite(config.drink_key, this.configFile, "Keys", "drink_key")
            IniWrite(config.skill_key, this.configFile, "Keys", "skill_key")
            IniWrite(config.drink_cooldown, this.configFile, "Timing", "drink_cooldown")
            IniWrite(config.skill_interval, this.configFile, "Timing", "skill_interval")
            IniWrite(config.skill_hold_time, this.configFile, "Timing", "skill_hold_time")
            IniWrite(config.detection_interval, this.configFile, "Timing", "detection_interval")
            IniWrite(config.drink_threshold, this.configFile, "Thresholds", "drink_threshold")
            IniWrite(config.skill_stop_threshold, this.configFile, "Thresholds", "skill_stop_threshold")
        } catch Error as e {
            ; 忽略保存错误
        }
    }
    
    static AutoAdjustForResolution(config) {
        baseWidth := 1920
        baseHeight := 1080
        currentWidth := A_ScreenWidth
        currentHeight := A_ScreenHeight
        
        if (currentWidth = baseWidth && currentHeight = baseHeight) {
            return config
        }
        
        scaleX := currentWidth / baseWidth
        scaleY := currentHeight / baseHeight
        
        config.hp_x := Round(config.hp_x * scaleX)
        config.hp_y := Round(config.hp_y * scaleY)
        config.hp_width := Round(config.hp_width * scaleX)
        config.hp_height := Round(config.hp_height * scaleY)
        
        return config
    }
}

; ===================================================================
; 全局变量
; ===================================================================
global ghubInitialized := false
global isRunning := false
global mainGui := ""
global hpDetectionEnabled := false
global lastHP := 100
global skillReleaseActive := false
global lastDrinkTime := 0
global drinkCooldown := 2000
global lastSkillTime := 0
global skillInterval := 800
global skillHoldTime := 80
global skillKeyPressed := false
global poe2WindowActive := false

; 缓存配置
global cachedDrinkKey := "4"
global cachedSkillKey := "5"
global cachedDrinkCooldown := 2000
global cachedSkillInterval := 800
global cachedSkillHoldTime := 80

; 血量检测配置
global hpConfig := {
    x: 1760,
    y: 865,
    width: 25,
    height: 180,
    scanX: 1772,
    topY: 865,
    bottomY: 1045
}

; 性能监控
global performanceStats := {
    hpDetectTime: 0,
    keyActionTime: 0,
    totalCycles: 0
}

; ===================================================================
; 优化的日志系统
; ===================================================================
class LogManager {
    static buffer := []
    static maxBufferSize := 20
    static lastFlushTime := 0
    static flushInterval := 1000  ; 1秒刷新一次
    
    static Add(text) {
        currentTime := FormatTime(, "HH:mm:ss")
        this.buffer.Push("[" . currentTime . "] " . text)
        
        ; 自动刷新
        if (this.buffer.Length >= this.maxBufferSize || 
            A_TickCount - this.lastFlushTime > this.flushInterval) {
            this.Flush()
        }
    }
    
    static Flush() {
        global mainGui
        if (!this.buffer.Length || !IsSet(mainGui) || !mainGui.HasProp("infoText"))
            return
            
        ; 批量更新
        newText := ""
        for item in this.buffer {
            newText .= item . "`r`n"
        }
        
        ; 保持最新100行
        existingLines := StrSplit(mainGui.infoText.Text, "`n")
        totalLines := this.buffer.Length + existingLines.Length
        
        if (totalLines > 100) {
            keepLines := 100 - this.buffer.Length
            if (keepLines > 0) {
                oldText := ""
                Loop keepLines {
                    if (A_Index <= existingLines.Length) {
                        oldText .= existingLines[A_Index] . "`n"
                    }
                }
                mainGui.infoText.Text := newText . oldText
            } else {
                mainGui.infoText.Text := newText
            }
        } else {
            mainGui.infoText.Text := newText . mainGui.infoText.Text
        }
        
        this.buffer := []
        this.lastFlushTime := A_TickCount
    }
}

; 简化的AddInfo函数
AddInfo(text) {
    LogManager.Add(text)
}

FlushLogBuffer() {
    LogManager.Flush()
}

; ===================================================================
; 优化的颜色检测 - 移除HSV转换，使用简单RGB
; ===================================================================
IsRedColorFast(x, y) {
    static cache := Map()
    static cacheTime := A_TickCount
    
    ; 缓存键
    key := x . "," . y
    
    ; 缓存有效期500ms
    if (cache.Has(key) && A_TickCount - cacheTime < 500) {
        return cache[key]
    }
    
    ; 清理缓存（每100次调用）
    static cleanCount := 0
    if (++cleanCount > 100) {
        cache.Clear()
        cleanCount := 0
        cacheTime := A_TickCount
    }
    
    ; 获取颜色
    c := PixelGetColor(x, y)
    r := (c >> 16) & 0xFF
    g := (c >> 8) & 0xFF
    b := c & 0xFF
    
    ; 简化的红色判定
    result := false
    if (r >= 50 && r > g + 10 && r > b + 10) {
        ; 排除白色和灰色
        if (!(r > 230 && g > 230 && b > 230) && 
            !(Abs(r - g) < 10 && Abs(r - b) < 10)) {
            result := true
        }
    }
    
    cache[key] := result
    return result
}

; ===================================================================
; 优化的血量检测 - 采样检测
; ===================================================================
GetHPOptimized() {
    static lastResult := 100.0
    static lastDetectTime := 0
    
    ; 限制检测频率
    currentTime := A_TickCount
    if (currentTime - lastDetectTime < 100) {
        return lastResult
    }
    lastDetectTime := currentTime
    
    try {
        startTime := A_TickCount
        
        scanX := hpConfig.scanX
        topY := hpConfig.topY
        bottomY := hpConfig.bottomY
        totalHeight := bottomY - topY + 1
        
        ; 采样检测 - 每5个像素检测一次
        sampleStep := 5
        redCount := 0
        sampleCount := 0
        
        y := topY
        while (y <= bottomY) {
            ; 权重计算
            relPos := (y - topY) / totalHeight
            weight := (relPos < 0.1 || relPos > 0.9) ? 0.3 : 1.0
            
            if (IsRedColorFast(scanX, y)) {
                redCount += weight
            }
            sampleCount += weight
            
            y += sampleStep
        }
        
        ; 计算血量
        if (sampleCount > 0) {
            hp := (redCount / sampleCount) * 100
            lastResult := Round(hp, 1)
        } else {
            lastResult := 0.0
        }
        
        ; 性能统计
        global performanceStats
        performanceStats.hpDetectTime := A_TickCount - startTime
        
        return lastResult
        
    } catch {
        return lastResult
    }
}

; ===================================================================
; 优化的窗口检测
; ===================================================================
IsPOE2ActiveCached() {
    static lastCheck := 0
    static lastResult := false
    
    ; 2秒缓存
    if (A_TickCount - lastCheck < 2000) {
        return lastResult
    }
    
    try {
        title := WinGetTitle("A")
        lastResult := (InStr(title, "Path of Exile") || InStr(title, "POE2"))
        lastCheck := A_TickCount
    } catch {
        lastResult := false
    }
    
    return lastResult
}

; ===================================================================
; 分离的定时器函数
; ===================================================================

; 血量检测定时器 - 200ms
HPDetectionTimer() {
    global hpDetectionEnabled, lastHP
    
    if (!hpDetectionEnabled)
        return
        
    try {
        newHP := GetHPOptimized()
        if (Abs(newHP - lastHP) > 1.0) {
            lastHP := newHP
            ; 延迟更新GUI
            SetTimer(() => UpdateHPDisplay(lastHP), -1)
        }
    } catch {
        ; 忽略错误
    }
}

; 按键操作定时器 - 300ms
KeyActionTimer() {
    global isRunning, hpDetectionEnabled, poe2WindowActive
    
    if (!isRunning || !hpDetectionEnabled)
        return
        
    ; 检查窗口状态
    poe2WindowActive := IsPOE2ActiveCached()
    if (!poe2WindowActive)
        return
        
    ; 处理按键
    ProcessKeyActionsOptimized()
}

; GUI更新定时器 - 1000ms
GUIUpdateTimer() {
    global performanceStats, mainGui
    
    ; 刷新日志
    FlushLogBuffer()
    
    ; 性能监控（每30秒）
    static lastPerfLog := 0
    if (A_TickCount - lastPerfLog > 30000) {
        performanceStats.totalCycles++
        avgHPTime := performanceStats.hpDetectTime
        AddInfo("📊 性能: 血量检测 " . avgHPTime . "ms")
        lastPerfLog := A_TickCount
    }
}

; ===================================================================
; 优化的按键处理
; ===================================================================
ProcessKeyActionsOptimized() {
    global lastHP, skillReleaseActive, lastDrinkTime, lastSkillTime
    global cachedDrinkKey, cachedSkillKey, cachedDrinkCooldown
    global cachedSkillInterval, cachedSkillHoldTime
    
    currentTime := A_TickCount
    
    ; 简化的状态处理
    static drinkReady := true
    static skillPhase := 0  ; 0=idle, 1=pressing, 2=waiting
    static phaseStartTime := 0
    
    ; 喝药逻辑
    if (drinkReady && lastHP < 75 && currentTime - lastDrinkTime > cachedDrinkCooldown) {
        try {
            IbSend("{" . cachedDrinkKey . " down}")
            SetTimer(() => IbSend("{" . cachedDrinkKey . " up}"), -80)
            lastDrinkTime := currentTime
            drinkReady := false
            SetTimer(() => (drinkReady := true), -1000)
            AddInfo("💊 自动喝药: " . lastHP . "%")
        } catch {
            ; 忽略错误
        }
    }
    
    ; 技能逻辑
    if (lastHP > 30) {
        if (!skillReleaseActive) {
            skillReleaseActive := true
            skillPhase := 0
        }
        
        switch skillPhase {
            case 0:  ; idle -> start
                try {
                    IbSend("{" . cachedSkillKey . " down}")
                    skillPhase := 1
                    phaseStartTime := currentTime
                } catch {
                    ; 忽略错误
                }
                
            case 1:  ; pressing -> release
                if (currentTime - phaseStartTime >= cachedSkillHoldTime) {
                    try {
                        IbSend("{" . cachedSkillKey . " up}")
                        skillPhase := 2
                        phaseStartTime := currentTime
                    } catch {
                        ; 忽略错误
                    }
                }
                
            case 2:  ; waiting -> restart
                if (currentTime - phaseStartTime >= cachedSkillInterval) {
                    skillPhase := 0
                }
        }
    } else {
        if (skillReleaseActive) {
            skillReleaseActive := false
            skillPhase := 0
            try {
                IbSend("{" . cachedSkillKey . " up}")
            } catch {
                ; 忽略错误
            }
            AddInfo("🛡️ 停止技能: 血量" . lastHP . "%")
        }
    }
}

; ===================================================================
; 启动/停止系统 - 使用分离的定时器
; ===================================================================
ToggleSystem(*) {
    global isRunning, ghubInitialized, mainGui, hpDetectionEnabled
    
    if (!ghubInitialized) {
        MsgBox("请先初始化 Logitech G HUB 驱动！", "错误", 16)
        return
    }
    
    if (!isRunning) {
        isRunning := true
        mainGui.toggleBtn.Text := "⏹️ 停止系统"
        
        UpdateCachedConfig()
        
        ; 启动分离的定时器
        SetTimer(HPDetectionTimer, 200)
        SetTimer(KeyActionTimer, 300)
        SetTimer(GUIUpdateTimer, 1000)
        
        AddInfo("▶️ 系统已启动 (优化版)")
        
    } else {
        isRunning := false
        mainGui.toggleBtn.Text := "▶️ 启动系统"
        
        ; 停止所有定时器
        SetTimer(HPDetectionTimer, 0)
        SetTimer(KeyActionTimer, 0)
        SetTimer(GUIUpdateTimer, 0)
        
        ; 释放按键
        try {
            IbSend("{" . cachedDrinkKey . " up}")
            IbSend("{" . cachedSkillKey . " up}")
        } catch {
            ; 忽略错误
        }
        
        AddInfo("⏹️ 系统已停止")
    }
}

; ===================================================================
; 其他必要的回调函数（简化版）
; ===================================================================

InitializeGHUB(*) {
    global ghubInitialized, mainGui
    
    AddInfo("正在初始化 Logitech G HUB 驱动...")
    
    try {
        IbSendInit("LogitechGHubNew", 1)
        ghubInitialized := true
        mainGui.statusLabel.Text := "● Logitech G HUB 已初始化"
        mainGui.statusLabel.Opt("cGreen")
        AddInfo("✅ GHUB 驱动初始化成功")
    } catch Error as e {
        AddInfo("❌ 初始化失败: " . e.Message)
        MsgBox("Logitech G HUB 驱动初始化失败！", "错误", 16)
    }
}

TestKeyInput(*) {
    global ghubInitialized, mainGui

    if (!ghubInitialized) {
        MsgBox("请先初始化 Logitech G HUB 驱动！", "错误", 16)
        return
    }

    drinkKey := mainGui.drinkKeyEdit.Text
    skillKey := mainGui.skillKeyEdit.Text

    AddInfo("🧪 测试按键: " . drinkKey . " 和 " . skillKey)
    
    SetTimer(() => (
        IbSend(drinkKey),
        Sleep(500),
        IbSend(skillKey),
        AddInfo("✅ 测试完成")
    ), -3000)
}

ToggleHPDetection(*) {
    global hpDetectionEnabled, mainGui
    
    hpDetectionEnabled := mainGui.hpEnableChk.Value
    
    if (hpDetectionEnabled) {
        AddInfo("🩸 血量检测已启用")
        if (!isRunning) {
            SetTimer(HPDetectionTimer, 200)
        }
    } else {
        AddInfo("🩸 血量检测已禁用")
        if (!isRunning) {
            SetTimer(HPDetectionTimer, 0)
        }
    }
}

UpdateCachedConfig(*) {
    global cachedDrinkKey, cachedSkillKey, cachedDrinkCooldown
    global cachedSkillInterval, cachedSkillHoldTime, mainGui
    
    try {
        cachedDrinkKey := mainGui.drinkKeyEdit.Text
        cachedSkillKey := mainGui.skillKeyEdit.Text
        cachedDrinkCooldown := Integer(mainGui.cooldownEdit.Text)
        cachedSkillInterval := Integer(mainGui.skillIntervalEdit.Text)
        cachedSkillHoldTime := Integer(mainGui.skillHoldEdit.Text)
        
        AddInfo("⚙️ 配置已更新")
    } catch Error as e {
        AddInfo("❌ 配置更新失败: " . e.Message)
    }
}

UpdateHPDisplay(hp) {
    global mainGui
    
    color := hp <= 30 ? "cRed" : hp <= 75 ? "c0xFF8000" : "cGreen"
    mainGui.hpText.Text := Format("{:.1f}%", hp)
    mainGui.hpText.Opt(color)
}

ClearLog(*) {
    global mainGui
    mainGui.infoText.Text := ""
    LogManager.buffer := []
    AddInfo("🗑️ 日志已清空")
}

; 简化的其他回调函数
DrawHPArea(*) {
    global hpConfig
    AddInfo("🔲 绘制功能暂时简化")
}

TestHPDetection(*) {
    global hpDetectionEnabled
    if (!hpDetectionEnabled) {
        MsgBox("请先启用血量检测！", "提示", 48)
        return
    }
    hp := GetHPOptimized()
    AddInfo("🧪 当前血量: " . hp . "%")
}

ApplyHPSettings(*) {
    global hpConfig, mainGui
    
    try {
        hpConfig.x := Integer(mainGui.hpXEdit.Text)
        hpConfig.y := Integer(mainGui.hpYEdit.Text)
        hpConfig.width := Integer(mainGui.hpWEdit.Text)
        hpConfig.height := Integer(mainGui.hpHEdit.Text)
        hpConfig.scanX := Round(hpConfig.x + (hpConfig.width / 2))
        hpConfig.topY := hpConfig.y
        hpConfig.bottomY := hpConfig.y + hpConfig.height
        
        AddInfo("✅ 血量检测设置已更新")
    } catch Error as e {
        MsgBox("❌ 设置错误: " . e.message, "错误", 16)
    }
}

GetMousePosition(*) {
    AddInfo("🖱️ 5秒后检测...")
    SetTimer(() => (
        MouseGetPos(&x, &y),
        AddInfo("🎯 鼠标位置: X=" . x . " Y=" . y)
    ), -5000)
}

AutoDetectHPBar(*) {
    AddInfo("🔍 自动检测功能暂时简化")
}

QuickHPTest(*) {
    TestHPDetection()
}

DebugWindowDetection(*) {
    try {
        title := WinGetTitle("A")
        process := WinGetProcessName("A")
        isPOE2 := IsPOE2ActiveCached()
        AddInfo("🔍 窗口: " . title)
        AddInfo("   进程: " . process)
        AddInfo("   POE2: " . (isPOE2 ? "✅" : "❌"))
    } catch {
        AddInfo("❌ 窗口检测失败")
    }
}

SaveCurrentConfig(*) {
    global appConfig, ConfigManager
    UpdateCachedConfig()
    ConfigManager.SaveConfig(appConfig)
    AddInfo("✅ 配置已保存")
}

; ===================================================================
; 创建GUI界面（简化版）
; ===================================================================
CreateGUI() {
    myGui := Gui("+Resize +MinSize750x650", "POE2自动喝药 v2.0 (优化版)")
    myGui.SetFont("s9", "Microsoft YaHei")

    ; 标题
    myGui.AddText("x10 y10 w400 h30 Center", "🎮 POE2自动喝药 + GHUB硬件按键发送器").SetFont("s12 Bold")

    ; 驱动状态
    myGui.AddText("x10 y50 w100", "GHUB驱动:")
    statusLabel := myGui.AddText("x120 y50 w280 cRed", "● 未初始化")

    ; 血量检测区域
    myGui.AddText("x10 y90 w400 h20 Center", "🩸 血量检测与自动控制").SetFont("s11 Bold")

    ; 血量显示
    myGui.AddText("x20 y120 w80 h25", "当前血量:")
    hpText := myGui.AddText("x110 y120 w100 h25 c0x008000 Center Border", "100.0%")
    hpText.SetFont("s12 Bold")

    ; 血量检测开关
    hpEnableChk := myGui.AddCheckbox("x230 y120 w140 h25", "启用血量检测")
    hpEnableChk.OnEvent("Click", ToggleHPDetection)

    ; 血量检测区域配置
    myGui.AddText("x20 y180 w25 h20", "X:")
    hpXEdit := myGui.AddEdit("x45 y177 w50 h23 Number", hpConfig.x)
    myGui.AddText("x105 y180 w25 h20", "Y:")
    hpYEdit := myGui.AddEdit("x130 y177 w50 h23 Number", hpConfig.y)
    myGui.AddText("x190 y180 w25 h20", "W:")
    hpWEdit := myGui.AddEdit("x215 y177 w40 h23 Number", hpConfig.width)
    myGui.AddText("x265 y180 w25 h20", "H:")
    hpHEdit := myGui.AddEdit("x290 y177 w40 h23 Number", hpConfig.height)

    ; 检测按钮
    testHPBtn := myGui.AddButton("x20 y205 w80 h25", "🧪 测试检测")
    testHPBtn.OnEvent("Click", TestHPDetection)
    applyHPBtn := myGui.AddButton("x110 y205 w80 h25", "✅ 应用设置")
    applyHPBtn.OnEvent("Click", ApplyHPSettings)
    mouseBtn := myGui.AddButton("x200 y205 w80 h25", "🖱️ 鼠标位置")
    mouseBtn.OnEvent("Click", GetMousePosition)

    ; 控制按钮
    myGui.AddText("x10 y280 w400 h20 Center", "🎮 GHUB控制面板").SetFont("s11 Bold")

    initBtn := myGui.AddButton("x20 y310 w110 h35", "🔧 初始化GHUB")
    initBtn.OnEvent("Click", InitializeGHUB)
    testBtn := myGui.AddButton("x140 y310 w110 h35", "🧪 测试按键")
    testBtn.OnEvent("Click", TestKeyInput)
    toggleBtn := myGui.AddButton("x260 y310 w110 h35", "▶️ 启动系统")
    toggleBtn.OnEvent("Click", ToggleSystem)

    ; 按键设置
    myGui.AddText("x10 y355 w400 h20 Center", "⚙️ 按键设置").SetFont("s11 Bold")

    myGui.AddText("x20 y380 w80", "喝药按键:")
    drinkKeyEdit := myGui.AddEdit("x110 y377 w40 h23", cachedDrinkKey)
    myGui.AddText("x160 y380 w80", "技能按键:")
    skillKeyEdit := myGui.AddEdit("x250 y377 w40 h23", cachedSkillKey)

    myGui.AddText("x20 y425 w100", "喝药冷却(ms):")
    cooldownEdit := myGui.AddEdit("x130 y422 w60 h23", cachedDrinkCooldown)
    
    myGui.AddText("x20 y450 w100", "技能按下(ms):")
    skillHoldEdit := myGui.AddEdit("x130 y447 w60 h23", cachedSkillHoldTime)
    myGui.AddText("x200 y450 w80", "技能间隔(ms):")
    skillIntervalEdit := myGui.AddEdit("x290 y447 w60 h23", cachedSkillInterval)

    updateConfigBtn := myGui.AddButton("x200 y472 w80 h23", "🔄 更新")
    updateConfigBtn.OnEvent("Click", UpdateCachedConfig)
    saveConfigBtn := myGui.AddButton("x290 y472 w60 h23", "💾 保存")
    saveConfigBtn.OnEvent("Click", SaveCurrentConfig)

    ; 日志区域
    myGui.AddText("x420 y50 w250 h20 Center", "📝 运行日志").SetFont("s11 Bold")
    clearLogBtn := myGui.AddButton("x680 y50 w60 h20", "🗑️ 清空")
    clearLogBtn.OnEvent("Click", ClearLog)
    infoText := myGui.AddEdit("x420 y75 w320 h420 ReadOnly VScroll")

    ; 存储控件引用
    myGui.statusLabel := statusLabel
    myGui.toggleBtn := toggleBtn
    myGui.infoText := infoText
    myGui.hpText := hpText
    myGui.hpEnableChk := hpEnableChk
    myGui.hpXEdit := hpXEdit
    myGui.hpYEdit := hpYEdit
    myGui.hpWEdit := hpWEdit
    myGui.hpHEdit := hpHEdit
    myGui.drinkKeyEdit := drinkKeyEdit
    myGui.skillKeyEdit := skillKeyEdit
    myGui.cooldownEdit := cooldownEdit
    myGui.skillHoldEdit := skillHoldEdit
    myGui.skillIntervalEdit := skillIntervalEdit

    myGui.OnEvent("Close", (*) => (FlushLogBuffer(), ExitApp()))

    return myGui
}

; ===================================================================
; 快捷键
; ===================================================================
F1::ToggleSystem()
F2::TestKeyInput()
F3::InitializeGHUB()
F4::MsgBox("系统状态简化显示", "状态", 64)
F12::ExitApp()

; ===================================================================
; 程序入口
; ===================================================================
if (!A_IsAdmin) {
    MsgBox("需要管理员权限！", "错误", 48)
    ExitApp()
}

; 加载配置
global appConfig := ConfigManager.LoadConfig()
appConfig := ConfigManager.AutoAdjustForResolution(appConfig)

; 初始化配置
hpDetectionEnabled := appConfig.hp_enabled
hpConfig.x := appConfig.hp_x
hpConfig.y := appConfig.hp_y
hpConfig.width := appConfig.hp_width
hpConfig.height := appConfig.hp_height
hpConfig.scanX := Round(appConfig.hp_x + (appConfig.hp_width / 2))
hpConfig.topY := appConfig.hp_y
hpConfig.bottomY := appConfig.hp_y + appConfig.hp_height

cachedDrinkKey := appConfig.drink_key
cachedSkillKey := appConfig.skill_key
cachedDrinkCooldown := appConfig.drink_cooldown
cachedSkillInterval := appConfig.skill_interval
cachedSkillHoldTime := appConfig.skill_hold_time

; 创建GUI
mainGui := CreateGUI()
mainGui.Show("w750 h540")

AddInfo("🚀 POE2自动喝药 v2.0 (性能优化版)")
AddInfo("⚡ 优化特性:")
AddInfo("   ✅ 分离定时器架构")
AddInfo("   ✅ 采样血量检测")
AddInfo("   ✅ 简化颜色算法")
AddInfo("   ✅ 智能缓存机制")
AddInfo("   ✅ 批量日志更新")
AddInfo("💡 请先初始化GHUB驱动")