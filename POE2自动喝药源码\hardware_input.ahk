#SingleInstance Force

; ===================================================================
; POE2 自动喝药 - 硬件输入端 (AHK)
; 负责硬件级按键触发和reWASD兼容
; 通过命名管道与Python检测端通信
; ===================================================================

; 包含 IbInputSimulator 库
#Include "..\IbInputSimulator\IbInputSimulator.ahk"

; ===================================================================
; 硬件输入管理器
; ===================================================================
class HardwareInputManager {
    ; 构造函数
    __New() {
        this.pipe := ""
        this.ghubInitialized := false
        this.running := false
        this.lastDrinkTime := {health: 0, mana: 0, shield: 0}
        this.config := this.LoadConfig()
        this.gui := ""
        
        ; 初始化组件
        this.InitializeGHUB()
        this.CreateGUI()
        this.StartPipeListener()
        
        this.AddLog("硬件输入管理器初始化完成")
    }
    
    ; 加载配置
    LoadConfig() {
        configFile := A_ScriptDir . "\hybrid_config.yaml"
        
        ; 默认配置
        config := {
            health: {potionKey: "1", cooldown: 2000},
            mana: {potionKey: "2", cooldown: 1500},
            shield: {potionKey: "3", cooldown: 1000},
            pipe: {name: "\\\\.\\pipe\\poe2_auto_drink", timeout: 1000}
        }
        
        ; TODO: 实际从YAML文件读取配置
        ; 这里使用默认配置
        
        return config
    }
    
    ; 初始化GHUB驱动
    InitializeGHUB() {
        try {
            ; 使用 LogitechGHubNew 驱动，模式1（接管AHK输入）
            IbSendInit("LogitechGHubNew", 1)
            this.ghubInitialized := true
            
            this.AddLog("✅ Logitech G HUB 驱动初始化成功")
            this.AddLog("📋 驱动信息: LogitechGHubNew (硬件级输入)")
            this.AddLog("🎯 reWASD 现在可以识别并重映射这些按键")
            
            ; 同步按键状态
            try {
                IbSyncKeyStates()
                this.AddLog("🔄 按键状态已同步")
            } catch {
                this.AddLog("⚠️ 按键状态同步失败（可忽略）")
            }
            
        } catch Error as e {
            this.AddLog("❌ Logitech G HUB 驱动初始化失败: " . e.Message)
            this.AddLog("💡 请确保:")
            this.AddLog("   1. 以管理员身份运行此脚本")
            this.AddLog("   2. 已安装 Logitech G HUB 软件")
            this.AddLog("   3. G HUB 软件正在运行")
            
            MsgBox("Logitech G HUB 驱动初始化失败！`n`n" .
                   "错误信息: " . e.Message . "`n`n" .
                   "请确保:`n" .
                   "1. 以管理员身份运行此脚本`n" .
                   "2. 已安装 Logitech G HUB 软件`n" .
                   "3. G HUB 软件正在运行", "初始化失败", 16)
        }
    }
    
    ; 创建GUI界面
    CreateGUI() {
        this.gui := Gui("+Resize +MinSize400x300", "POE2 硬件输入端")
        
        ; 状态显示
        this.gui.Add("Text", "x10 y10 w200 h20", "GHUB状态:")
        this.statusLabel := this.gui.Add("Text", "x120 y10 w200 h20 cRed", "● 未初始化")
        
        this.gui.Add("Text", "x10 y35 w200 h20", "管道状态:")
        this.pipeLabel := this.gui.Add("Text", "x120 y35 w200 h20 cRed", "● 未连接")
        
        ; 按键配置
        this.gui.Add("GroupBox", "x10 y65 w380 h100", "按键配置")
        
        this.gui.Add("Text", "x20 y85 w60 h20", "血量药剂:")
        this.healthKeyEdit := this.gui.Add("Edit", "x85 y83 w30 h20", this.config.health.potionKey)
        this.gui.Add("Text", "x125 y85 w60 h20", "冷却(ms):")
        this.healthCooldownEdit := this.gui.Add("Edit", "x190 y83 w50 h20", this.config.health.cooldown)
        
        this.gui.Add("Text", "x20 y110 w60 h20", "蓝量药剂:")
        this.manaKeyEdit := this.gui.Add("Edit", "x85 y108 w30 h20", this.config.mana.potionKey)
        this.gui.Add("Text", "x125 y110 w60 h20", "冷却(ms):")
        this.manaCooldownEdit := this.gui.Add("Edit", "x190 y108 w50 h20", this.config.mana.cooldown)
        
        this.gui.Add("Text", "x20 y135 w60 h20", "护盾药剂:")
        this.shieldKeyEdit := this.gui.Add("Edit", "x85 y133 w30 h20", this.config.shield.potionKey)
        this.gui.Add("Text", "x125 y135 w60 h20", "冷却(ms):")
        this.shieldCooldownEdit := this.gui.Add("Edit", "x190 y133 w50 h20", this.config.shield.cooldown)
        
        ; 控制按钮
        this.startBtn := this.gui.Add("Button", "x10 y175 w80 h30", "启动服务")
        this.startBtn.OnEvent("Click", (*) => this.Start())
        
        this.stopBtn := this.gui.Add("Button", "x100 y175 w80 h30 Disabled", "停止服务")
        this.stopBtn.OnEvent("Click", (*) => this.Stop())
        
        this.testBtn := this.gui.Add("Button", "x190 y175 w80 h30", "测试按键")
        this.testBtn.OnEvent("Click", (*) => this.TestKeys())
        
        this.saveBtn := this.gui.Add("Button", "x280 y175 w80 h30", "保存配置")
        this.saveBtn.OnEvent("Click", (*) => this.SaveConfig())
        
        ; 日志区域
        this.gui.Add("Text", "x10 y215 w100 h20", "运行日志:")
        this.logEdit := this.gui.Add("Edit", "x10 y235 w380 h100 ReadOnly VScroll")
        
        ; 更新状态显示
        this.UpdateGUIStatus()
        
        ; 显示GUI
        this.gui.Show()
        
        ; 设置关闭事件
        this.gui.OnEvent("Close", (*) => this.OnClose())
    }
    
    ; 更新GUI状态
    UpdateGUIStatus() {
        if (this.ghubInitialized) {
            this.statusLabel.Text := "● 已初始化"
            this.statusLabel.Opt("cGreen")
        } else {
            this.statusLabel.Text := "● 初始化失败"
            this.statusLabel.Opt("cRed")
        }
        
        if (this.pipe) {
            this.pipeLabel.Text := "● 已连接"
            this.pipeLabel.Opt("cGreen")
        } else {
            this.pipeLabel.Text := "● 未连接"
            this.pipeLabel.Opt("cRed")
        }
    }
    
    ; 启动管道监听
    StartPipeListener() {
        ; 使用定时器检查管道连接
        SetTimer(() => this.CheckPipeConnection(), 1000)
        
        ; 启动消息处理循环
        SetTimer(() => this.ProcessPipeMessages(), 10)
    }
    
    ; 检查管道连接
    CheckPipeConnection() {
        if (!this.pipe && this.running) {
            try {
                ; 尝试连接到Python端创建的管道
                pipeName := this.config.pipe.name
                this.AddLog("🔍 尝试连接管道: " . pipeName)

                ; 等待管道可用
                if (!this.WaitForPipe(pipeName, 3000)) {
                    this.AddLog("⏰ 管道不可用，等待Python端创建...")
                    return
                }

                this.AddLog("✅ 管道可用，尝试连接...")

                ; 使用正确的参数连接命名管道
                this.pipe := DllCall("CreateFile",
                    "Str", pipeName,
                    "UInt", 0xC0000000,  ; GENERIC_READ | GENERIC_WRITE
                    "UInt", 0,           ; 不共享
                    "Ptr", 0,            ; 默认安全属性
                    "UInt", 3,           ; OPEN_EXISTING
                    "UInt", 0,           ; 默认属性
                    "Ptr", 0,            ; 无模板文件
                    "Ptr")

                ; CreateFile返回INVALID_HANDLE_VALUE (-1)表示失败
                if (this.pipe != -1 && this.pipe != 0) {
                    this.AddLog("✅ 管道连接成功: " . pipeName)
                    this.UpdateGUIStatus()

                    ; 发送连接确认
                    this.SendConnectionConfirm()
                } else {
                    ; 获取错误信息
                    errorCode := DllCall("GetLastError")
                    this.AddLog("❌ 管道连接失败，错误代码: " . errorCode)

                    ; 常见错误代码说明
                    switch errorCode {
                        case 2:
                            this.AddLog("💡 错误说明: 管道不存在，等待Python端创建")
                        case 161:
                            this.AddLog("💡 错误说明: 管道路径无效")
                        case 231:
                            this.AddLog("💡 错误说明: 管道忙碌，稍后重试")
                        default:
                            this.AddLog("💡 错误说明: 未知错误 " . errorCode)
                    }

                    this.pipe := ""
                }
            } catch Error as e {
                ; 连接失败，继续尝试
                this.AddLog("❌ 管道连接异常: " . e.Message)
                this.pipe := ""
            }
        }
    }

    ; 等待管道可用
    WaitForPipe(pipeName, timeout) {
        return DllCall("WaitNamedPipe", "Str", pipeName, "UInt", timeout)
    }

    ; 发送连接确认
    SendConnectionConfirm() {
        try {
            ; 发送简单的确认消息
            confirmMsg := "AHK_CONNECTED"
            bytesWritten := 0
            result := DllCall("WriteFile",
                "Ptr", this.pipe,
                "AStr", confirmMsg,
                "UInt", StrLen(confirmMsg),
                "UIntP", &bytesWritten,
                "Ptr", 0)

            if (result) {
                this.AddLog("📤 发送连接确认成功")
            } else {
                this.AddLog("❌ 发送连接确认失败")
            }
        } catch Error as e {
            this.AddLog("❌ 发送连接确认异常: " . e.Message)
        }
    }
    
    ; 处理管道消息
    ProcessPipeMessages() {
        if (!this.pipe || !this.running)
            return
        
        try {
            ; 读取命令数据
            buffer := Buffer(8)
            bytesRead := 0
            
            result := DllCall("ReadFile", "Ptr", this.pipe, "Ptr", buffer.Ptr, "UInt", 8, "UIntP", &bytesRead, "Ptr", 0)
            
            if (result && bytesRead >= 8) {
                ; 解析命令
                cmdId := NumGet(buffer, 0, "UChar")
                resourceType := NumGet(buffer, 1, "UChar")
                value := NumGet(buffer, 2, "Float")
                checksum := NumGet(buffer, 6, "UShort")
                
                ; 处理命令
                this.ProcessCommand(cmdId, resourceType, value)
                
                ; 发送响应
                this.SendResponse(0x01)  ; 成功响应
            }
        } catch {
            ; 读取失败，可能是管道断开
            this.pipe := ""
            this.UpdateGUIStatus()
        }
    }
    
    ; 处理命令
    ProcessCommand(cmdId, resourceType, value) {
        if (cmdId == 0x01) {  ; 使用药剂命令
            resourceName := ""
            switch resourceType {
                case 0x01: resourceName := "health"
                case 0x02: resourceName := "mana"
                case 0x03: resourceName := "shield"
                default: return
            }
            
            this.UsePotionCommand(resourceName, value)
        }
    }
    
    ; 执行使用药剂命令
    UsePotionCommand(resourceType, percentage) {
        if (!this.ghubInitialized) {
            this.AddLog("❌ GHUB未初始化，无法发送按键")
            return
        }
        
        currentTime := A_TickCount
        lastTime := this.lastDrinkTime.%resourceType%
        cooldown := this.config.%resourceType%.cooldown
        
        ; 检查冷却时间
        if (currentTime - lastTime < cooldown) {
            this.AddLog("⏰ " . resourceType . " 冷却中，剩余: " . (cooldown - (currentTime - lastTime)) . "ms")
            return
        }
        
        ; 发送硬件按键
        key := this.config.%resourceType%.potionKey
        try {
            IbSend(key)
            this.lastDrinkTime.%resourceType% := currentTime
            this.AddLog("💊 发送" . resourceType . "药剂按键: " . key . " (" . percentage . "%)")
        } catch Error as e {
            this.AddLog("❌ 按键发送失败: " . e.Message)
        }
    }
    
    ; 发送响应
    SendResponse(status) {
        if (!this.pipe)
            return
        
        try {
            buffer := Buffer(4)
            NumPut("UInt", status, buffer, 0)
            
            bytesWritten := 0
            DllCall("WriteFile", "Ptr", this.pipe, "Ptr", buffer.Ptr, "UInt", 4, "UIntP", &bytesWritten, "Ptr", 0)
        } catch {
            ; 发送失败
        }
    }
    
    ; 启动服务
    Start() {
        if (this.running) {
            this.AddLog("⚠️ 服务已在运行")
            return
        }
        
        this.running := true
        this.startBtn.Enabled := false
        this.stopBtn.Enabled := true
        
        this.AddLog("🚀 硬件输入服务已启动")
        this.AddLog("📡 等待Python检测端连接...")
    }
    
    ; 停止服务
    Stop() {
        if (!this.running) {
            return
        }
        
        this.running := false
        this.startBtn.Enabled := true
        this.stopBtn.Enabled := false
        
        ; 关闭管道
        if (this.pipe) {
            DllCall("CloseHandle", "Ptr", this.pipe)
            this.pipe := ""
        }
        
        this.AddLog("🛑 硬件输入服务已停止")
        this.UpdateGUIStatus()
    }
    
    ; 测试按键
    TestKeys() {
        if (!this.ghubInitialized) {
            MsgBox("请先初始化 Logitech G HUB 驱动！", "错误", 16)
            return
        }
        
        this.AddLog("🧪 开始测试按键...")
        
        ; 测试血量按键
        healthKey := this.healthKeyEdit.Text
        this.AddLog("📤 测试血量按键: " . healthKey)
        IbSend(healthKey)
        Sleep(500)
        
        ; 测试蓝量按键
        manaKey := this.manaKeyEdit.Text
        this.AddLog("📤 测试蓝量按键: " . manaKey)
        IbSend(manaKey)
        Sleep(500)
        
        this.AddLog("✅ 按键测试完成")
    }
    
    ; 保存配置
    SaveConfig() {
        ; 更新配置
        this.config.health.potionKey := this.healthKeyEdit.Text
        this.config.health.cooldown := Integer(this.healthCooldownEdit.Text)
        this.config.mana.potionKey := this.manaKeyEdit.Text
        this.config.mana.cooldown := Integer(this.manaCooldownEdit.Text)
        this.config.shield.potionKey := this.shieldKeyEdit.Text
        this.config.shield.cooldown := Integer(this.shieldCooldownEdit.Text)
        
        ; TODO: 保存到YAML文件
        
        this.AddLog("💾 配置已保存")
        MsgBox("配置保存成功！", "信息", 64)
    }
    
    ; 添加日志
    AddLog(message) {
        timestamp := FormatTime(A_Now, "HH:mm:ss")
        logMessage := "[" . timestamp . "] " . message . "`r`n"
        
        if (this.gui && this.logEdit) {
            this.logEdit.Text .= logMessage
            ; 滚动到底部
            this.logEdit.Focus()
            Send("^{End}")
        }
        
        ; 同时输出到控制台
        OutputDebug(logMessage)
    }
    
    ; 关闭事件
    OnClose() {
        this.Stop()
        ExitApp()
    }
}

; ===================================================================
; 主程序入口
; ===================================================================

; 检查管理员权限
if (!A_IsAdmin) {
    MsgBox("此程序需要管理员权限才能正常工作！`n`n" .
           "请右键点击脚本文件，选择'以管理员身份运行'。", "权限不足", 48)
    ExitApp()
}

; 创建硬件输入管理器实例
global hardwareManager := HardwareInputManager()

; 设置热键
F9::hardwareManager.Start()  ; F9启动
F10::hardwareManager.Stop() ; F10停止
F11::hardwareManager.TestKeys() ; F11测试按键

; 保持脚本运行
return

; 退出处理
GuiClose:
ExitApp()

OnExit((*) => hardwareManager.Stop())