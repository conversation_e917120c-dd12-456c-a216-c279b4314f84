; POE2自动喝药系统 - AutoHotkey版本
; 基于Python版本重新实现，更轻量高效

class POE2AutoDrink {
    __New() {
        ; 配置参数
        this.config := {
            ; 血量检测配置 (1920x1080分辨率)
            health: {
                enable: true,
                area: {x: 150, y: 950, width: 60, height: 120},
                colorLower: [0, 100, 40],
                colorUpper: [179, 216, 163],
                threshold: 50,
                potionKey: "1",
                cooldown: 1.0
            },
            ; 蓝量检测配置 (1920x1080分辨率)
            mana: {
                enable: true,
                area: {x: 1710, y: 950, width: 60, height: 120},
                colorLower: [0, 80, 0],
                colorUpper: [116, 224, 255],
                threshold: 40,
                potionKey: "2",
                cooldown: 1.0
            },
            ; 护盾检测配置 (1920x1080分辨率)
            shield: {
                enable: false,
                area: {x: 150, y: 950, width: 60, height: 120},
                colorLower: [0, 60, 0],
                colorUpper: [130, 255, 255],
                threshold: 50,
                potionKey: "1",
                cooldown: 1.0
            },
            ; 控制配置
            control: {
                checkInterval: 200,  ; 检测间隔(毫秒)
                toggleKey: "F5",
                exitKey: "F6"
            }
        }
        
        ; 运行状态
        this.isRunning := false
        this.lastDrinkTime := {health: 0, mana: 0, shield: 0}
        this.currentStatus := {health: 100, mana: 100, shield: 100}
        
        this.CreateGUI()
        this.SetupHotkeys()
    }
    
    ; 创建GUI界面
    CreateGUI() {
        this.gui := Gui("+Resize", "POE2自动喝药系统 v1.0")
        this.gui.SetFont("s10", "Microsoft YaHei")
        
        ; 状态显示区域
        this.gui.Add("GroupBox", "x10 y10 w470 h100", "实时状态")
        this.gui.Add("Text", "x20 y35 w60 h20", "血量:")
        this.healthText := this.gui.Add("Text", "x80 y35 w80 h20 c0x008000", "100.0%")
        this.healthText.SetFont("s12 Bold")
        
        this.gui.Add("Text", "x20 y60 w60 h20", "蓝量:")
        this.manaText := this.gui.Add("Text", "x80 y60 w80 h20 c0x0080FF", "100.0%")
        this.manaText.SetFont("s12 Bold")
        
        this.gui.Add("Text", "x200 y35 w60 h20", "护盾:")
        this.shieldText := this.gui.Add("Text", "x260 y35 w80 h20 c0x808080", "100.0%")
        this.shieldText.SetFont("s12 Bold")
        
        this.gui.Add("Text", "x200 y60 w60 h20", "状态:")
        this.statusText := this.gui.Add("Text", "x260 y60 w120 h20", "已停止")
        
        ; 控制按钮区域
        this.gui.Add("GroupBox", "x10 y120 w470 h80", "控制")
        this.startBtn := this.gui.Add("Button", "x20 y145 w60 h25", "启动(F5)")
        this.stopBtn := this.gui.Add("Button", "x90 y145 w60 h25", "停止(F5)")
        this.testBtn := this.gui.Add("Button", "x160 y145 w60 h25", "测试检测")
        this.debugBtn := this.gui.Add("Button", "x230 y145 w60 h25", "调试信息")
        this.drawBtn := this.gui.Add("Button", "x300 y145 w60 h25", "绘制区域")
        this.closeDrawBtn := this.gui.Add("Button", "x370 y145 w60 h25", "关闭绘制")
        
        ; 血量配置
        this.gui.Add("GroupBox", "x10 y210 w470 h120", "血量配置")
        this.healthEnableChk := this.gui.Add("CheckBox", "x20 y235 w80 h20", "启用血量")
        this.healthEnableChk.Checked := this.config.health.enable
        this.gui.Add("Text", "x110 y235 w40 h20", "按键:")
        this.healthKeyEdit := this.gui.Add("Edit", "x150 y232 w30 h23", this.config.health.potionKey)
        this.gui.Add("Text", "x190 y235 w40 h20", "阈值:")
        this.healthThresholdEdit := this.gui.Add("Edit", "x230 y232 w40 h23 Number", this.config.health.threshold)
        this.gui.Add("Text", "x280 y235 w40 h20", "冷却:")
        this.healthCooldownEdit := this.gui.Add("Edit", "x320 y232 w40 h23", this.config.health.cooldown)
        
        ; 血量坐标配置
        this.gui.Add("Text", "x20 y260 w20 h20", "X:")
        this.healthXEdit := this.gui.Add("Edit", "x40 y257 w50 h23 Number", this.config.health.area.x)
        this.gui.Add("Text", "x100 y260 w20 h20", "Y:")
        this.healthYEdit := this.gui.Add("Edit", "x120 y257 w50 h23 Number", this.config.health.area.y)
        this.gui.Add("Text", "x180 y260 w20 h20", "W:")
        this.healthWEdit := this.gui.Add("Edit", "x200 y257 w50 h23 Number", this.config.health.area.width)
        this.gui.Add("Text", "x260 y260 w20 h20", "H:")
        this.healthHEdit := this.gui.Add("Edit", "x280 y257 w50 h23 Number", this.config.health.area.height)
        
        ; 蓝量配置
        this.gui.Add("GroupBox", "x10 y340 w470 h120", "蓝量配置")
        this.manaEnableChk := this.gui.Add("CheckBox", "x20 y365 w80 h20", "启用蓝量")
        this.manaEnableChk.Checked := this.config.mana.enable
        this.gui.Add("Text", "x110 y365 w40 h20", "按键:")
        this.manaKeyEdit := this.gui.Add("Edit", "x150 y362 w30 h23", this.config.mana.potionKey)
        this.gui.Add("Text", "x190 y365 w40 h20", "阈值:")
        this.manaThresholdEdit := this.gui.Add("Edit", "x230 y362 w40 h23 Number", this.config.mana.threshold)
        this.gui.Add("Text", "x280 y365 w40 h20", "冷却:")
        this.manaCooldownEdit := this.gui.Add("Edit", "x320 y362 w40 h23", this.config.mana.cooldown)
        
        ; 蓝量坐标配置
        this.gui.Add("Text", "x20 y390 w20 h20", "X:")
        this.manaXEdit := this.gui.Add("Edit", "x40 y387 w50 h23 Number", this.config.mana.area.x)
        this.gui.Add("Text", "x100 y390 w20 h20", "Y:")
        this.manaYEdit := this.gui.Add("Edit", "x120 y387 w50 h23 Number", this.config.mana.area.y)
        this.gui.Add("Text", "x180 y390 w20 h20", "W:")
        this.manaWEdit := this.gui.Add("Edit", "x200 y387 w50 h23 Number", this.config.mana.area.width)
        this.gui.Add("Text", "x260 y390 w20 h20", "H:")
        this.manaHEdit := this.gui.Add("Edit", "x280 y387 w50 h23 Number", this.config.mana.area.height)
        
        ; 护盾配置
        this.gui.Add("GroupBox", "x10 y470 w470 h120", "护盾配置")
        this.shieldEnableChk := this.gui.Add("CheckBox", "x20 y495 w80 h20", "启用护盾")
        this.shieldEnableChk.Checked := this.config.shield.enable
        this.gui.Add("Text", "x110 y495 w40 h20", "按键:")
        this.shieldKeyEdit := this.gui.Add("Edit", "x150 y492 w30 h23", this.config.shield.potionKey)
        this.gui.Add("Text", "x190 y495 w40 h20", "阈值:")
        this.shieldThresholdEdit := this.gui.Add("Edit", "x230 y492 w40 h23 Number", this.config.shield.threshold)
        this.gui.Add("Text", "x280 y495 w40 h20", "冷却:")
        this.shieldCooldownEdit := this.gui.Add("Edit", "x320 y492 w40 h23", this.config.shield.cooldown)
        
        ; 护盾坐标配置
        this.gui.Add("Text", "x20 y520 w20 h20", "X:")
        this.shieldXEdit := this.gui.Add("Edit", "x40 y517 w50 h23 Number", this.config.shield.area.x)
        this.gui.Add("Text", "x100 y520 w20 h20", "Y:")
        this.shieldYEdit := this.gui.Add("Edit", "x120 y517 w50 h23 Number", this.config.shield.area.y)
        this.gui.Add("Text", "x180 y520 w20 h20", "W:")
        this.shieldWEdit := this.gui.Add("Edit", "x200 y517 w50 h23 Number", this.config.shield.area.width)
        this.gui.Add("Text", "x260 y520 w20 h20", "H:")
        this.shieldHEdit := this.gui.Add("Edit", "x280 y517 w50 h23 Number", this.config.shield.area.height)
        
        ; 应用设置按钮
        this.applyBtn := this.gui.Add("Button", "x10 y600 w100 h30", "应用设置")
        this.saveBtn := this.gui.Add("Button", "x120 y600 w100 h30", "保存配置")
        this.loadBtn := this.gui.Add("Button", "x230 y600 w100 h30", "加载配置")
        
        ; 日志区域
        this.gui.Add("GroupBox", "x10 y640 w470 h120", "日志")
        this.logEdit := this.gui.Add("Edit", "x20 y665 w450 h85 ReadOnly VScroll", "")
        
        ; 绑定事件
        this.startBtn.OnEvent("Click", (*) => this.Start())
        this.stopBtn.OnEvent("Click", (*) => this.Stop())
        this.testBtn.OnEvent("Click", (*) => this.TestDetection())
        this.debugBtn.OnEvent("Click", (*) => this.ShowDebugInfo())
        this.drawBtn.OnEvent("Click", (*) => this.DrawDetectionAreas())
        this.closeDrawBtn.OnEvent("Click", (*) => this.CloseOverlay())
        this.applyBtn.OnEvent("Click", (*) => this.ApplySettings())
        this.saveBtn.OnEvent("Click", (*) => this.SaveConfig())
        this.loadBtn.OnEvent("Click", (*) => this.LoadConfig())
        this.gui.OnEvent("Close", (*) => ExitApp())
        
        this.gui.Show("w490 h750")
        this.AddLog("POE2自动喝药系统已启动")
        this.AddLog("热键: F5=开关, F6=退出")
        
        ; 启动状态更新定时器
        this.updateMethod := ObjBindMethod(this, "UpdateStatus")
        SetTimer(this.updateMethod, 500)
    }
    
    ; 设置热键
    SetupHotkeys() {
        ; 热键将在类外部定义
        this.AddLog("热键已设置: F5=开关, F6=退出")
    }
    
    ; 检测资源百分比 - 核心算法
    DetectResourcePercentage(resourceType) {
        try {
            config := this.config.%resourceType%
            area := config.area
            
            ; 扫描区域内的像素
            validLines := 0
            totalLines := area.height
            
            ; 从底部向上扫描（符合资源条的显示逻辑）
            y := area.y + area.height - 1
            while (y >= area.y) {
                validPixels := 0
                
                ; 水平扫描这一行
                x := area.x
                while (x < area.x + area.width) {
                    if (this.IsColorInRange(x, y, config.colorLower, config.colorUpper)) {
                        validPixels++
                    }
                    x += 2  ; 跳跃采样提高性能
                }
                
                ; 如果这一行有足够的有效像素，认为是有效行
                if (validPixels >= (area.width / 4)) {  ; 25%以上像素有效
                    validLines++
                } else if (validLines > 0) {
                    ; 遇到无效行且已有有效行，停止扫描（找到顶部边界）
                    break
                }
                
                y -= 2  ; 跳跃扫描提高性能
            }
            
            ; 计算百分比
            percentage := (validLines * 2) / totalLines * 100  ; *2因为跳跃扫描
            return Max(0, Min(100, Round(percentage, 1)))
            
        } catch Error as e {
            this.AddLog("检测" . resourceType . "时出错: " . e.message)
            return 0
        }
    }
    
    ; 颜色范围检测
    IsColorInRange(x, y, colorLower, colorUpper) {
        c := PixelGetColor(x, y)
        r := (c >> 16) & 0xFF
        g := (c >> 8) & 0xFF
        b := c & 0xFF
        
        ; 转换为HSV进行比较（简化版本）
        hsv := this.RGBToHSV(r, g, b)
        
        return (hsv.h >= colorLower[1] && hsv.h <= colorUpper[1] &&
                hsv.s >= colorLower[2] && hsv.s <= colorUpper[2] &&
                hsv.v >= colorLower[3] && hsv.v <= colorUpper[3])
    }
    
    ; RGB转HSV（简化实现）
    RGBToHSV(r, g, b) {
        r := r / 255.0
        g := g / 255.0
        b := b / 255.0
        
        max_val := Max(r, Max(g, b))
        min_val := Min(r, Min(g, b))
        diff := max_val - min_val
        
        ; 计算V（明度）
        v := max_val * 100
        
        ; 计算S（饱和度）
        s := (max_val == 0) ? 0 : (diff / max_val) * 100
        
        ; 计算H（色相）
        if (diff == 0) {
            h := 0
        } else if (max_val == r) {
            h := 60 * Mod((g - b) / diff, 6)
        } else if (max_val == g) {
            h := 60 * ((b - r) / diff + 2)
        } else {
            h := 60 * ((r - g) / diff + 4)
        }
        
        if (h < 0) h += 360
        h := h / 2  ; 转换为0-179范围
        
        return {h: h, s: s, v: v}
    }
    
    ; 使用药剂
    UsePotion(potionKey) {
        try {
            Send("{" . potionKey . "}")
            this.AddLog("使用药剂: " . potionKey)
        } catch Error as e {
            this.AddLog("使用药剂失败: " . e.message)
        }
    }
    
    ; 主检测循环
    MainLoop() {
        if (!this.isRunning) 
            return
        
        currentTime := A_TickCount / 1000.0
        
        ; 检测各项资源
        for resourceType in ["health", "mana", "shield"] {
            config := this.config.%resourceType%
            if (!config.enable) 
                continue
            
            percentage := this.DetectResourcePercentage(resourceType)
            this.currentStatus.%resourceType% := percentage
            
            ; 检查是否需要使用药剂
            if (percentage < config.threshold) {
                lastDrink := this.lastDrinkTime.%resourceType%
                if (currentTime - lastDrink >= config.cooldown) {
                    this.UsePotion(config.potionKey)
                    this.lastDrinkTime.%resourceType% := currentTime
                }
            }
        }
    }
    
    ; 更新状态显示
    UpdateStatus() {
        if (!this.gui) 
            return
        
        ; 更新资源显示
        this.healthText.Text := Format("{:.1f}%", this.currentStatus.health)
        this.manaText.Text := Format("{:.1f}%", this.currentStatus.mana)
        this.shieldText.Text := Format("{:.1f}%", this.currentStatus.shield)
        
        ; 设置颜色
        this.SetResourceColor(this.healthText, this.currentStatus.health)
        this.SetResourceColor(this.manaText, this.currentStatus.mana)
        this.SetResourceColor(this.shieldText, this.currentStatus.shield)
        
        ; 更新状态文本
        this.statusText.Text := this.isRunning ? "运行中" : "已停止"
        
        ; 如果正在运行，执行检测
        if (this.isRunning) {
            this.MainLoop()
        }
    }
    
    ; 设置资源颜色
    SetResourceColor(textControl, percentage) {
        if (percentage <= 15) {
            textControl.SetFont("c0xFF0000")  ; 红色
        } else if (percentage <= 30) {
            textControl.SetFont("c0xFF8000")  ; 橙色
        } else {
            textControl.SetFont("c0x008000")  ; 绿色
        }
    }
    
    ; 启动
    Start() {
        this.isRunning := true
        this.AddLog("自动喝药已启动")
    }
    
    ; 停止
    Stop() {
        this.isRunning := false
        this.AddLog("自动喝药已停止")
    }
    
    ; 切换状态
    Toggle() {
        if (this.isRunning) {
            this.Stop()
        } else {
            this.Start()
        }
    }
    
    ; 测试检测
    TestDetection() {
        this.AddLog("=== 检测测试 ===")
        for resourceType in ["health", "mana", "shield"] {
            if (this.config.%resourceType%.enable) {
                percentage := this.DetectResourcePercentage(resourceType)
                this.AddLog(resourceType . ": " . percentage . "%")
            }
        }
    }
    
    ; 显示调试信息
    ShowDebugInfo() {
        MouseGetPos(&mx, &my)
        c := PixelGetColor(mx, my)
        r := (c >> 16) & 0xFF
        g := (c >> 8) & 0xFF
        b := c & 0xFF
        hsv := this.RGBToHSV(r, g, b)
        
        info := Format("鼠标位置: ({}, {})`n颜色 RGB: ({}, {}, {})`n颜色 HSV: ({:.1f}, {:.1f}, {:.1f})", 
            mx, my, r, g, b, hsv.h, hsv.s, hsv.v)
        MsgBox(info, "调试信息")
    }
    
    ; 绘制检测区域
    DrawDetectionAreas() {
        try {
            ; 先关闭之前的覆盖窗口
            this.CloseOverlay()
            
            ; 存储覆盖窗口数组
            this.overlayGuis := []
            
            ; 为每个启用的资源创建边框覆盖
            if (this.config.health.enable) {
                this.CreateAreaOverlay("health", "Red")
            }
            if (this.config.mana.enable) {
                this.CreateAreaOverlay("mana", "Blue")
            }
            if (this.config.shield.enable) {
                this.CreateAreaOverlay("shield", "Green")
            }
            
            this.AddLog("检测区域边框已绘制，5秒后自动关闭")
            
            ; 5秒后自动关闭
            if (this.closeOverlayMethod) {
                SetTimer(this.closeOverlayMethod, 0)
            }
            this.closeOverlayMethod := ObjBindMethod(this, "CloseOverlay")
            SetTimer(this.closeOverlayMethod, 5000)
            
        } catch Error as e {
            this.AddLog("绘制区域失败: " . e.message)
            MsgBox("绘制失败详情:`n" . e.message, "错误")
        }
    }
    
    ; 创建区域覆盖边框
    CreateAreaOverlay(type, color) {
        try {
            cfg := this.config.%type%
            area := cfg.area
            
            ; 边框宽度
            borderWidth := 3
            
            ; 创建4个边框窗口（上下左右）
            
            ; 上边框
            topGui := Gui("+AlwaysOnTop +ToolWindow -Caption", "")
            topGui.BackColor := color
            topGui.Show("x" . area.x . " y" . area.y . " w" . area.width . " h" . borderWidth . " NoActivate")
            WinSetTransparent(180, topGui)
            this.overlayGuis.Push(topGui)
            
            ; 下边框
            bottomGui := Gui("+AlwaysOnTop +ToolWindow -Caption", "")
            bottomGui.BackColor := color
            bottomGui.Show("x" . area.x . " y" . (area.y + area.height - borderWidth) . " w" . area.width . " h" . borderWidth . " NoActivate")
            WinSetTransparent(180, bottomGui)
            this.overlayGuis.Push(bottomGui)
            
            ; 左边框
            leftGui := Gui("+AlwaysOnTop +ToolWindow -Caption", "")
            leftGui.BackColor := color
            leftGui.Show("x" . area.x . " y" . area.y . " w" . borderWidth . " h" . area.height . " NoActivate")
            WinSetTransparent(180, leftGui)
            this.overlayGuis.Push(leftGui)
            
            ; 右边框
            rightGui := Gui("+AlwaysOnTop +ToolWindow -Caption", "")
            rightGui.BackColor := color
            rightGui.Show("x" . (area.x + area.width - borderWidth) . " y" . area.y . " w" . borderWidth . " h" . area.height . " NoActivate")
            WinSetTransparent(180, rightGui)
            this.overlayGuis.Push(rightGui)
            
            ; 添加标签窗口
            labelGui := Gui("+AlwaysOnTop +ToolWindow -Caption", "")
            labelGui.BackColor := "Black"
            labelGui.Add("Text", "x2 y2 w100 h20 c" . color . " Center", type)
            labelGui.Show("x" . area.x . " y" . (area.y - 25) . " w104 h24 NoActivate")
            WinSetTransparent(200, labelGui)
            this.overlayGuis.Push(labelGui)
            
            this.AddLog("已绘制" . type . "区域边框: " . area.x . "," . area.y . " " . area.width . "x" . area.height)
            
        } catch Error as e {
            this.AddLog("创建" . type . "覆盖失败: " . e.message)
        }
    }
    
    ; 关闭覆盖窗口
    CloseOverlay() {
        try {
            ; 清除定时器
            if (this.closeOverlayMethod) {
                SetTimer(this.closeOverlayMethod, 0)
                this.closeOverlayMethod := ""
            }
            
            ; 关闭所有覆盖窗口
            if (this.overlayGuis && this.overlayGuis.Length > 0) {
                for gui in this.overlayGuis {
                    try {
                        gui.Destroy()
                    } catch {
                        ; 忽略单个窗口关闭失败
                    }
                }
                this.overlayGuis := []
                this.AddLog("检测区域显示已关闭")
            }
            
        } catch Error as e {
            this.AddLog("关闭覆盖窗口失败: " . e.message)
            ; 强制清除引用
            this.overlayGuis := []
            this.closeOverlayMethod := ""
        }
    }
    
    ; 应用设置
    ApplySettings() {
        try {
            ; 更新血量配置
            this.config.health.enable := this.healthEnableChk.Checked
            this.config.health.potionKey := this.healthKeyEdit.Text
            this.config.health.threshold := Integer(this.healthThresholdEdit.Text)
            this.config.health.cooldown := Float(this.healthCooldownEdit.Text)
            this.config.health.area.x := Integer(this.healthXEdit.Text)
            this.config.health.area.y := Integer(this.healthYEdit.Text)
            this.config.health.area.width := Integer(this.healthWEdit.Text)
            this.config.health.area.height := Integer(this.healthHEdit.Text)
            
            ; 更新蓝量配置
            this.config.mana.enable := this.manaEnableChk.Checked
            this.config.mana.potionKey := this.manaKeyEdit.Text
            this.config.mana.threshold := Integer(this.manaThresholdEdit.Text)
            this.config.mana.cooldown := Float(this.manaCooldownEdit.Text)
            this.config.mana.area.x := Integer(this.manaXEdit.Text)
            this.config.mana.area.y := Integer(this.manaYEdit.Text)
            this.config.mana.area.width := Integer(this.manaWEdit.Text)
            this.config.mana.area.height := Integer(this.manaHEdit.Text)
            
            ; 更新护盾配置
            this.config.shield.enable := this.shieldEnableChk.Checked
            this.config.shield.potionKey := this.shieldKeyEdit.Text
            this.config.shield.threshold := Integer(this.shieldThresholdEdit.Text)
            this.config.shield.cooldown := Float(this.shieldCooldownEdit.Text)
            this.config.shield.area.x := Integer(this.shieldXEdit.Text)
            this.config.shield.area.y := Integer(this.shieldYEdit.Text)
            this.config.shield.area.width := Integer(this.shieldWEdit.Text)
            this.config.shield.area.height := Integer(this.shieldHEdit.Text)
            
            this.AddLog("设置已应用")
        } catch Error as e {
            MsgBox("设置错误: " . e.message, "错误")
        }
    }
    
    ; 保存配置
    SaveConfig() {
        this.ApplySettings()
        ; 这里可以添加保存到文件的逻辑
        this.AddLog("配置已保存")
    }
    
    ; 加载配置
    LoadConfig() {
        ; 这里可以添加从文件加载的逻辑
        this.AddLog("配置已加载")
    }
    
    ; 添加日志
    AddLog(message) {
        timestamp := FormatTime(A_Now, "HH:mm:ss")
        this.logEdit.Text .= Format("[{}] {}`r`n", timestamp, message)
        this.logEdit.Focus()
        Send("^{End}")
    }
}

; 创建自动喝药实例
autoDrink := POE2AutoDrink()

; 全局热键定义
F5::autoDrink.Toggle()
F6::ExitApp() 