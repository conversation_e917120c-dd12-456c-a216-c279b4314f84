[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "poe2-auto-drink"
version = "1.0.0"
description = "POE2 自动喝药 - 混合架构方案"
authors = [{name = "POE2 Auto Drink Team"}]
readme = "README_混合架构.md"
requires-python = ">=3.8.1"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: Microsoft :: Windows",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Games/Entertainment",
    "Topic :: Utilities",
]

dependencies = [
    "opencv-python>=4.8.0",
    "numpy>=1.24.0",
    "pyyaml>=6.0",
    "mss>=9.0.0",
    "pyautogui>=0.9.54",
    "pynput>=1.7.6",
    "keyboard>=0.13.5",
    "pywin32>=306; platform_system=='Windows'",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

[project.urls]
Homepage = "https://github.com/poe2-auto-drink/poe2-auto-drink"
Repository = "https://github.com/poe2-auto-drink/poe2-auto-drink.git"
Issues = "https://github.com/poe2-auto-drink/poe2-auto-drink/issues"

[project.scripts]
poe2-auto-drink = "start_hybrid:main"
poe2-hybrid-gui = "hybrid_gui:main"

[tool.hatch.build.targets.wheel]
packages = ["."]

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
(
  /(
      \.eggs
    | \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
  )/
)
'''

[tool.mypy]
python_version = "3.8.1"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "cv2.*",
    "mss.*",
    "pyautogui.*",
    "pynput.*",
    "keyboard.*",
    "win32pipe.*",
    "win32file.*",
    "win32gui.*",
    "win32ui.*",
    "win32con.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = [
    "tests",
]
python_files = [
    "test_*.py",
    "*_test.py",
]
python_classes = [
    "Test*",
]
python_functions = [
    "test_*",
]