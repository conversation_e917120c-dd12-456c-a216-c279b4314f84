#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
POE2 自动喝药 - 混合架构主控制界面
统一管理Python检测端和AHK硬件输入端
"""

import sys
import os
import time
import threading
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import yaml
import logging
from pathlib import Path
from detection_engine import PerformanceDetectionEngine

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler('hybrid_gui.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class HybridControlGUI:
    """混合架构主控制界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.detection_engine = None
        self.ahk_process = None
        self.config = self.load_config()
        self.running = False
        
        self.setup_gui()
        self.setup_status_update()
        
        logger.info("混合架构控制界面初始化完成")
    
    def load_config(self):
        """加载配置文件"""
        config_file = 'hybrid_config.yaml'
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"配置文件加载成功: {config_file}")
            return config
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            messagebox.showerror("错误", f"配置文件加载失败: {e}")
            return self.get_default_config()
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            'ui': {
                'main_window': {
                    'title': 'POE2 自动喝药 - 混合架构',
                    'size': {'width': 800, 'height': 600}
                }
            }
        }
    
    def setup_gui(self):
        """设置GUI界面"""
        # 主窗口配置
        ui_config = self.config.get('ui', {}).get('main_window', {})
        title = ui_config.get('title', 'POE2 自动喝药 - 混合架构')
        size = ui_config.get('size', {'width': 800, 'height': 600})
        
        self.root.title(title)
        self.root.geometry(f"{size['width']}x{size['height']}")
        self.root.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 创建各个区域
        self.create_status_area(main_frame)
        self.create_control_area(main_frame)
        self.create_detection_config_area(main_frame)
        self.create_hardware_config_area(main_frame)
        self.create_log_area(main_frame)
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_status_area(self, parent):
        """创建状态显示区域"""
        status_frame = ttk.LabelFrame(parent, text="系统状态", padding="5")
        status_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Python检测端状态
        ttk.Label(status_frame, text="Python检测端:").grid(row=0, column=0, sticky=tk.W)
        self.python_status_label = ttk.Label(status_frame, text="● 未启动", foreground="red")
        self.python_status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # AHK硬件输入端状态
        ttk.Label(status_frame, text="AHK硬件端:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        self.ahk_status_label = ttk.Label(status_frame, text="● 未启动", foreground="red")
        self.ahk_status_label.grid(row=0, column=3, sticky=tk.W, padx=(10, 0))
        
        # 管道连接状态
        ttk.Label(status_frame, text="管道连接:").grid(row=1, column=0, sticky=tk.W)
        self.pipe_status_label = ttk.Label(status_frame, text="● 未连接", foreground="red")
        self.pipe_status_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # 整体运行状态
        ttk.Label(status_frame, text="运行状态:").grid(row=1, column=2, sticky=tk.W, padx=(20, 0))
        self.running_status_label = ttk.Label(status_frame, text="● 已停止", foreground="red")
        self.running_status_label.grid(row=1, column=3, sticky=tk.W, padx=(10, 0))
    
    def create_control_area(self, parent):
        """创建控制按钮区域"""
        control_frame = ttk.LabelFrame(parent, text="系统控制", padding="5")
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 主控制按钮
        self.start_btn = ttk.Button(control_frame, text="启动系统", command=self.start_system)
        self.start_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_btn = ttk.Button(control_frame, text="停止系统", command=self.stop_system, state="disabled")
        self.stop_btn.grid(row=0, column=1, padx=(0, 10))
        
        self.restart_btn = ttk.Button(control_frame, text="重启系统", command=self.restart_system, state="disabled")
        self.restart_btn.grid(row=0, column=2, padx=(0, 10))
        
        # 分离控制按钮
        ttk.Separator(control_frame, orient="vertical").grid(row=0, column=3, sticky=(tk.N, tk.S), padx=10)
        
        self.start_python_btn = ttk.Button(control_frame, text="启动Python端", command=self.start_python_engine)
        self.start_python_btn.grid(row=0, column=4, padx=(0, 10))
        
        self.start_ahk_btn = ttk.Button(control_frame, text="启动AHK端", command=self.start_ahk_input)
        self.start_ahk_btn.grid(row=0, column=5, padx=(0, 10))
        
        # 配置管理按钮
        ttk.Separator(control_frame, orient="vertical").grid(row=0, column=6, sticky=(tk.N, tk.S), padx=10)
        
        self.load_config_btn = ttk.Button(control_frame, text="加载配置", command=self.load_config_file)
        self.load_config_btn.grid(row=0, column=7, padx=(0, 10))
        
        self.save_config_btn = ttk.Button(control_frame, text="保存配置", command=self.save_config_file)
        self.save_config_btn.grid(row=0, column=8)
    
    def create_detection_config_area(self, parent):
        """创建检测配置区域"""
        detection_frame = ttk.LabelFrame(parent, text="检测配置", padding="5")
        detection_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        detection_frame.columnconfigure(1, weight=1)
        
        # 创建Notebook用于分页
        notebook = ttk.Notebook(detection_frame)
        notebook.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 血量检测配置
        self.create_resource_config_tab(notebook, "health", "血量检测")
        
        # 蓝量检测配置
        self.create_resource_config_tab(notebook, "mana", "蓝量检测")
        
        # 护盾检测配置
        self.create_resource_config_tab(notebook, "shield", "护盾检测")
    
    def create_resource_config_tab(self, notebook, resource_type, tab_name):
        """创建资源检测配置标签页"""
        frame = ttk.Frame(notebook, padding="5")
        notebook.add(frame, text=tab_name)
        
        config = self.config.get('detection', {}).get(resource_type, {})
        
        # 启用检测
        enable_var = tk.BooleanVar(value=config.get('enable', False))
        ttk.Checkbutton(frame, text="启用检测", variable=enable_var).grid(row=0, column=0, sticky=tk.W)
        setattr(self, f"{resource_type}_enable_var", enable_var)
        
        # 检测区域
        ttk.Label(frame, text="检测区域:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        
        area_frame = ttk.Frame(frame)
        area_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        
        area = config.get('detection_area', {})
        
        ttk.Label(area_frame, text="X:").grid(row=0, column=0)
        x_var = tk.StringVar(value=str(area.get('x', 0)))
        ttk.Entry(area_frame, textvariable=x_var, width=8).grid(row=0, column=1, padx=(5, 10))
        setattr(self, f"{resource_type}_x_var", x_var)
        
        ttk.Label(area_frame, text="Y:").grid(row=0, column=2)
        y_var = tk.StringVar(value=str(area.get('y', 0)))
        ttk.Entry(area_frame, textvariable=y_var, width=8).grid(row=0, column=3, padx=(5, 10))
        setattr(self, f"{resource_type}_y_var", y_var)
        
        ttk.Label(area_frame, text="宽度:").grid(row=0, column=4)
        w_var = tk.StringVar(value=str(area.get('width', 0)))
        ttk.Entry(area_frame, textvariable=w_var, width=8).grid(row=0, column=5, padx=(5, 10))
        setattr(self, f"{resource_type}_width_var", w_var)
        
        ttk.Label(area_frame, text="高度:").grid(row=0, column=6)
        h_var = tk.StringVar(value=str(area.get('height', 0)))
        ttk.Entry(area_frame, textvariable=h_var, width=8).grid(row=0, column=7, padx=(5, 0))
        setattr(self, f"{resource_type}_height_var", h_var)
        
        # 阈值和冷却时间
        ttk.Label(frame, text="使用阈值(%):").grid(row=3, column=0, sticky=tk.W, pady=(10, 0))
        threshold_var = tk.StringVar(value=str(config.get('threshold', 50)))
        ttk.Entry(frame, textvariable=threshold_var, width=10).grid(row=3, column=1, sticky=tk.W, pady=(10, 0))
        setattr(self, f"{resource_type}_threshold_var", threshold_var)
        
        ttk.Label(frame, text="冷却时间(秒):").grid(row=4, column=0, sticky=tk.W, pady=(5, 0))
        cooldown_var = tk.StringVar(value=str(config.get('cooldown', 2.0)))
        ttk.Entry(frame, textvariable=cooldown_var, width=10).grid(row=4, column=1, sticky=tk.W, pady=(5, 0))
        setattr(self, f"{resource_type}_cooldown_var", cooldown_var)
    
    def create_hardware_config_area(self, parent):
        """创建硬件配置区域"""
        hardware_frame = ttk.LabelFrame(parent, text="硬件输入配置", padding="5")
        hardware_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10), padx=(10, 0))
        
        # 按键配置
        key_config = self.config.get('hardware_input', {}).get('key_mapping', {})
        
        # 血量药剂按键
        ttk.Label(hardware_frame, text="血量药剂按键:").grid(row=0, column=0, sticky=tk.W)
        self.health_key_var = tk.StringVar(value=key_config.get('health', {}).get('potion_key', '1'))
        ttk.Entry(hardware_frame, textvariable=self.health_key_var, width=5).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 蓝量药剂按键
        ttk.Label(hardware_frame, text="蓝量药剂按键:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        self.mana_key_var = tk.StringVar(value=key_config.get('mana', {}).get('potion_key', '2'))
        ttk.Entry(hardware_frame, textvariable=self.mana_key_var, width=5).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(5, 0))
        
        # 护盾药剂按键
        ttk.Label(hardware_frame, text="护盾药剂按键:").grid(row=2, column=0, sticky=tk.W, pady=(5, 0))
        self.shield_key_var = tk.StringVar(value=key_config.get('shield', {}).get('potion_key', '3'))
        ttk.Entry(hardware_frame, textvariable=self.shield_key_var, width=5).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=(5, 0))
        
        # GHUB设置
        ttk.Separator(hardware_frame, orient="horizontal").grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        ghub_config = self.config.get('hardware_input', {}).get('ghub_settings', {})
        
        # reWASD兼容
        self.rewasd_var = tk.BooleanVar(value=self.config.get('hardware_input', {}).get('rewasd_compatibility', {}).get('enable', True))
        ttk.Checkbutton(hardware_frame, text="reWASD兼容模式", variable=self.rewasd_var).grid(row=4, column=0, columnspan=2, sticky=tk.W)
        
        # 硬件按键
        self.hardware_keys_var = tk.BooleanVar(value=ghub_config.get('sync_keys', True))
        ttk.Checkbutton(hardware_frame, text="使用硬件按键", variable=self.hardware_keys_var).grid(row=5, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))
    
    def create_log_area(self, parent):
        """创建日志显示区域"""
        log_frame = ttk.LabelFrame(parent, text="运行日志", padding="5")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 创建文本框和滚动条
        text_frame = ttk.Frame(log_frame)
        text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(text_frame, height=10, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=self.log_text.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        # 日志控制按钮
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        ttk.Button(log_control_frame, text="清空日志", command=self.clear_log).grid(row=0, column=0)
        ttk.Button(log_control_frame, text="保存日志", command=self.save_log).grid(row=0, column=1, padx=(10, 0))
        ttk.Button(log_control_frame, text="检查连接", command=self.check_pipe_connection).grid(row=0, column=2, padx=(10, 0))

        # 配置网格权重
        parent.rowconfigure(3, weight=1)
    
    def setup_status_update(self):
        """设置状态更新"""
        self.update_status()
        self.root.after(1000, self.setup_status_update)  # 每秒更新一次
    
    def update_status(self):
        """更新状态显示"""
        # 更新Python检测端状态
        if self.detection_engine and self.detection_engine.running:
            self.python_status_label.config(text="● 运行中", foreground="green")
        else:
            self.python_status_label.config(text="● 未启动", foreground="red")
        
        # 更新AHK硬件端状态
        if self.ahk_process and self.ahk_process.poll() is None:
            self.ahk_status_label.config(text="● 运行中", foreground="green")
        else:
            self.ahk_status_label.config(text="● 未启动", foreground="red")
        
        # 更新管道连接状态
        if (self.detection_engine and self.detection_engine.running and 
            self.detection_engine.pipe_handle):
            self.pipe_status_label.config(text="● 已连接", foreground="green")
        else:
            self.pipe_status_label.config(text="● 未连接", foreground="red")
        
        # 更新整体运行状态
        if self.running:
            self.running_status_label.config(text="● 运行中", foreground="green")
        else:
            self.running_status_label.config(text="● 已停止", foreground="red")
    
    def start_system(self):
        """启动整个系统"""
        try:
            self.add_log("🚀 启动混合架构系统...")

            # 第一步：启动Python检测端
            self.add_log("📡 第1步: 启动Python检测端...")
            self.start_python_engine()

            # 等待Python端管道服务器就绪
            self.add_log("⏳ 等待Python端管道服务器就绪...")
            time.sleep(3)  # 给Python端足够时间创建管道

            # 检查Python端是否成功启动
            if not (self.detection_engine and self.detection_engine.running):
                raise Exception("Python检测端启动失败")

            # 第二步：启动AHK硬件输入端
            self.add_log("🎮 第2步: 启动AHK硬件输入端...")
            self.start_ahk_input()

            # 等待连接建立
            self.add_log("🔗 等待管道连接建立...")
            time.sleep(2)  # 等待连接建立

            self.running = True
            self.start_btn.config(state="disabled")
            self.stop_btn.config(state="normal")
            self.restart_btn.config(state="normal")

            self.add_log("✅ 混合架构系统启动成功")
            self.add_log("💡 如果连接失败，请在AHK界面按F9手动启动服务")

        except Exception as e:
            self.add_log(f"❌ 系统启动失败: {e}")
            messagebox.showerror("错误", f"系统启动失败: {e}")
            # 清理已启动的组件
            self.stop_system()
    
    def stop_system(self, show_error=True):
        """停止整个系统"""
        try:
            self.add_log("🛑 停止混合架构系统...")

            # 停止Python检测端
            if self.detection_engine:
                self.add_log("📡 停止Python检测端...")
                try:
                    self.detection_engine.stop()
                    self.detection_engine = None
                    self.add_log("✅ Python检测端已停止")
                except Exception as e:
                    self.add_log(f"⚠️ Python检测端停止异常: {e}")

            # 停止AHK硬件输入端
            if self.ahk_process:
                self.add_log("🎮 停止AHK硬件端...")
                try:
                    self.ahk_process.terminate()
                    # 等待进程结束
                    try:
                        self.ahk_process.wait(timeout=5)
                        self.add_log("✅ AHK硬件端已停止")
                    except subprocess.TimeoutExpired:
                        self.add_log("⚠️ 强制终止AHK进程")
                        self.ahk_process.kill()
                    self.ahk_process = None
                except Exception as e:
                    self.add_log(f"⚠️ AHK硬件端停止异常: {e}")

            self.running = False
            self.start_btn.config(state="normal")
            self.stop_btn.config(state="disabled")
            self.restart_btn.config(state="disabled")

            self.add_log("🛑 混合架构系统已停止")

        except Exception as e:
            self.add_log(f"❌ 系统停止失败: {e}")
            if show_error:
                messagebox.showerror("错误", f"系统停止失败: {e}")
    
    def restart_system(self):
        """重启系统"""
        self.add_log("重启系统...")
        self.stop_system()
        time.sleep(2)
        self.start_system()
    
    def start_python_engine(self):
        """启动Python检测引擎"""
        try:
            if self.detection_engine:
                self.add_log("Python检测端已在运行")
                return
            
            self.add_log("启动Python检测端...")
            self.detection_engine = PerformanceDetectionEngine('hybrid_config.yaml')
            
            # 在单独线程中启动
            def start_engine():
                self.detection_engine.start()
            
            thread = threading.Thread(target=start_engine, daemon=True)
            thread.start()
            
            self.add_log("✅ Python检测端启动成功")
            
        except Exception as e:
            self.add_log(f"❌ Python检测端启动失败: {e}")
            messagebox.showerror("错误", f"Python检测端启动失败: {e}")
    
    def start_ahk_input(self):
        """启动AHK硬件输入端"""
        try:
            if self.ahk_process and self.ahk_process.poll() is None:
                self.add_log("AHK硬件端已在运行")
                return

            self.add_log("启动AHK硬件端...")

            # 直接启动AHK脚本文件
            ahk_script = os.path.join(os.path.dirname(__file__), 'hardware_input.ahk')
            if not os.path.exists(ahk_script):
                raise Exception(f"AHK脚本文件不存在: {ahk_script}")

            # 使用shell=True直接运行AHK脚本
            self.ahk_process = subprocess.Popen([ahk_script], shell=True)
            self.add_log("✅ AHK硬件端启动成功")
            self.add_log("💡 请在AHK界面按F9启动硬件输入服务")

        except Exception as e:
            self.add_log(f"❌ AHK硬件端启动失败: {e}")
            messagebox.showerror("错误", f"AHK硬件端启动失败: {e}")
    

    
    def load_config_file(self):
        """加载配置文件"""
        filename = filedialog.askopenfilename(
            title="选择配置文件",
            filetypes=[("YAML文件", "*.yaml"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f)
                self.add_log(f"✅ 配置文件加载成功: {filename}")
                messagebox.showinfo("成功", "配置文件加载成功")
            except Exception as e:
                self.add_log(f"❌ 配置文件加载失败: {e}")
                messagebox.showerror("错误", f"配置文件加载失败: {e}")
    
    def save_config_file(self):
        """保存配置文件"""
        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".yaml",
            filetypes=[("YAML文件", "*.yaml"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                # 更新配置
                self.update_config_from_gui()
                
                with open(filename, 'w', encoding='utf-8') as f:
                    yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
                self.add_log(f"✅ 配置文件保存成功: {filename}")
                messagebox.showinfo("成功", "配置文件保存成功")
            except Exception as e:
                self.add_log(f"❌ 配置文件保存失败: {e}")
                messagebox.showerror("错误", f"配置文件保存失败: {e}")
    
    def update_config_from_gui(self):
        """从GUI更新配置"""
        # 更新检测配置
        for resource_type in ['health', 'mana', 'shield']:
            if resource_type not in self.config.get('detection', {}):
                self.config.setdefault('detection', {})[resource_type] = {}
            
            config = self.config['detection'][resource_type]
            
            # 更新启用状态
            enable_var = getattr(self, f"{resource_type}_enable_var", None)
            if enable_var:
                config['enable'] = enable_var.get()
            
            # 更新检测区域
            area = config.setdefault('detection_area', {})
            for coord in ['x', 'y', 'width', 'height']:
                var = getattr(self, f"{resource_type}_{coord}_var", None)
                if var:
                    try:
                        area[coord] = int(var.get())
                    except ValueError:
                        pass
            
            # 更新阈值和冷却时间
            threshold_var = getattr(self, f"{resource_type}_threshold_var", None)
            if threshold_var:
                try:
                    config['threshold'] = float(threshold_var.get())
                except ValueError:
                    pass
            
            cooldown_var = getattr(self, f"{resource_type}_cooldown_var", None)
            if cooldown_var:
                try:
                    config['cooldown'] = float(cooldown_var.get())
                except ValueError:
                    pass
        
        # 更新硬件输入配置
        hardware_config = self.config.setdefault('hardware_input', {})
        key_mapping = hardware_config.setdefault('key_mapping', {})
        
        for resource_type, key_var in [('health', self.health_key_var), 
                                      ('mana', self.mana_key_var), 
                                      ('shield', self.shield_key_var)]:
            key_mapping.setdefault(resource_type, {})['potion_key'] = key_var.get()
        
        # 更新reWASD兼容性
        rewasd_config = hardware_config.setdefault('rewasd_compatibility', {})
        rewasd_config['enable'] = self.rewasd_var.get()
        
        # 更新GHUB设置
        ghub_config = hardware_config.setdefault('ghub_settings', {})
        ghub_config['sync_keys'] = self.hardware_keys_var.get()
    
    def add_log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        
        # 限制日志行数
        lines = self.log_text.get("1.0", tk.END).split("\n")
        if len(lines) > 1000:
            self.log_text.delete("1.0", "100.0")
        
        logger.info(message)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete("1.0", tk.END)
    
    def save_log(self):
        """保存日志"""
        filename = filedialog.asksaveasfilename(
            title="保存日志文件",
            defaultextension=".log",
            filetypes=[("日志文件", "*.log"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get("1.0", tk.END))
                messagebox.showinfo("成功", "日志保存成功")
            except Exception as e:
                messagebox.showerror("错误", f"日志保存失败: {e}")

    def check_pipe_connection(self):
        """检查管道连接状态"""
        self.add_log("🔍 检查管道连接状态...")

        if not self.detection_engine:
            self.add_log("❌ Python检测端未启动")
            return

        if not self.detection_engine.running:
            self.add_log("❌ Python检测端未运行")
            return

        if not self.detection_engine.pipe_handle:
            self.add_log("❌ 管道服务器未创建")
            return

        self.add_log("✅ Python端管道服务器正常")

        # 检查AHK端状态
        if not self.ahk_process:
            self.add_log("❌ AHK硬件端未启动")
            return

        if self.ahk_process.poll() is not None:
            self.add_log("❌ AHK硬件端进程已退出")
            return

        self.add_log("✅ AHK硬件端进程正常")
        self.add_log("💡 如果连接仍有问题，请在AHK界面按F9重新启动服务")
    
    def on_closing(self):
        """关闭事件处理"""
        if self.running:
            if messagebox.askokcancel("退出", "系统正在运行，确定要退出吗？"):
                self.stop_system()
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """运行GUI"""
        self.add_log("混合架构控制界面启动")
        self.add_log("请先启动系统以开始自动喝药功能")
        self.root.mainloop()

def main():
    """主函数 - UV脚本入口点"""
    app = HybridControlGUI()
    app.run()


if __name__ == "__main__":
    main()