import sys
import cv2
import numpy as np
import yaml
import mss
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QPushButton, QLabel, QSpinBox, 
                           QComboBox, QGroupBox, QCheckBox, QMessageBox,
                           QTabWidget, QLineEdit, QGridLayout, QFrame, QDialog, QProgressBar, QFileDialog)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QImage, QPixmap, QPalette, QColor
import auto_drink
import os
import win32api

class CalibrateWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('自动喝药校准工具')
        self.resize(1200, 900)
        self.setMinimumSize(1100, 800)
        font = QFont("微软雅黑", 14)
        self.setFont(font)
        
        # 配置文件记忆功能
        self.config_file = self.load_last_config_file()
        
        # 配置文件选择区
        file_select_layout = QHBoxLayout()
        file_select_layout.setSpacing(10)
        file_select_layout.addWidget(QLabel('配置文件:'))
        self.config_file_label = QLabel(os.path.basename(self.config_file))
        file_select_layout.addWidget(self.config_file_label)
        browse_btn = QPushButton('浏览...')
        browse_btn.clicked.connect(self.browse_config_file)
        file_select_layout.addWidget(browse_btn)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
            QPushButton {
                background-color: #4a90e2;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 13px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #357abd;
            }
            QPushButton:pressed {
                background-color: #2d6da3;
            }
            QLabel {
                font-size: 13px;
            }
            QSpinBox, QComboBox {
                padding: 5px;
                border: 1px solid #cccccc;
                border-radius: 3px;
                background-color: white;
            }
            QTabWidget::pane {
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                border: 1px solid #cccccc;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
            }
        """)
        
        # 初始化变量
        self.config = self.load_config()
        self.selection_in_progress = False
        self.start_point = None
        self.end_point = None
        self.selection_img = None
        self.current_resource_type = None
        
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.addLayout(file_select_layout)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                border: 1px solid #cccccc;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
            }
        """)
        
        # 血量设置选项卡
        health_tab = QWidget()
        self.setup_resource_tab(health_tab, 'health', '血量')
        tab_widget.addTab(health_tab, "血量设置")
        
        # 蓝量设置选项卡
        mana_tab = QWidget()
        self.setup_resource_tab(mana_tab, 'mana', '蓝量')
        tab_widget.addTab(mana_tab, "蓝量设置")
        
        # 护盾设置选项卡
        shield_tab = QWidget()
        self.setup_resource_tab(shield_tab, 'shield', '护盾')
        tab_widget.addTab(shield_tab, "护盾设置")
        
        # 屏幕设置选项卡
        screen_tab = QWidget()
        self.setup_screen_tab(screen_tab)
        tab_widget.addTab(screen_tab, "屏幕设置")
        
        layout.addWidget(tab_widget)
        
        # 添加保存按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        save_button = QPushButton("保存配置")
        save_button.setFixedWidth(200)
        save_button.clicked.connect(self.save_config)
        # 新增导出配置按钮
        export_button = QPushButton("导出配置")
        export_button.setFixedWidth(200)
        export_button.clicked.connect(self.export_config)
        button_layout.addWidget(save_button)
        button_layout.addWidget(export_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 在合适位置添加按钮（如主布局或配置区）
        view_btn = QPushButton('查看当前选区')
        view_btn.clicked.connect(self.show_current_selections)
        layout.addWidget(view_btn)
        
        # 启动时直接加载last_config.txt记录的文件，无需下拉框
        self.config_file_label.setText(os.path.basename(self.config_file))
        self.refresh_ui_from_config()
        
    def load_last_config_file(self):
        try:
            with open('last_config.txt', 'r', encoding='utf-8') as f:
                fname = f.read().strip()
                print(f"[DEBUG] 校准工具读取last_config.txt: {fname}")
                if fname and os.path.exists(fname):
                    return fname
        except Exception as e:
            print(f"[DEBUG] 校准工具读取last_config.txt失败: {e}")
        return 'config.yaml'
    def save_last_config_file(self):
        try:
            with open('last_config.txt', 'w', encoding='utf-8') as f:
                f.write(self.config_file)
        except Exception:
            pass
    def on_config_file_changed(self, filename):
        if filename and os.path.exists(filename):
            self.config_file = os.path.abspath(filename)
            print(f"[DEBUG] 校准工具切换配置: {self.config_file}")
            self.config = self.load_config()
            self.refresh_ui_from_config()
            self.save_last_config_file()
            self.config_file_label.setText(os.path.basename(self.config_file))
    def browse_config_file(self):
        fname, _ = QFileDialog.getOpenFileName(self, '选择配置文件', '.', 'YAML Files (*.yaml)')
        if fname:
            self.config_file = fname
            self.config = self.load_config()
            self.refresh_ui_from_config()
            self.save_last_config_file()
            self.config_file_label.setText(os.path.basename(self.config_file))
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载配置文件失败: {e}")
            return None
            
    def setup_resource_tab(self, tab, resource_type, resource_name):
        """设置资源选项卡"""
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 启用自动喝药复选框
        enable_checkbox = QCheckBox(f"启用自动喝{resource_name}")
        enable_checkbox.setChecked(self.config.get(resource_type, {}).get('enable', resource_type=='health' or resource_type=='mana'))
        layout.addWidget(enable_checkbox)
        setattr(self, f"{resource_type}_enable_checkbox", enable_checkbox)
        
        # 区域选择组
        area_group = QGroupBox("检测区域")
        area_layout = QGridLayout()
        area_layout.setSpacing(10)
        
        # 坐标输入框
        x_input = QSpinBox(); x_input.setRange(0, 9999); x_input.setValue(self.config.get(resource_type, {}).get('detection_area', {}).get('x', 0))
        y_input = QSpinBox(); y_input.setRange(0, 9999); y_input.setValue(self.config.get(resource_type, {}).get('detection_area', {}).get('y', 0))
        w_input = QSpinBox(); w_input.setRange(1, 9999); w_input.setValue(self.config.get(resource_type, {}).get('detection_area', {}).get('width', 50))
        h_input = QSpinBox(); h_input.setRange(1, 9999); h_input.setValue(self.config.get(resource_type, {}).get('detection_area', {}).get('height', 100))
        area_layout.addWidget(QLabel("X:"), 0, 0); area_layout.addWidget(x_input, 0, 1)
        area_layout.addWidget(QLabel("Y:"), 0, 2); area_layout.addWidget(y_input, 0, 3)
        area_layout.addWidget(QLabel("宽度:"), 1, 0); area_layout.addWidget(w_input, 1, 1)
        area_layout.addWidget(QLabel("高度:"), 1, 2); area_layout.addWidget(h_input, 1, 3)
        
        # 选择区域按钮
        select_button = QPushButton(f"选择{resource_name}区域")
        select_button.clicked.connect(lambda: self.select_area(resource_type))
        area_layout.addWidget(select_button, 2, 0, 1, 4)
        
        area_group.setLayout(area_layout)
        layout.addWidget(area_group)
        
        # 分析检测组
        analyze_group = QGroupBox("分析检测/手动配置")
        analyze_layout = QGridLayout()
        analyze_layout.setSpacing(10)
        
        # 分析按钮
        analyze_button = QPushButton(f"分析{resource_name}颜色")
        analyze_button.clicked.connect(lambda: self.analyze_color(resource_type))
        analyze_layout.addWidget(analyze_button, 0, 0, 1, 2)
        
        # HSV下限
        analyze_layout.addWidget(QLabel("HSV下限"), 1, 0)
        h_lower = QSpinBox(); h_lower.setRange(0, 179); h_lower.setValue(self.config.get(resource_type, {}).get('color_lower', [0,0,0])[0])
        s_lower = QSpinBox(); s_lower.setRange(0, 255); s_lower.setValue(self.config.get(resource_type, {}).get('color_lower', [0,0,0])[1])
        v_lower = QSpinBox(); v_lower.setRange(0, 255); v_lower.setValue(self.config.get(resource_type, {}).get('color_lower', [0,0,0])[2])
        analyze_layout.addWidget(h_lower, 1, 1)
        analyze_layout.addWidget(s_lower, 1, 2)
        analyze_layout.addWidget(v_lower, 1, 3)
        # 下限色块
        lower_color_label = QLabel()
        lower_color_label.setFixedSize(40, 20)
        analyze_layout.addWidget(lower_color_label, 1, 4)

        # HSV上限
        analyze_layout.addWidget(QLabel("HSV上限"), 2, 0)
        h_upper = QSpinBox(); h_upper.setRange(0, 179); h_upper.setValue(self.config.get(resource_type, {}).get('color_upper', [179,255,255])[0])
        s_upper = QSpinBox(); s_upper.setRange(0, 255); s_upper.setValue(self.config.get(resource_type, {}).get('color_upper', [179,255,255])[1])
        v_upper = QSpinBox(); v_upper.setRange(0, 255); v_upper.setValue(self.config.get(resource_type, {}).get('color_upper', [179,255,255])[2])
        analyze_layout.addWidget(h_upper, 2, 1)
        analyze_layout.addWidget(s_upper, 2, 2)
        analyze_layout.addWidget(v_upper, 2, 3)
        # 上限色块
        upper_color_label = QLabel()
        upper_color_label.setFixedSize(40, 20)
        analyze_layout.addWidget(upper_color_label, 2, 4)

        threshold_input = QSpinBox(); threshold_input.setRange(1, 99); threshold_input.setValue(self.config.get(resource_type, {}).get('threshold', 60))
        analyze_layout.addWidget(QLabel("阈值%"), 3, 0)
        analyze_layout.addWidget(threshold_input, 3, 1)

        # 按键（护盾与血药同步）
        if resource_type == 'shield':
            key_combo = QComboBox(); key_combo.addItems(['1','2','3','4','5','6','7','8','9','0']);
            key_combo.setCurrentText(self.config.get('health', {}).get('potion_key', '1'))
            key_combo.setEnabled(False)
        else:
            key_combo = QComboBox(); key_combo.addItems(['1','2','3','4','5','6','7','8','9','0']);
            key_combo.setCurrentText(self.config.get(resource_type, {}).get('potion_key', '1'))
        analyze_layout.addWidget(QLabel("按键"), 3, 2)
        analyze_layout.addWidget(key_combo, 3, 3)

        def update_color_labels():
            # HSV转BGR
            lower_hsv = np.uint8([[[h_lower.value(), s_lower.value(), v_lower.value()]]])
            upper_hsv = np.uint8([[[h_upper.value(), s_upper.value(), v_upper.value()]]])
            lower_bgr = cv2.cvtColor(lower_hsv, cv2.COLOR_HSV2BGR)[0][0]
            upper_bgr = cv2.cvtColor(upper_hsv, cv2.COLOR_HSV2BGR)[0][0]
            # BGR转QColor
            lower_color = QColor(int(lower_bgr[2]), int(lower_bgr[1]), int(lower_bgr[0]))
            upper_color = QColor(int(upper_bgr[2]), int(upper_bgr[1]), int(upper_bgr[0]))
            lower_color_label.setStyleSheet(f"background-color: {lower_color.name()}; border: 1px solid #888;")
            upper_color_label.setStyleSheet(f"background-color: {upper_color.name()}; border: 1px solid #888;")
        # 绑定SpinBox变化信号
        for sb in [h_lower, s_lower, v_lower]:
            sb.valueChanged.connect(update_color_labels)
        for sb in [h_upper, s_upper, v_upper]:
            sb.valueChanged.connect(update_color_labels)
        update_color_labels()
        
        # 保存配置按钮
        save_button = QPushButton("保存当前配置")
        def save_hsv():
            self.config[resource_type]['color_lower'] = [h_lower.value(), s_lower.value(), v_lower.value()]
            self.config[resource_type]['color_upper'] = [h_upper.value(), s_upper.value(), v_upper.value()]
            self.config[resource_type]['threshold'] = threshold_input.value()
            self.config[resource_type]['potion_key'] = key_combo.currentText()
            self.config[resource_type]['enable'] = enable_checkbox.isChecked()
            self.save_config()
        save_button.clicked.connect(save_hsv)
        analyze_layout.addWidget(save_button, 4, 0, 1, 4)
        
        analyze_group.setLayout(analyze_layout)
        layout.addWidget(analyze_group)
        
        # 测试检测组
        test_group = QGroupBox("测试检测")
        test_layout = QVBoxLayout()
        test_button = QPushButton(f"测试{resource_name}检测")
        test_button.clicked.connect(lambda: self.test_detection(resource_type))
        test_layout.addWidget(test_button)
        test_group.setLayout(test_layout)
        layout.addWidget(test_group)
        
        # 保存输入框的引用
        setattr(self, f"{resource_type}_x", x_input)
        setattr(self, f"{resource_type}_y", y_input)
        setattr(self, f"{resource_type}_w", w_input)
        setattr(self, f"{resource_type}_h", h_input)
        # 保存HSV控件引用，便于分析按钮自动填充
        setattr(self, f"{resource_type}_h_lower", h_lower)
        setattr(self, f"{resource_type}_s_lower", s_lower)
        setattr(self, f"{resource_type}_v_lower", v_lower)
        setattr(self, f"{resource_type}_h_upper", h_upper)
        setattr(self, f"{resource_type}_s_upper", s_upper)
        setattr(self, f"{resource_type}_v_upper", v_upper)
        setattr(self, f"{resource_type}_threshold", threshold_input)
        setattr(self, f"{resource_type}_key", key_combo)
        
        # 绑定信号实现自动同步和互斥
        enable_checkbox.stateChanged.connect(lambda state, rt=resource_type: self.on_resource_enable_changed(rt, state))
        x_input.valueChanged.connect(self.sync_config)
        y_input.valueChanged.connect(self.sync_config)
        w_input.valueChanged.connect(self.sync_config)
        h_input.valueChanged.connect(self.sync_config)
        h_lower.valueChanged.connect(self.sync_config)
        s_lower.valueChanged.connect(self.sync_config)
        v_lower.valueChanged.connect(self.sync_config)
        h_upper.valueChanged.connect(self.sync_config)
        s_upper.valueChanged.connect(self.sync_config)
        v_upper.valueChanged.connect(self.sync_config)
        threshold_input.valueChanged.connect(self.sync_config)
        if resource_type != 'shield':
            key_combo.currentIndexChanged.connect(self.sync_config)
        
        # 护盾选区自动套用血量选区
        if resource_type == 'health':
            def sync_shield_area():
                self.shield_x.setValue(self.health_x.value())
                self.shield_y.setValue(self.health_y.value())
                self.shield_w.setValue(self.health_w.value())
                self.shield_h.setValue(self.health_h.value())
            x_input.valueChanged.connect(sync_shield_area)
            y_input.valueChanged.connect(sync_shield_area)
            w_input.valueChanged.connect(sync_shield_area)
            h_input.valueChanged.connect(sync_shield_area)
        
    def setup_screen_tab(self, tab):
        """设置屏幕选项卡，只保留显示器选择，分辨率用win32api获取真实物理分辨率"""
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        layout.setContentsMargins(15, 15, 15, 15)
        # 显示器选择
        monitor_group = QGroupBox("显示器选择")
        monitor_layout = QVBoxLayout()
        monitor_layout.setSpacing(10)
        self.monitor_combo = QComboBox()
        self.monitor_resolutions = []
        monitors = win32api.EnumDisplayMonitors()
        for i, m in enumerate(monitors, 1):
            info = win32api.GetMonitorInfo(m[0])
            left, top, right, bottom = info['Monitor']
            width = right - left
            height = bottom - top
            res_str = f"显示器 {i} ({width}x{height})"
            self.monitor_combo.addItem(res_str)
            self.monitor_resolutions.append((width, height))
        self.monitor_combo.setCurrentIndex(self.config['screen'].get('monitor_index', 1) - 1)
        self.monitor_combo.currentIndexChanged.connect(self.on_monitor_changed)
        monitor_layout.addWidget(self.monitor_combo)
        monitor_group.setLayout(monitor_layout)
        layout.addWidget(monitor_group)
        # 添加弹性空间
        layout.addStretch()
    def on_monitor_changed(self, idx):
        # 自动同步分辨率到config
        width, height = self.monitor_resolutions[idx]
        self.config['screen']['monitor_index'] = idx + 1
        self.config['screen']['resolution']['width'] = width
        self.config['screen']['resolution']['height'] = height
    def select_area(self, resource_type):
        """选择区域"""
        self.current_resource_type = resource_type
        
        # 截取屏幕
        with mss.mss() as sct:
            monitor = sct.monitors[self.monitor_combo.currentIndex() + 1]
            screenshot = sct.grab(monitor)
            self.selection_img = np.array(screenshot)
        
        # 创建OpenCV窗口
        cv2.namedWindow('选择区域')
        cv2.setMouseCallback('选择区域', self.mouse_callback)
        
        # 显示指导信息
        resource_name = "生命值" if resource_type == 'health' else "魔力值" if resource_type == 'mana' else "护盾"
        cv2.putText(self.selection_img, f"请用鼠标选择{resource_name}区域", (50, 50), 
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        # 显示图像
        cv2.imshow('选择区域', self.selection_img)
        
    def mouse_callback(self, event, x, y, flags, param):
        """鼠标回调函数"""
        if event == cv2.EVENT_LBUTTONDOWN:
            self.selection_in_progress = True
            self.start_point = (x, y)
        
        elif event == cv2.EVENT_MOUSEMOVE:
            if self.selection_in_progress:
                self.end_point = (x, y)
                # 复制原图并在上面绘制矩形
                img_copy = self.selection_img.copy()
                cv2.rectangle(img_copy, self.start_point, self.end_point, (0, 255, 0), 2)
                cv2.imshow('选择区域', img_copy)
        
        elif event == cv2.EVENT_LBUTTONUP:
            self.selection_in_progress = False
            self.end_point = (x, y)
            # 确保矩形是从左上角到右下角绘制的
            x1, y1 = min(self.start_point[0], self.end_point[0]), min(self.start_point[1], self.end_point[1])
            x2, y2 = max(self.start_point[0], self.end_point[0]), max(self.start_point[1], self.end_point[1])
            self.start_point = (x1, y1)
            self.end_point = (x2, y2)
            
            # 更新UI
            width = x2 - x1
            height = y2 - y1
            if width > 0 and height > 0:
                getattr(self, f"{self.current_resource_type}_x").setValue(x1)
                getattr(self, f"{self.current_resource_type}_y").setValue(y1)
                getattr(self, f"{self.current_resource_type}_w").setValue(width)
                getattr(self, f"{self.current_resource_type}_h").setValue(height)
            
            # 关闭窗口
            cv2.destroyWindow('选择区域')
            
    def analyze_color(self, resource_type):
        """分析颜色"""
        # 自动初始化resource_type配置，防止KeyError
        if resource_type not in self.config:
            self.config[resource_type] = {
                'detection_area': {'x': 0, 'y': 0, 'width': 50, 'height': 100},
                'color_lower': [0, 0, 0],
                'color_upper': [179, 255, 255],
                'threshold': 60,
                'potion_key': '1',
                'enable': False
            }
        
        # 获取当前坐标
        x = getattr(self, f"{resource_type}_x").value()
        y = getattr(self, f"{resource_type}_y").value()
        w = getattr(self, f"{resource_type}_w").value()
        h = getattr(self, f"{resource_type}_h").value()
        
        if w <= 0 or h <= 0:
            QMessageBox.warning(self, "错误", "请先选择有效区域")
            return
        
        # 截取屏幕
        with mss.mss() as sct:
            monitor = sct.monitors[self.monitor_combo.currentIndex() + 1]
            screenshot = sct.grab(monitor)
            img = np.array(screenshot)
        
        # 提取区域
        area = img[y:y+h, x:x+w]
        
        # 转换为HSV
        hsv = cv2.cvtColor(area, cv2.COLOR_BGR2HSV)
        
        # 计算掩码以过滤出非黑色像素
        non_black_mask = cv2.inRange(hsv, np.array([0, 25, 25]), np.array([179, 255, 255]))
        
        # 仅分析非黑色区域的HSV值
        h_vals = hsv[:,:,0][non_black_mask > 0]
        s_vals = hsv[:,:,1][non_black_mask > 0]
        v_vals = hsv[:,:,2][non_black_mask > 0]
        
        # 如果没有足够的非黑色像素，则提示错误
        if len(h_vals) < 10:
            QMessageBox.warning(self, "错误", "选中区域颜色过暗或过少，请重新选择")
            return
        
        # 分析HSV值
        h_min, h_max = np.percentile(h_vals, 5), np.percentile(h_vals, 95)
        s_min, s_max = np.percentile(s_vals, 5), np.percentile(s_vals, 95)
        v_min, v_max = np.percentile(v_vals, 5), np.percentile(v_vals, 95)
        
        # 为血量和蓝量应用更具体的颜色范围调整
        if resource_type == 'health':
            h_min = 0
            h_max = 179
            s_min = max(20, s_min - 10)
        elif resource_type == 'mana':
            h_min = max(90, h_min - 3)
            h_max = min(150, h_max + 3)
        
        # 添加适度的容差
        s_min = max(25, s_min - 5)
        s_max = min(255, s_max + 5)
        v_min = max(25, v_min - 5)
        v_max = min(255, v_max + 5)
        
        # 更新配置
        self.config[resource_type]['color_lower'] = [int(h_min), int(s_min), int(v_min)]
        self.config[resource_type]['color_upper'] = [int(h_max), int(s_max), int(v_max)]

        # 自动覆盖SpinBox的HSV值
        getattr(self, f"{resource_type}_h_lower").setValue(int(h_min))
        getattr(self, f"{resource_type}_s_lower").setValue(int(s_min))
        getattr(self, f"{resource_type}_v_lower").setValue(int(v_min))
        getattr(self, f"{resource_type}_h_upper").setValue(int(h_max))
        getattr(self, f"{resource_type}_s_upper").setValue(int(s_max))
        getattr(self, f"{resource_type}_v_upper").setValue(int(v_max))

        QMessageBox.information(self, "成功", f"颜色分析完成\nHSV范围: [{int(h_min)}, {int(s_min)}, {int(v_min)}] - [{int(h_max)}, {int(s_max)}, {int(v_max)}]")
        
    def test_detection(self, resource_type):
        """测试检测（调用auto_drink.detect_resource_percentage，底部连续有效行法，图片加刻度条）"""
        try:
            auto_drink.config = self.config  # 确保配置同步
            resource_config = self.config[resource_type]
            area = auto_drink.direct_grab_area(resource_config['detection_area'])
            progress = auto_drink.detect_resource_percentage(area, resource_type)
            w = resource_config['detection_area']['width']
            h = resource_config['detection_area']['height']
            # 生成掩码用于可视化
            hsv = cv2.cvtColor(area, cv2.COLOR_BGR2HSV)
            lower = np.array(resource_config['color_lower'])
            upper = np.array(resource_config['color_upper'])
            mask = cv2.inRange(hsv, lower, upper)
            # 画红线标注顶部
            # 由于算法唯一化，无法直接获得top_row，只显示进度百分比
            bar_width = 40
            img_with_bar = np.ones((h, w + bar_width, 3), dtype=np.uint8) * 255
            img_with_bar[:, bar_width:, :] = area
            # 刻度条本体
            for i in range(11):
                y = int(h - i * h / 10)
                cv2.line(img_with_bar, (0, y), (bar_width - 10, y), (0, 0, 0), 1)
                percent = int(i * 10)
                cv2.putText(img_with_bar, f"{percent}%", (2, y - 2), cv2.FONT_HERSHEY_PLAIN, 1, (0, 0, 0), 1)
            # 标注百分比
            y = int(h * (1 - progress / 100))
            cv2.line(img_with_bar, (0, y), (bar_width, y), (0, 0, 255), 2)
            cv2.putText(img_with_bar, f"{progress:.1f}%", (2, y + 15), cv2.FONT_HERSHEY_PLAIN, 1.2, (0, 0, 255), 2)
            # 合成掩码和彩色检测区域图片
            mask_bgr = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR)
            combined_img = np.hstack((mask_bgr, img_with_bar))
            # 弹窗展示合成图片和进度条
            dlg = ResultDialog(combined_img, progress, parent=self)
            dlg.exec_()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"测试检测失败: {str(e)}")
        
    def test_screenshot(self):
        """测试截图"""
        try:
            with mss.mss() as sct:
                monitor = sct.monitors[self.monitor_combo.currentIndex() + 1]
                screenshot = sct.grab(monitor)
                img = np.array(screenshot)
                # 显示截图
                cv2.imshow('测试截图', img)
                cv2.waitKey(0)
                try:
                    cv2.destroyWindow('测试截图')
                except Exception:
                    pass
        except Exception as e:
            QMessageBox.critical(self, "错误", f"截图失败: {e}")
            
    def save_config(self):
        try:
            # 更新显示器选择和分辨率
            idx = self.monitor_combo.currentIndex()
            width, height = self.monitor_resolutions[idx]
            self.config['screen']['monitor_index'] = idx + 1
            self.config['screen']['resolution']['width'] = width
            self.config['screen']['resolution']['height'] = height
            # 保存到文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, allow_unicode=True)
            self.config_file_label.setText(os.path.basename(self.config_file))
            QMessageBox.information(self, "成功", "配置已保存")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存配置失败: {str(e)}")

    def refresh_ui_from_config(self):
        """根据当前配置刷新界面控件"""
        # 刷新血量、蓝量区域、HSV等控件
        for resource_type in ['health', 'mana', 'shield']:
            getattr(self, f"{resource_type}_x").setValue(self.config[resource_type]['detection_area']['x'])
            getattr(self, f"{resource_type}_y").setValue(self.config[resource_type]['detection_area']['y'])
            getattr(self, f"{resource_type}_w").setValue(self.config[resource_type]['detection_area']['width'])
            getattr(self, f"{resource_type}_h").setValue(self.config[resource_type]['detection_area']['height'])
            getattr(self, f"{resource_type}_h_lower").setValue(self.config[resource_type]['color_lower'][0])
            getattr(self, f"{resource_type}_s_lower").setValue(self.config[resource_type]['color_lower'][1])
            getattr(self, f"{resource_type}_v_lower").setValue(self.config[resource_type]['color_lower'][2])
            getattr(self, f"{resource_type}_h_upper").setValue(self.config[resource_type]['color_upper'][0])
            getattr(self, f"{resource_type}_s_upper").setValue(self.config[resource_type]['color_upper'][1])
            getattr(self, f"{resource_type}_v_upper").setValue(self.config[resource_type]['color_upper'][2])
            getattr(self, f"{resource_type}_threshold").setValue(self.config[resource_type]['threshold'])
            getattr(self, f"{resource_type}_key").setCurrentText(self.config[resource_type]['potion_key'])
            getattr(self, f"{resource_type}_enable_checkbox").setChecked(self.config[resource_type].get('enable', True))
        # 屏幕设置
        self.monitor_combo.setCurrentIndex(self.config['screen'].get('monitor_index', 1) - 1)

    def sync_config(self):
        # 实时同步所有参数到auto_drink.config
        for resource_type in ['health', 'mana', 'shield']:
            if resource_type not in self.config:
                self.config[resource_type] = {}
            self.config[resource_type]['detection_area'] = self.config[resource_type].get('detection_area', {})
            self.config[resource_type]['detection_area']['x'] = getattr(self, f"{resource_type}_x").value()
            self.config[resource_type]['detection_area']['y'] = getattr(self, f"{resource_type}_y").value()
            self.config[resource_type]['detection_area']['width'] = getattr(self, f"{resource_type}_w").value()
            self.config[resource_type]['detection_area']['height'] = getattr(self, f"{resource_type}_h").value()
            self.config[resource_type]['color_lower'] = [getattr(self, f"{resource_type}_h_lower").value(), getattr(self, f"{resource_type}_s_lower").value(), getattr(self, f"{resource_type}_v_lower").value()]
            self.config[resource_type]['color_upper'] = [getattr(self, f"{resource_type}_h_upper").value(), getattr(self, f"{resource_type}_s_upper").value(), getattr(self, f"{resource_type}_v_upper").value()]
            self.config[resource_type]['threshold'] = getattr(self, f"{resource_type}_threshold").value()
            if resource_type == 'shield':
                # 护盾按键与血药同步
                self.config[resource_type]['potion_key'] = self.config['health']['potion_key']
            else:
                self.config[resource_type]['potion_key'] = getattr(self, f"{resource_type}_key").currentText()
            self.config[resource_type]['enable'] = getattr(self, f"{resource_type}_enable_checkbox").isChecked()
        auto_drink.config = self.config

    def on_resource_enable_changed(self, resource_type, state):
        # 互斥逻辑：护盾和血量只能启用一个
        if state == Qt.Checked:
            if resource_type == 'health' and getattr(self, 'shield_enable_checkbox').isChecked():
                self.shield_enable_checkbox.blockSignals(True)
                self.shield_enable_checkbox.setChecked(False)
                self.shield_enable_checkbox.blockSignals(False)
            elif resource_type == 'shield' and getattr(self, 'health_enable_checkbox').isChecked():
                self.health_enable_checkbox.blockSignals(True)
                self.health_enable_checkbox.setChecked(False)
                self.health_enable_checkbox.blockSignals(False)
        self.sync_config()

    def export_config(self):
        """导出当前配置为YAML文件"""
        fname, _ = QFileDialog.getSaveFileName(self, '导出配置文件', '.', 'YAML Files (*.yaml)')
        if fname:
            try:
                with open(fname, 'w', encoding='utf-8') as f:
                    yaml.dump(self.config, f, allow_unicode=True)
                self.config_file = fname
                self.config_file_label.setText(os.path.basename(self.config_file))
                QMessageBox.information(self, "成功", f"配置已导出到: {fname}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出配置失败: {e}")

    def show_current_selections(self):
        import mss
        import cv2
        import numpy as np
        # 获取当前显示器截图
        with mss.mss() as sct:
            monitor_idx = self.monitor_combo.currentIndex() + 1
            monitor = sct.monitors[monitor_idx]
            screenshot = sct.grab(monitor)
            img = np.array(screenshot)
        # 获取各区域
        areas = [
            ('health', (0, 0, 255)),  # 红色
            ('mana', (255, 0, 0)),    # 蓝色
            ('shield', (0, 255, 0)),  # 绿色
        ]
        for key, color in areas:
            area = self.config.get(key, {}).get('detection_area', None)
            if area:
                x, y, w, h = area['x'], area['y'], area['width'], area['height']
                cv2.rectangle(img, (x, y), (x + w, y + h), color, 3)
                cv2.putText(img, key, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)
        # 显示图片
        cv2.imshow('当前选区', img)
        cv2.waitKey(0)
        cv2.destroyAllWindows()

class ResultDialog(QDialog):
    def __init__(self, image, progress, parent=None):
        super().__init__(parent)
        self.setWindowTitle('检测结果')
        self.setFixedSize(700, 500)
        layout = QVBoxLayout(self)
        # 图片展示
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.image_label)
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(int(progress))
        self.progress_bar.setFormat(f'血量进度: {progress:.2f}%')
        layout.addWidget(self.progress_bar)
        # 设置图片
        self.set_image(image)
    def set_image(self, cv_img):
        # BGR转RGB
        rgb_img = cv2.cvtColor(cv_img, cv2.COLOR_BGR2RGB)
        h, w, ch = rgb_img.shape
        bytes_per_line = ch * w
        qimg = QImage(rgb_img.data, w, h, bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(qimg)
        self.image_label.setPixmap(pixmap.scaled(650, 350, Qt.KeepAspectRatio, Qt.SmoothTransformation))

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = CalibrateWindow()
    window.show()
    sys.exit(app.exec_()) 