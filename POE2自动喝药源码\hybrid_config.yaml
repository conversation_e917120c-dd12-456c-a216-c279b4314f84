# POE2 自动喝药 - 混合架构配置文件
# Python检测端 + AHK硬件输入端

# ===================================================================
# 混合架构核心配置
# ===================================================================
hybrid:
  # 通信配置
  communication:
    pipe_name: "\\\\.\\pipe\\poe2_auto_drink"  # 命名管道名称
    timeout: 1000                              # 超时时间(ms)
    buffer_size: 1024                          # 缓冲区大小
    heartbeat_interval: 5000                   # 心跳间隔(ms)
  
  # 性能配置
  performance:
    detection_frequency: 20                    # 检测频率(Hz)
    max_threads: 4                            # 最大线程数
    cache_enabled: true                       # 启用缓存
    pixel_sampling: 2                         # 像素采样间隔
    
  # 调试配置
  debug:
    enable_logging: true                      # 启用日志
    log_level: "INFO"                         # 日志级别
    performance_monitor: false                # 性能监控
    save_screenshots: false                   # 保存截图

# ===================================================================
# Python检测端配置
# ===================================================================
detection:
  # 血量检测
  health:
    detection_area:
      x: 1760                                 # 检测区域X坐标
      y: 865                                  # 检测区域Y坐标
      width: 25                               # 检测区域宽度
      height: 180                             # 检测区域高度
    color_range:
      lower: [0, 50, 50]                      # HSV下限 [H, S, V]
      upper: [10, 255, 255]                   # HSV上限 [H, S, V]
    threshold: 75                             # 使用药剂阈值(%)
    cooldown: 2.0                             # 冷却时间(秒)
    enable: true                              # 启用检测
    priority: 1                               # 优先级(1最高)
    
  # 蓝量检测
  mana:
    detection_area:
      x: 1790
      y: 865
      width: 25
      height: 180
    color_range:
      lower: [100, 50, 50]                    # 蓝色HSV范围
      upper: [130, 255, 255]
    threshold: 50
    cooldown: 1.5
    enable: true
    priority: 2
    
  # 护盾检测
  shield:
    detection_area:
      x: 1820
      y: 865
      width: 25
      height: 180
    color_range:
      lower: [200, 50, 50]                    # 白色/银色HSV范围
      upper: [220, 255, 255]
    threshold: 30
    cooldown: 1.0
    enable: false                             # 默认禁用
    priority: 3

# ===================================================================
# AHK硬件输入端配置
# ===================================================================
hardware_input:
  # GHUB驱动配置
  ghub_settings:
    driver_type: "LogitechGHubNew"            # 驱动类型
    driver_mode: 1                            # 驱动模式
    sync_keys: true                           # 同步按键状态
    auto_retry: true                          # 自动重试
    
  # 按键映射
  key_mapping:
    health:
      potion_key: "1"                         # 血量药剂按键
      cooldown: 2000                          # 冷却时间(ms)
      key_type: "main_keyboard"               # 按键类型
      
    mana:
      potion_key: "2"                         # 蓝量药剂按键
      cooldown: 1500
      key_type: "main_keyboard"
      
    shield:
      potion_key: "3"                         # 护盾药剂按键
      cooldown: 1000
      key_type: "main_keyboard"
      
  # 热键配置
  hotkeys:
    start_service: "F9"                       # 启动服务
    stop_service: "F10"                       # 停止服务
    test_keys: "F11"                          # 测试按键
    emergency_stop: "F12"                     # 紧急停止
    
  # reWASD兼容性
  rewasd_compatibility:
    enable: true                              # 启用reWASD兼容
    use_hardware_keys: true                   # 使用硬件按键
    bypass_detection: true                    # 绕过检测

# ===================================================================
# 屏幕和显示配置
# ===================================================================
screen:
  # 显示器配置
  monitor:
    index: 1                                  # 显示器索引(1为主显示器)
    resolution:
      width: 2560                             # 屏幕宽度
      height: 1440                            # 屏幕高度
    scaling: 1.0                              # 缩放比例
    
  # 游戏窗口配置
  game_window:
    title: "Path of Exile 2"                 # 游戏窗口标题
    class: ""                                 # 窗口类名
    auto_focus: false                         # 自动聚焦
    
  # 截图配置
  capture:
    method: "direct_pixel"                   # 截图方法: direct_pixel, mss, win32
    color_space: "HSV"                       # 颜色空间: RGB, HSV, LAB
    anti_flicker: true                        # 防闪烁

# ===================================================================
# 预设配置
# ===================================================================
presets:
  # 2K分辨率预设
  "2k_default":
    screen:
      resolution: {width: 2560, height: 1440}
    detection:
      health:
        detection_area: {x: 1760, y: 865, width: 25, height: 180}
      mana:
        detection_area: {x: 1790, y: 865, width: 25, height: 180}
        
  # 4K分辨率预设
  "4k_default":
    screen:
      resolution: {width: 3840, height: 2160}
    detection:
      health:
        detection_area: {x: 2640, y: 1297, width: 38, height: 270}
      mana:
        detection_area: {x: 2685, y: 1297, width: 38, height: 270}
        
  # 1080P分辨率预设
  "1080p_default":
    screen:
      resolution: {width: 1920, height: 1080}
    detection:
      health:
        detection_area: {x: 1320, y: 649, width: 19, height: 135}
      mana:
        detection_area: {x: 1342, y: 649, width: 19, height: 135}

# ===================================================================
# 高级配置
# ===================================================================
advanced:
  # 算法配置
  algorithm:
    detection_method: "bottom_up_scan"        # 检测方法
    color_tolerance: 10                       # 颜色容差
    min_valid_lines: 3                        # 最小有效行数
    edge_detection: false                     # 边缘检测
    
  # 优化配置
  optimization:
    use_threading: true                       # 使用多线程
    thread_pool_size: 4                       # 线程池大小
    memory_limit: 100                         # 内存限制(MB)
    cpu_limit: 10                             # CPU限制(%)
    
  # 安全配置
  security:
    admin_required: true                      # 需要管理员权限
    process_whitelist: ["PathOfExile_x64.exe"] # 进程白名单
    auto_pause_on_alt_tab: true               # Alt+Tab时自动暂停
    
  # 统计配置
  statistics:
    enable_stats: true                        # 启用统计
    save_interval: 60                         # 保存间隔(秒)
    max_history: 1000                         # 最大历史记录
    
# ===================================================================
# 用户界面配置
# ===================================================================
ui:
  # 主界面
  main_window:
    title: "POE2 自动喝药 - 混合架构"
    size: {width: 600, height: 500}
    position: {x: 100, y: 100}
    always_on_top: false
    
  # 状态显示
  status_display:
    show_percentage: true                     # 显示百分比
    show_cooldown: true                       # 显示冷却时间
    update_interval: 100                      # 更新间隔(ms)
    color_coding: true                        # 颜色编码
    
  # 日志配置
  logging:
    max_lines: 1000                           # 最大日志行数
    auto_scroll: true                         # 自动滚动
    timestamp_format: "HH:mm:ss"              # 时间戳格式
    
# ===================================================================
# 版本信息
# ===================================================================
version:
  config_version: "1.0.0"
  python_engine: "1.0.0"
  ahk_input: "1.0.0"
  last_updated: "2024-12-19"
  author: "POE2 Auto Drink Team"