# UV 锁定文件说明

## 📋 什么是锁定文件？

锁定文件（`uv.lock`）是 UV 自动生成的文件，它记录了项目的确切依赖版本和依赖树。这确保了在不同环境中安装相同版本的依赖包。

## 🎯 锁定文件的作用

### 1. 版本一致性
- 确保开发、测试、生产环境使用相同的依赖版本
- 避免"在我机器上能运行"的问题
- 防止依赖版本冲突

### 2. 可重现构建
- 任何人都可以重现完全相同的环境
- CI/CD 流水线结果一致
- 团队协作更加稳定

### 3. 安全性
- 锁定已知安全的版本
- 防止恶意包的意外安装
- 便于安全审计

## 🔧 如何使用锁定文件

### 生成锁定文件
```bash
# UV 会在首次运行 sync 时自动生成
uv sync

# 或者显式生成锁定文件
uv lock
```

### 从锁定文件安装
```bash
# 严格按照锁定文件安装（推荐用于生产环境）
uv sync --locked

# 普通安装（会更新锁定文件）
uv sync
```

### 更新锁定文件
```bash
# 更新所有依赖到最新兼容版本
uv lock --upgrade

# 更新特定包
uv lock --upgrade-package numpy

# 重新解析依赖（不升级版本）
uv lock --refresh
```

## 📁 文件管理

### 版本控制
```bash
# 应该提交到版本控制系统
git add uv.lock
git commit -m "Update dependencies lock file"
```

### .gitignore 配置
```gitignore
# 不要忽略 uv.lock
# uv.lock  # ❌ 错误做法

# 但可以忽略缓存目录
.uv/
__pycache__/
*.pyc
.venv/
```

## 🚀 项目工作流

### 开发者工作流
```bash
# 1. 克隆项目
git clone <repository>
cd project

# 2. 安装依赖（使用锁定版本）
uv sync --locked

# 3. 开发...

# 4. 添加新依赖
uv add requests

# 5. 提交更改（包括更新的锁定文件）
git add .
git commit -m "Add requests dependency"
```

### 生产部署工作流
```bash
# 生产环境严格使用锁定版本
uv sync --locked --no-dev
```

## 🔍 锁定文件内容解析

### 文件结构
```toml
version = 1
requires-python = ">=3.8"

[[package]]
name = "numpy"
version = "1.24.3"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "...", hash = "sha256:..." }
wheels = [
    { url = "...", hash = "sha256:..." },
]

[[package]]
name = "opencv-python"
version = "4.8.1.78"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "numpy" },
]
```

### 关键字段说明
- `name`: 包名
- `version`: 确切版本号
- `source`: 包来源（PyPI、Git等）
- `hash`: 文件哈希值（确保完整性）
- `dependencies`: 依赖关系

## ⚠️ 常见问题

### 1. 锁定文件冲突
```bash
# 解决合并冲突后重新生成
uv lock --refresh
```

### 2. 依赖解析失败
```bash
# 清理缓存重试
uv cache clean
uv lock --refresh
```

### 3. 版本不兼容
```bash
# 检查依赖树
uv tree

# 强制更新有问题的包
uv lock --upgrade-package problematic-package
```

### 4. 锁定文件过时
```bash
# 检查是否有更新
uv lock --dry-run --upgrade

# 应用更新
uv lock --upgrade
```

## 🎯 最佳实践

### 1. 定期更新
```bash
# 每周检查更新
uv lock --dry-run --upgrade

# 测试通过后应用
uv lock --upgrade
uv sync
```

### 2. 分环境管理
```bash
# 开发环境（包含开发依赖）
uv sync

# 生产环境（仅生产依赖）
uv sync --locked --no-dev

# 测试环境
uv sync --locked --group test
```

### 3. 安全检查
```bash
# 检查已知漏洞（需要额外工具）
uv run safety check

# 或使用 pip-audit
uv run pip-audit
```

### 4. 性能优化
```bash
# 使用本地缓存
export UV_CACHE_DIR=/path/to/cache

# 并行安装
export UV_CONCURRENT_DOWNLOADS=10
```

## 📊 与其他工具对比

| 特性 | UV Lock | Poetry Lock | Pipenv Lock |
|------|---------|-------------|-------------|
| 生成速度 | ⚡ 极快 | 🐌 慢 | 🐌 慢 |
| 文件大小 | 📦 小 | 📦 中等 | 📦 大 |
| 跨平台 | ✅ 是 | ✅ 是 | ✅ 是 |
| 哈希验证 | ✅ 是 | ✅ 是 | ✅ 是 |
| 依赖解析 | ⚡ 快 | 🐌 慢 | 🐌 慢 |

## 🔧 故障排除

### 常见错误及解决方案

#### 错误："Lock file is out of date"
```bash
# 解决方案：重新生成锁定文件
uv lock
```

#### 错误："Dependency resolution failed"
```bash
# 解决方案：清理缓存并重试
uv cache clean
uv lock --refresh
```

#### 错误："Hash mismatch"
```bash
# 解决方案：重新下载并验证
uv cache clean
uv sync --refresh
```

## 📚 相关命令参考

```bash
# 锁定文件操作
uv lock                    # 生成/更新锁定文件
uv lock --upgrade          # 升级所有依赖
uv lock --refresh          # 重新解析依赖
uv lock --dry-run          # 预览更改

# 安装操作
uv sync                    # 从锁定文件安装
uv sync --locked           # 严格按锁定文件安装
uv sync --no-dev           # 不安装开发依赖
uv sync --refresh          # 强制重新下载

# 检查操作
uv tree                    # 显示依赖树
uv list                    # 列出已安装包
uv show package-name       # 显示包信息
```

---

## 🎉 总结

锁定文件是现代 Python 项目管理的重要组成部分，它提供了：

1. **可重现性** - 确保环境一致
2. **安全性** - 防止恶意包
3. **稳定性** - 避免版本冲突
4. **协作性** - 团队环境统一

正确使用锁定文件可以大大提高项目的稳定性和可维护性！ 🚀