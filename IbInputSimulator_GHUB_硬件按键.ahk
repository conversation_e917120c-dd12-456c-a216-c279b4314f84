#Requires AutoHotkey v2.0 64-bit
#SingleInstance Force

; ===================================================================
; IbInputSimulator GHUB 硬件按键发送器 + POE2血量检测
; 专门用于通过 Logitech G HUB 驱动发送硬件按键，供 reWASD 识别和重映射
; 集成血量检测，自动喝药和技能释放
; ===================================================================

; 包含 IbInputSimulator 库
#Include "IbInputSimulator\IbInputSimulator.ahk"

; ===================================================================
; 配置文件管理系统
; ===================================================================
class ConfigManager {
    static configFile := A_ScriptDir . "\poe2_config.ini"
    
    ; 默认配置
    static defaultConfig := {
        ; 血量检测配置
        hp_x: 1760,
        hp_y: 865,
        hp_width: 25,
        hp_height: 180,
        hp_enabled: false,
        
        ; 按键配置
        drink_key: "4",
        skill_key: "5",
        
        ; 时序配置
        drink_cooldown: 2000,
        skill_interval: 800,
        skill_hold_time: 80,
        detection_interval: 200,
        
        ; 血量阈值
        drink_threshold: 75,
        skill_stop_threshold: 30
    }
    
    ; 加载配置
    static LoadConfig() {
        config := this.defaultConfig.Clone()
        
        if (!FileExist(this.configFile)) {
            this.SaveConfig(config)
            return config
        }
        
        try {
            ; 读取血量检测配置
            config.hp_x := Integer(IniRead(this.configFile, "BloodDetection", "x", config.hp_x))
            config.hp_y := Integer(IniRead(this.configFile, "BloodDetection", "y", config.hp_y))
            config.hp_width := Integer(IniRead(this.configFile, "BloodDetection", "width", config.hp_width))
            config.hp_height := Integer(IniRead(this.configFile, "BloodDetection", "height", config.hp_height))
            config.hp_enabled := (IniRead(this.configFile, "BloodDetection", "enabled", config.hp_enabled) = "true")
            
            ; 读取按键配置
            config.drink_key := IniRead(this.configFile, "Keys", "drink_key", config.drink_key)
            config.skill_key := IniRead(this.configFile, "Keys", "skill_key", config.skill_key)
            
            ; 读取时序配置
            config.drink_cooldown := Integer(IniRead(this.configFile, "Timing", "drink_cooldown", config.drink_cooldown))
            config.skill_interval := Integer(IniRead(this.configFile, "Timing", "skill_interval", config.skill_interval))
            config.skill_hold_time := Integer(IniRead(this.configFile, "Timing", "skill_hold_time", config.skill_hold_time))
            config.detection_interval := Integer(IniRead(this.configFile, "Timing", "detection_interval", config.detection_interval))
            
            ; 读取阈值配置
            config.drink_threshold := Integer(IniRead(this.configFile, "Thresholds", "drink_threshold", config.drink_threshold))
            config.skill_stop_threshold := Integer(IniRead(this.configFile, "Thresholds", "skill_stop_threshold", config.skill_stop_threshold))
            
        } catch Error as e {
            ; 配置文件读取失败时，输出警告但不阻止程序运行
            ; AddInfo("⚠️ 配置文件读取失败，使用默认配置: " . e.message)
        }
        
        return config
    }
    
    ; 保存配置
    static SaveConfig(config) {
        try {
            ; 保存血量检测配置
            IniWrite(config.hp_x, this.configFile, "BloodDetection", "x")
            IniWrite(config.hp_y, this.configFile, "BloodDetection", "y")
            IniWrite(config.hp_width, this.configFile, "BloodDetection", "width")
            IniWrite(config.hp_height, this.configFile, "BloodDetection", "height")
            IniWrite(config.hp_enabled ? "true" : "false", this.configFile, "BloodDetection", "enabled")
            
            ; 保存按键配置
            IniWrite(config.drink_key, this.configFile, "Keys", "drink_key")
            IniWrite(config.skill_key, this.configFile, "Keys", "skill_key")
            
            ; 保存时序配置
            IniWrite(config.drink_cooldown, this.configFile, "Timing", "drink_cooldown")
            IniWrite(config.skill_interval, this.configFile, "Timing", "skill_interval")
            IniWrite(config.skill_hold_time, this.configFile, "Timing", "skill_hold_time")
            IniWrite(config.detection_interval, this.configFile, "Timing", "detection_interval")
            
            ; 保存阈值配置
            IniWrite(config.drink_threshold, this.configFile, "Thresholds", "drink_threshold")
            IniWrite(config.skill_stop_threshold, this.configFile, "Thresholds", "skill_stop_threshold")
            
            ; AddInfo("✅ 配置已保存到: " . this.configFile)
            
        } catch Error as e {
            ; AddInfo("❌ 配置保存失败: " . e.message)
        }
    }
    
    ; 自动检测分辨率并调整坐标
    static AutoAdjustForResolution(config) {
        ; 基准分辨率 1920x1080
        baseWidth := 1920
        baseHeight := 1080
        
        ; 当前分辨率
        currentWidth := A_ScreenWidth
        currentHeight := A_ScreenHeight
        
        ; 如果分辨率相同，无需调整
        if (currentWidth = baseWidth && currentHeight = baseHeight) {
            return config
        }
        
        ; 计算缩放比例
        scaleX := currentWidth / baseWidth
        scaleY := currentHeight / baseHeight
        
        ; 调整坐标
        config.hp_x := Round(config.hp_x * scaleX)
        config.hp_y := Round(config.hp_y * scaleY)
        config.hp_width := Round(config.hp_width * scaleX)
        config.hp_height := Round(config.hp_height * scaleY)
        
        ; AddInfo("📐 分辨率自适应调整: " . currentWidth . "x" . currentHeight)
        ; AddInfo("🎯 血量检测区域已调整为: " . config.hp_x . "," . config.hp_y . " " . config.hp_width . "x" . config.hp_height)
        
        return config
    }
}

; ===================================================================
; 全局变量
; ===================================================================
global ghubInitialized := false
global isRunning := false
global sendInterval := 200    ; 检测间隔 200ms
global hpDetectionInterval := 50  ; 血量检测更频繁的间隔 50ms
global mainGui := ""          ; 主GUI对象

; 血量检测相关变量
global hpDetectionEnabled := false
global lastHP := 100
global skillReleaseActive := false
global lastDrinkTime := 0
global drinkCooldown := 2000  ; 喝药冷却时间 2秒

; 技能按键时序控制
global lastSkillTime := 0
global skillInterval := 800   ; 技能间隔 800ms
global skillHoldTime := 80    ; 技能按下时间 80ms
global skillKeyPressed := false  ; 技能按键状态
global systemJustStarted := false  ; 系统重置标志

; POE2窗口检测
global poe2WindowActive := false

; 缓存配置参数，避免每次循环读取GUI
global cachedDrinkKey := "4"
global cachedSkillKey := "5"
global cachedDrinkCooldown := 2000
global cachedSkillInterval := 800
global cachedSkillHoldTime := 80

; 血量检测配置
global hpConfig := {
    x: 1760,        ; 检测区域左上角X
    y: 865,         ; 检测区域左上角Y
    width: 25,      ; 检测区域宽度
    height: 180,    ; 检测区域高度
    scanX: 1772,    ; 扫描中心线X坐标 (整数)
    topY: 865,      ; 顶部Y坐标
    bottomY: 1045   ; 底部Y坐标
}

; 性能优化配置
global performanceMode := true  ; 默认启用性能模式
global debugMode := false       ; 调试模式（更多日志输出）

; ===================================================================
; 回调函数定义 - 必须在GUI创建之前定义
; ===================================================================

; 初始化 Logitech G HUB 驱动
InitializeGHUB(*) {
    global ghubInitialized, mainGui
    
    AddInfo("正在初始化 Logitech G HUB 驱动...")
    
    try {
        ; 使用 LogitechGHubNew 驱动，模式1（接管AHK输入）
        IbSendInit("LogitechGHubNew", 1)
        ghubInitialized := true
        
        ; 更新状态显示
        mainGui.statusLabel.Text := "● Logitech G HUB 已初始化"
        mainGui.statusLabel.Opt("cGreen")
        
        AddInfo("✅ Logitech G HUB 驱动初始化成功！")
        AddInfo("📋 驱动信息: LogitechGHubNew (硬件级输入)")
        AddInfo("🎯 reWASD 现在可以识别并重映射这些按键")
        
        ; 同步按键状态
        try {
            IbSyncKeyStates()
            AddInfo("🔄 按键状态已同步")
        } catch {
            AddInfo("⚠️ 按键状态同步失败（可忽略）")
        }
        
        TrayTip("GHUB 驱动", "Logitech G HUB 驱动初始化成功", 2)
        
    } catch Error as e {
        AddInfo("❌ Logitech G HUB 驱动初始化失败: " . e.Message)
        AddInfo("💡 请确保:")
        AddInfo("   1. 以管理员身份运行此脚本")
        AddInfo("   2. 已安装 Logitech G HUB 软件")
        AddInfo("   3. G HUB 软件正在运行")
        
        MsgBox("Logitech G HUB 驱动初始化失败！`n`n" .
               "错误信息: " . e.Message . "`n`n" .
               "请确保:`n" .
               "1. 以管理员身份运行此脚本`n" .
               "2. 已安装 Logitech G HUB 软件`n" .
               "3. G HUB 软件正在运行", "初始化失败", 16)
    }
}

; 测试按键输入
TestKeyInput(*) {
    global ghubInitialized, mainGui

    if (!ghubInitialized) {
        MsgBox("请先初始化 Logitech G HUB 驱动！", "错误", 16)
        return
    }

    ; 获取当前配置的按键并转换为主键盘按键
    drinkKey := ConvertToMainKeyboardKey(mainGui.drinkKeyEdit.Text)
    skillKey := ConvertToMainKeyboardKey(mainGui.skillKeyEdit.Text)

    AddInfo("🧪 开始测试按键输入...")
    AddInfo("📝 将在3秒后发送测试按键: " . drinkKey . " 和 " . skillKey . " (主键盘数字键)")
    AddInfo("💡 请切换到记事本或其他文本编辑器查看效果")

    ; 倒计时
    Loop 3 {
        AddInfo("⏰ " . (4-A_Index) . " 秒后开始测试...")
        Sleep(1000)
    }

    try {
        ; 发送测试按键序列
        AddInfo("📤 发送测试序列 (硬件级输入 - 主键盘数字键):")

        ; 测试喝药按键
        AddInfo("  发送喝药按键: " . drinkKey . " (主键盘)")
        IbSend(drinkKey)
        Sleep(500)
        
        ; 测试技能按键
        AddInfo("  发送技能按键: " . skillKey . " (主键盘)")
        IbSend(skillKey)
        Sleep(500)

        AddInfo("✅ 测试完成！如果在目标程序中看到按键输出，说明 GHUB 驱动工作正常")
        AddInfo("🎯 reWASD 应该能够识别并重映射这些主键盘数字键")

    } catch Error as e {
        AddInfo("❌ 测试失败: " . e.Message)
        MsgBox("按键测试失败！`n`n错误信息: " . e.Message, "测试失败", 16)
    }
}

; 切换血量检测
ToggleHPDetection(*) {
    global hpDetectionEnabled, mainGui, skillReleaseActive, appConfig
    
    hpDetectionEnabled := mainGui.hpEnableChk.Value
    appConfig.hp_enabled := hpDetectionEnabled  ; 同步到配置对象
    
    if (hpDetectionEnabled) {
        AddInfo("🩸 血量检测已启用 - 开始独立血量监控")
        mainGui.drinkStatusText.Text := "🟡 待命中"
        mainGui.drinkStatusText.Opt("c0xFF8000")  ; 橙色
        
        ; 启动独立的血量检测循环（100ms频率，提升性能）
        SetTimer(UnifiedMainLoop, 100)
        
    } else {
        AddInfo("🩸 血量检测已禁用")
        mainGui.drinkStatusText.Text := "🔴 未启用"
        mainGui.drinkStatusText.Opt("cRed")
        mainGui.skillStatusText.Text := "🔴 停止"
        mainGui.skillStatusText.Opt("cRed")
        skillReleaseActive := false
        
        ; 如果系统也没有运行，则停止循环
        global isRunning
        if (!isRunning) {
            SetTimer(UnifiedMainLoop, 0)
            AddInfo("🔄 血量检测循环已停止")
            ; 刷新剩余的日志缓冲区
            FlushLogBuffer()
        }
    }
}

; 绘制血量检测区域
DrawHPArea(*) {
    global hpConfig
    
    try {
        ; 显示当前配置信息
        AddInfo("🔲 血量检测区域配置:")
        AddInfo("   位置: X=" . hpConfig.x . " Y=" . hpConfig.y)
        AddInfo("   大小: W=" . hpConfig.width . " H=" . hpConfig.height)
        AddInfo("   扫描线: X=" . hpConfig.scanX)
        
        ; 创建完整的区域边框GUI
        overlayGui := Gui("+AlwaysOnTop +ToolWindow -Caption -MaximizeBox -MinimizeBox", "")
        overlayGui.BackColor := "Red"
        
        ; 显示完整的检测区域边框
        x := hpConfig.x
        y := hpConfig.y
        w := hpConfig.width
        h := hpConfig.height
        
        ; 创建边框效果：4个边框条
        ; 顶部边框
        topGui := Gui("+AlwaysOnTop +ToolWindow -Caption", "")
        topGui.BackColor := "Red"
        topGui.Show("x" . x . " y" . y . " w" . w . " h3 NoActivate")
        WinSetTransparent(200, topGui)
        
        ; 底部边框
        bottomGui := Gui("+AlwaysOnTop +ToolWindow -Caption", "")
        bottomGui.BackColor := "Red"
        bottomGui.Show("x" . x . " y" . (y + h - 3) . " w" . w . " h3 NoActivate")
        WinSetTransparent(200, bottomGui)
        
        ; 左边框
        leftGui := Gui("+AlwaysOnTop +ToolWindow -Caption", "")
        leftGui.BackColor := "Red"
        leftGui.Show("x" . x . " y" . y . " w3 h" . h . " NoActivate")
        WinSetTransparent(200, leftGui)
        
        ; 右边框
        rightGui := Gui("+AlwaysOnTop +ToolWindow -Caption", "")
        rightGui.BackColor := "Red"
        rightGui.Show("x" . (x + w - 3) . " y" . y . " w3 h" . h . " NoActivate")
        WinSetTransparent(200, rightGui)
        
        ; 扫描中心线
        scanGui := Gui("+AlwaysOnTop +ToolWindow -Caption", "")
        scanGui.BackColor := "Yellow"
        scanGui.Show("x" . hpConfig.scanX . " y" . y . " w2 h" . h . " NoActivate")
        WinSetTransparent(255, scanGui)
        
        AddInfo("🔲 血量检测区域已绘制 (红色边框 + 黄色扫描线)")
        AddInfo("📍 20秒后自动关闭，请检查位置是否正确并截图")
        
        ; 将GUI对象存储到全局变量中
        global overlayGuis := [topGui, bottomGui, leftGui, rightGui, scanGui]
        
        ; 20秒后自动关闭所有边框
        SetTimer(CloseAllOverlays, 20000)
        
    } catch Error as e {
        AddInfo("❌ 绘制失败: " . e.message)
    }
}

; 关闭所有覆盖GUI的函数
CloseAllOverlays() {
    global overlayGuis
    
    try {
        if (IsSet(overlayGuis)) {
            Loop overlayGuis.Length {
                overlayGuis[A_Index].Destroy()
            }
            AddInfo("✅ 区域绘制已关闭")
        }
    } catch {
        ; 忽略关闭错误
    }
    
    ; 停止定时器
    SetTimer(CloseAllOverlays, 0)
}

; 鼠标位置检测工具
GetMousePosition(*) {
    AddInfo("🖱️ 鼠标位置检测启动...")
    AddInfo("💡 请在5秒内将鼠标移动到血量条上")
    AddInfo("⏰ 倒计时开始...")
    
    ; 5秒倒计时
    Loop 5 {
        AddInfo("   " . (6-A_Index) . " 秒...")
        Sleep(1000)
    }
    
    ; 检测当前鼠标位置
    MouseGetPos(&mouseX, &mouseY)
    AddInfo("🎯 检测位置: X=" . mouseX . " Y=" . mouseY)
    
    ; 计算相对于当前检测区域的位置
    global hpConfig
    relativeX := mouseX - hpConfig.x
    relativeY := mouseY - hpConfig.y
    
    if (relativeX >= 0 && relativeX <= hpConfig.width && 
        relativeY >= 0 && relativeY <= hpConfig.height) {
        AddInfo("📍 鼠标在检测区域内: 相对位置 (" . relativeX . "," . relativeY . ")")
    } else {
        AddInfo("📍 鼠标在检测区域外")
        AddInfo("💡 建议调整检测区域位置，或将鼠标移动到血量条上重新测试")
    }
    
    ; 显示像素颜色信息和详细的红色检测分析
    try {
        pixelColor := PixelGetColor(mouseX, mouseY)
        r := (pixelColor >> 16) & 0xFF
        g := (pixelColor >> 8) & 0xFF
        b := pixelColor & 0xFF
        
        AddInfo("🎨 像素颜色分析:")
        AddInfo("   RGB值: (" . r . "," . g . "," . b . ")")
        AddInfo("   十六进制: #" . Format("{:06X}", pixelColor))
        
        ; 详细的红色检测分析 - 匹配新的红色检测条件
        ; 排除条件检查
        isWhite := (r >= 240 && g >= 240 && b >= 240)
        isBlack := (r <= 15 && g <= 15 && b <= 15)
        totalBrightness := r + g + b
        isTooDark := (totalBrightness <= 120)
        isGray := (Abs(r - g) <= 8 && Abs(r - b) <= 8 && Abs(g - b) <= 8 && r <= g + 5)
        
        ; 红色检测条件
        redDominant := (r >= 45 && r > g + 8 && r > b + 8)
        traditionalRed := (r >= 60 && r <= 200 && g <= 100 && b <= 100 && r > g + 15 && r > b + 15)
        darkRed := (r >= 50 && r <= 120 && g <= 70 && b <= 70 && r > g + 10 && r > b + 10)
        orangeRed := (r >= 80 && r <= 255 && g >= 20 && g <= 100 && b <= 80 && r > g + 20 && r > b + 30)
        
        finalResult := IsRedColor(mouseX, mouseY)
        
        AddInfo("🔍 红色检测分析 (严格版):")
        AddInfo("   排除白色: " . (isWhite ? "❌ 是白色" : "✅ 不是白色"))
        AddInfo("   排除黑色: " . (isBlack ? "❌ 是黑色" : "✅ 不是黑色"))
        AddInfo("   排除过暗: " . (isTooDark ? "❌ 太暗(总亮度" . totalBrightness . "≤120)" : "✅ 亮度足够(总亮度" . totalBrightness . ")"))
        AddInfo("   排除灰色: " . (isGray ? "❌ 是灰色" : "✅ 不是灰色"))
        AddInfo("   红色占主导: " . (redDominant ? "✅ 通过" : "❌ 不通过"))
        AddInfo("   传统红色: " . (traditionalRed ? "✅ 通过" : "❌ 不通过"))
        AddInfo("   暗红色: " . (darkRed ? "✅ 通过" : "❌ 不通过"))
        AddInfo("   橙红色: " . (orangeRed ? "✅ 通过" : "❌ 不通过"))
        AddInfo("   最终结果: " . (finalResult ? "✅ 识别为红色" : "❌ 不是红色"))
        
        ; 如果是红色，建议用这个位置更新检测区域
        if (finalResult) {
            AddInfo("🎯 建议：此位置识别为红色，可考虑调整检测区域中心到此处")
        }
        
    } catch Error as e {
        AddInfo("❌ 无法获取像素颜色: " . e.message)
    }
}

; 自动定位血量条
AutoDetectHPBar(*) {
    AddInfo("🔍 开始自动检测血量条位置...")
    AddInfo("💡 请确保POE2游戏窗口可见且血量条显示")
    
    ; 扫描屏幕寻找可能的血量条位置
    screenWidth := A_ScreenWidth
    screenHeight := A_ScreenHeight
    
    ; 从屏幕右下角开始扫描 (血量条通常在这个区域)
    startX := Round(screenWidth * 0.8)  ; 从80%宽度开始
    endX := screenWidth - 50
    startY := Round(screenHeight * 0.7)  ; 从70%高度开始
    endY := screenHeight - 50
    
    bestX := 0
    bestY := 0
    maxRedHeight := 0
    
    ; 粗略扫描，寻找连续的红色像素列
    Loop {
        x := startX + (A_Index - 1) * 10
        if (x > endX)
            break
            
        y := startY
        redCount := 0
        redStartY := 0
        
        ; 垂直扫描这一列
        Loop {
            currentY := y + (A_Index - 1) * 2
            if (currentY > endY)
                break
                
            if (IsRedColor(x, currentY)) {
                if (redCount = 0) {
                    redStartY := currentY
                }
                redCount++
            } else if (redCount > 0) {
                ; 红色区域结束，检查是否是最佳候选
                redHeight := redCount * 2
                if (redHeight > maxRedHeight && redHeight > 20) {  ; 至少40像素高
                    maxRedHeight := redHeight
                    bestX := x - 12  ; 向左偏移，作为检测区域左边缘
                    bestY := redStartY
                }
                redCount := 0
            }
        }
    }
    
    if (maxRedHeight > 20) {
        ; 找到可能的血量条
        global hpConfig, mainGui
        
        newX := bestX
        newY := bestY
        newWidth := 25
        newHeight := maxRedHeight + 10
        
        AddInfo("🎯 发现可能的血量条位置:")
        AddInfo("   建议坐标: X=" . newX . " Y=" . newY)
        AddInfo("   建议大小: W=" . newWidth . " H=" . newHeight)
        
        ; 询问是否应用新坐标
        result := MsgBox("发现可能的血量条位置:`n`n" .
                        "X: " . newX . "`n" .
                        "Y: " . newY . "`n" .
                        "宽度: " . newWidth . "`n" .
                        "高度: " . newHeight . "`n`n" .
                        "是否应用这些坐标？", "自动检测结果", 4)
        
        if (result = "Yes") {
            ; 更新GUI和配置
            mainGui.hpXEdit.Text := newX
            mainGui.hpYEdit.Text := newY
            mainGui.hpWEdit.Text := newWidth
            mainGui.hpHEdit.Text := newHeight
            
            ; 应用设置
            ApplyHPSettings()
            
            ; 绘制新区域
            DrawHPArea()
        }
    } else {
        AddInfo("❌ 未找到明显的血量条，请手动调整位置")
        AddInfo("💡 建议:")
        AddInfo("   1. 确保游戏窗口可见")
        AddInfo("   2. 血量不要是满血状态")
        AddInfo("   3. 使用鼠标位置检测功能手动定位")
    }
}

; 测试血量检测
TestHPDetection(*) {
    global hpDetectionEnabled, hpConfig
    
    if (!hpDetectionEnabled) {
        MsgBox("请先启用血量检测！", "提示", 48)
        return
    }
    
    AddInfo("🧪 开始详细测试血量检测...")
    
    ; 详细的血量检测分析
    try {
        scanX := hpConfig.scanX
        topY := hpConfig.topY
        bottomY := hpConfig.bottomY
        totalHeight := bottomY - topY + 1
        
        AddInfo("📏 检测参数:")
        AddInfo("   扫描X坐标: " . scanX)
        AddInfo("   顶部Y坐标: " . topY)
        AddInfo("   底部Y坐标: " . bottomY)
        AddInfo("   总高度: " . totalHeight)
        
        ; 扫描并分析每个像素
        redPixelCount := 0
        firstRedY := -1
        lastRedY := -1
        
        AddInfo("🔍 开始逐像素扫描分析...")
        
        ; 每10个像素采样一次，显示颜色信息
        sampleCount := 0
        Loop {
            y := topY + (A_Index - 1)
            if (y > bottomY)
                break
                
            isRed := IsRedColor(scanX, y)
            if (isRed) {
                redPixelCount++
                if (firstRedY = -1)
                    firstRedY := y
                lastRedY := y
            }
            
            ; 每10个像素显示一次采样信息
            if (Mod(A_Index - 1, 10) = 0) {
                c := PixelGetColor(scanX, y)
                r := (c >> 16) & 0xFF
                g := (c >> 8) & 0xFF
                b := c & 0xFF
                AddInfo("   Y=" . y . " RGB(" . r . "," . g . "," . b . ") " . (isRed ? "✅红色" : "❌非红"))
                sampleCount++
                if (sampleCount >= 5) {  ; 限制输出量
                    AddInfo("   ... (更多像素)")
                    break
                }
            }
        }
        
        ; 分析结果
        AddInfo("📊 扫描结果分析:")
        AddInfo("   红色像素数量: " . redPixelCount . "/" . totalHeight)
        AddInfo("   首个红色Y: " . (firstRedY = -1 ? "无" : firstRedY))
        AddInfo("   最后红色Y: " . (lastRedY = -1 ? "无" : lastRedY))
        
        if (redPixelCount > 0) {
            redHeight := lastRedY - topY + 1
            hp := (redHeight / totalHeight) * 100
            AddInfo("   红色区域高度: " . redHeight)
            AddInfo("   计算血量: " . Round(hp, 1) . "%")
        } else {
            AddInfo("   ❌ 未检测到红色像素！")
            AddInfo("💡 可能的原因：")
            AddInfo("   1. 检测区域位置不正确")
            AddInfo("   2. 血量条颜色不符合红色检测条件")
            AddInfo("   3. 游戏界面被其他窗口遮挡")
        }
        
        ; 使用原函数获取最终结果
        finalHP := GetHP()
        AddInfo("📈 最终检测结果: " . finalHP . "%")
        
        ; 更新显示
        UpdateHPDisplay(finalHP)
        
    } catch Error as e {
        AddInfo("❌ 详细测试失败: " . e.message)
    }
}

; 应用血量检测设置
ApplyHPSettings(*) {
    global hpConfig, mainGui
    
    try {
        hpConfig.x := Integer(mainGui.hpXEdit.Text)
        hpConfig.y := Integer(mainGui.hpYEdit.Text)
        hpConfig.width := Integer(mainGui.hpWEdit.Text)
        hpConfig.height := Integer(mainGui.hpHEdit.Text)
        
        ; 重新计算扫描参数
        hpConfig.scanX := Round(hpConfig.x + (hpConfig.width / 2))
        hpConfig.topY := hpConfig.y
        hpConfig.bottomY := hpConfig.y + hpConfig.height
        
        AddInfo("✅ 血量检测设置已更新")
        AddInfo("📍 检测区域: X=" . hpConfig.x . " Y=" . hpConfig.y . " W=" . hpConfig.width . " H=" . hpConfig.height)
        
    } catch Error as e {
        MsgBox("❌ 设置错误: " . e.message, "错误", 16)
    }
}

; 更新缓存配置参数
UpdateCachedConfig(*) {
    global cachedDrinkKey, cachedSkillKey, cachedDrinkCooldown
    global cachedSkillInterval, cachedSkillHoldTime, mainGui
    global appConfig, hpConfig
    
    try {
        ; 更新缓存变量
        cachedDrinkKey := ConvertToMainKeyboardKey(mainGui.drinkKeyEdit.Text)
        cachedSkillKey := ConvertToMainKeyboardKey(mainGui.skillKeyEdit.Text)
        cachedDrinkCooldown := Integer(mainGui.cooldownEdit.Text)
        cachedSkillInterval := Integer(mainGui.skillIntervalEdit.Text)
        cachedSkillHoldTime := Integer(mainGui.skillHoldEdit.Text)
        
        ; 同时更新配置对象
        appConfig.drink_key := cachedDrinkKey
        appConfig.skill_key := cachedSkillKey
        appConfig.drink_cooldown := cachedDrinkCooldown
        appConfig.skill_interval := cachedSkillInterval
        appConfig.skill_hold_time := cachedSkillHoldTime
        appConfig.detection_interval := Integer(mainGui.intervalEdit.Text)
        
        ; 更新血量检测区域配置
        appConfig.hp_x := Integer(mainGui.hpXEdit.Text)
        appConfig.hp_y := Integer(mainGui.hpYEdit.Text)
        appConfig.hp_width := Integer(mainGui.hpWEdit.Text)
        appConfig.hp_height := Integer(mainGui.hpHEdit.Text)
        
        ; 更新血量检测配置
        hpConfig.x := appConfig.hp_x
        hpConfig.y := appConfig.hp_y
        hpConfig.width := appConfig.hp_width
        hpConfig.height := appConfig.hp_height
        hpConfig.scanX := Round(appConfig.hp_x + (appConfig.hp_width / 2))
        hpConfig.topY := appConfig.hp_y
        hpConfig.bottomY := appConfig.hp_y + appConfig.hp_height
        
        AddInfo("⚙️ 配置已缓存更新")
        AddInfo("💊 喝药按键: " . cachedDrinkKey . " | 冷却: " . cachedDrinkCooldown . "ms")
        AddInfo("⚔️ 技能按键: " . cachedSkillKey . " | 按下: " . cachedSkillHoldTime . "ms | 间隔: " . cachedSkillInterval . "ms")
        AddInfo("🎯 血量检测区域: " . appConfig.hp_x . "," . appConfig.hp_y . " " . appConfig.hp_width . "x" . appConfig.hp_height)
        
    } catch Error as e {
        AddInfo("❌ 配置更新失败: " . e.Message)
        MsgBox("配置更新失败！`n`n错误信息: " . e.Message, "配置错误", 16)
    }
}

; 保存当前配置
SaveCurrentConfig(*) {
    global appConfig, ConfigManager, hpDetectionEnabled
    
    try {
        ; 确保血量检测状态也被保存
        appConfig.hp_enabled := hpDetectionEnabled
        
        ConfigManager.SaveConfig(appConfig)
        AddInfo("✅ 配置已保存到: " . ConfigManager.configFile)
        TrayTip("配置保存", "配置已成功保存", 2)
    } catch Error as e {
        AddInfo("❌ 配置保存失败: " . e.message)
        MsgBox("配置保存失败！`n`n错误信息: " . e.message, "配置错误", 16)
    }
}

; ===================================================================
; 创建GUI界面
; ===================================================================
CreateGUI() {
    ; 创建主窗口 - 增大宽度以容纳右侧日志窗口
    myGui := Gui("+Resize +MinSize750x650", "POE2自动喝药 + GHUB硬件按键发送器 v2.0")
    myGui.SetFont("s9", "Microsoft YaHei")
    myGui.BackColor := "0xF0F0F0"

    ; 标题
    myGui.AddText("x10 y10 w400 h30 Center", "🎮 POE2自动喝药 + GHUB硬件按键发送器").SetFont("s12 Bold")

    ; 驱动状态
    myGui.AddText("x10 y50 w100", "GHUB驱动:")
    statusLabel := myGui.AddText("x120 y50 w280 cRed", "● 未初始化")

    ; === 血量检测区域 ===
    myGui.AddText("x10 y80 w400 h2 0x10")
    myGui.AddText("x10 y90 w400 h20 Center", "🩸 血量检测与自动控制").SetFont("s11 Bold")

    ; 血量显示
    myGui.AddText("x20 y120 w80 h25", "当前血量:")
    hpText := myGui.AddText("x110 y120 w100 h25 c0x008000 Center Border", "100.0%")
    hpText.SetFont("s12 Bold")

    ; 血量检测开关
    hpEnableChk := myGui.AddCheckbox("x230 y120 w140 h25", "启用血量检测")
    hpEnableChk.OnEvent("Click", ToggleHPDetection)

    ; 自动功能状态
    myGui.AddText("x20 y150 w80", "自动喝药:")
    drinkStatusText := myGui.AddText("x110 y150 w100 c0x666666", "🔴 未启用")
    myGui.AddText("x230 y150 w80", "技能释放:")
    skillStatusText := myGui.AddText("x320 y150 w50 c0x666666", "🔴 停止")

    ; 血量检测区域配置
    myGui.AddText("x20 y180 w25 h20", "X:")
    hpXEdit := myGui.AddEdit("x45 y177 w50 h23 Number", hpConfig.x)
    myGui.AddText("x105 y180 w25 h20", "Y:")
    hpYEdit := myGui.AddEdit("x130 y177 w50 h23 Number", hpConfig.y)
    myGui.AddText("x190 y180 w25 h20", "W:")
    hpWEdit := myGui.AddEdit("x215 y177 w40 h23 Number", hpConfig.width)
    myGui.AddText("x265 y180 w25 h20", "H:")
    hpHEdit := myGui.AddEdit("x290 y177 w40 h23 Number", hpConfig.height)

    ; 血量检测按钮
    drawHPBtn := myGui.AddButton("x20 y205 w80 h25", "🔲 绘制区域")
    drawHPBtn.OnEvent("Click", DrawHPArea)
    testHPBtn := myGui.AddButton("x110 y205 w80 h25", "🧪 测试检测")
    testHPBtn.OnEvent("Click", TestHPDetection)
    applyHPBtn := myGui.AddButton("x200 y205 w80 h25", "✅ 应用设置")
    applyHPBtn.OnEvent("Click", ApplyHPSettings)
    
    ; 第二行按钮
    autoDetectBtn := myGui.AddButton("x20 y235 w80 h25", "🔍 自动定位")
    autoDetectBtn.OnEvent("Click", AutoDetectHPBar)
    quickTestBtn := myGui.AddButton("x110 y235 w80 h25", "⚡ 快速检测")
    quickTestBtn.OnEvent("Click", QuickHPTest)
    mouseBtn := myGui.AddButton("x200 y235 w80 h25", "🖱️ 鼠标位置")
    mouseBtn.OnEvent("Click", GetMousePosition)
    debugBtn := myGui.AddButton("x290 y235 w80 h25", "🔍 窗口调试")
    debugBtn.OnEvent("Click", DebugWindowDetection)

    ; === 控制按钮区域 ===
    myGui.AddText("x10 y270 w400 h2 0x10")
    myGui.AddText("x10 y280 w400 h20 Center", "🎮 GHUB控制面板").SetFont("s11 Bold")

    ; 初始化和控制按钮
    initBtn := myGui.AddButton("x20 y310 w110 h35", "🔧 初始化GHUB")
    initBtn.OnEvent("Click", InitializeGHUB)
    testBtn := myGui.AddButton("x140 y310 w110 h35", "🧪 测试按键")
    testBtn.OnEvent("Click", TestKeyInput)
    toggleBtn := myGui.AddButton("x260 y310 w110 h35", "▶️ 启动系统")
    toggleBtn.OnEvent("Click", ToggleSystem)

    ; === 按键设置区域 ===
    myGui.AddText("x10 y355 w400 h20 Center", "⚙️ 按键设置").SetFont("s11 Bold")

    ; 喝药按键设置
    myGui.AddText("x20 y380 w80", "喝药按键:")
    drinkKeyEdit := myGui.AddEdit("x110 y377 w40 h23", cachedDrinkKey)
    myGui.AddText("x160 y380 w80", "技能按键:")
    skillKeyEdit := myGui.AddEdit("x250 y377 w40 h23", cachedSkillKey)
    
    ; 添加说明文字
    myGui.AddText("x20 y405 w370 h15 c0x666666", "💡 使用键盘上方一排的数字键 (1-9,0)，不是小键盘数字键")

    ; 冷却时间设置
    myGui.AddText("x20 y425 w100", "喝药冷却(ms):")
    cooldownEdit := myGui.AddEdit("x130 y422 w60 h23", cachedDrinkCooldown)
    myGui.AddText("x200 y425 w80", "按键间隔:")
    intervalEdit := myGui.AddEdit("x290 y422 w60 h23", sendInterval)
    
    ; 技能时序设置
    myGui.AddText("x20 y450 w100", "技能按下(ms):")
    skillHoldEdit := myGui.AddEdit("x130 y447 w60 h23", cachedSkillHoldTime)
    myGui.AddText("x200 y450 w80", "技能间隔(ms):")
    skillIntervalEdit := myGui.AddEdit("x290 y447 w60 h23", cachedSkillInterval)

    ; 配置更新按钮
    updateConfigBtn := myGui.AddButton("x200 y472 w80 h23", "🔄 更新")
    updateConfigBtn.OnEvent("Click", UpdateCachedConfig)
    saveConfigBtn := myGui.AddButton("x290 y472 w60 h23", "💾 保存")
    saveConfigBtn.OnEvent("Click", SaveCurrentConfig)

    ; === 右侧大型日志区域 ===
    myGui.AddText("x420 y50 w250 h20 Center", "📝 运行日志").SetFont("s11 Bold")
    clearLogBtn := myGui.AddButton("x680 y50 w60 h20", "🗑️ 清空")
    clearLogBtn.OnEvent("Click", ClearLog)
    infoText := myGui.AddEdit("x420 y75 w320 h420 ReadOnly VScroll")

    ; 快捷键说明
    myGui.AddText("x10 y505 w400 h30",
        "快捷键: F1=启动/停止 | F2=测试按键 | F3=初始化GHUB | F4=状态 | F5=鼠标位置 | F6=技能时序分析 | F7=切换性能模式 | F12=退出")

    ; 存储控件引用
    myGui.statusLabel := statusLabel
    myGui.toggleBtn := toggleBtn
    myGui.infoText := infoText
    myGui.hpText := hpText
    myGui.hpEnableChk := hpEnableChk
    myGui.drinkStatusText := drinkStatusText
    myGui.skillStatusText := skillStatusText
    myGui.hpXEdit := hpXEdit
    myGui.hpYEdit := hpYEdit
    myGui.hpWEdit := hpWEdit
    myGui.hpHEdit := hpHEdit
    myGui.drinkKeyEdit := drinkKeyEdit
    myGui.skillKeyEdit := skillKeyEdit
    myGui.cooldownEdit := cooldownEdit
    myGui.intervalEdit := intervalEdit
    myGui.skillHoldEdit := skillHoldEdit
    myGui.skillIntervalEdit := skillIntervalEdit
    myGui.updateConfigBtn := updateConfigBtn
    myGui.saveConfigBtn := saveConfigBtn

    ; 设置关闭事件
    myGui.OnEvent("Close", (*) => (FlushLogBuffer(), ExitApp()))

    return myGui
}

; ===================================================================
; 启动/停止系统
; ===================================================================
ToggleSystem(*) {
    global isRunning, ghubInitialized, mainGui, hpDetectionEnabled, skillReleaseActive
    global systemJustStarted  ; 添加状态重置标志
    
    if (!ghubInitialized) {
        MsgBox("请先初始化 Logitech G HUB 驱动！", "错误", 16)
        return
    }
    
    if (!isRunning) {
        ; 启动系统
        isRunning := true
        systemJustStarted := true  ; 设置重置标志
        mainGui.toggleBtn.Text := "⏹️ 停止系统"
        mainGui.toggleBtn.Opt("cRed")

        ; 更新缓存配置
        UpdateCachedConfig()

        AddInfo("▶️ POE2自动系统已启动")
        AddInfo("🩸 血量检测: " . (hpDetectionEnabled ? "启用 (统一循环)" : "禁用"))
        AddInfo("💊 自动喝药: 血量 < 75% 时发送按键 " . cachedDrinkKey)
        AddInfo("⚔️ 技能释放: 血量 > 30% 时发送按键 " . cachedSkillKey)
        AddInfo("🛡️ 技能停止: 血量 ≤ 30% 时停止发送按键 " . cachedSkillKey)

        ; 启动或确保统一主循环运行 (100ms频率，优化性能)
        SetTimer(UnifiedMainLoop, 100)

        TrayTip("系统状态", "POE2自动系统已启动", 1)
        
    } else {
        ; 停止系统
        isRunning := false
        systemJustStarted := true  ; 设置重置标志以确保状态清理
        mainGui.toggleBtn.Text := "▶️ 启动系统"
        mainGui.toggleBtn.Opt("cGreen")
        
        ; 强制释放所有可能按下的按键
        try {
            ; 强制释放喝药和技能按键
            IbSend("{" . cachedDrinkKey . " up}")
            IbSend("{" . cachedSkillKey . " up}")
            AddInfo("🛡️ 系统停止：强制释放所有按键")
        } catch {
            ; 忽略释放错误
        }
        
        ; 重置所有状态
        skillReleaseActive := false
        ResetAllStates()
        mainGui.drinkStatusText.Text := hpDetectionEnabled ? "🟡 待命中" : "🔴 未启用"
        if (hpDetectionEnabled) {
            mainGui.drinkStatusText.Opt("c0xFF8000")  ; 橙色
        } else {
            mainGui.drinkStatusText.Opt("cRed")
        }
        mainGui.skillStatusText.Text := "🔴 停止"
        mainGui.skillStatusText.Opt("cRed")
        
        ; 如果血量检测也没启用，则停止统一主循环
        if (!hpDetectionEnabled) {
            SetTimer(UnifiedMainLoop, 0)
            AddInfo("🔄 统一主循环已停止")
        }
        
        AddInfo("⏹️ POE2自动系统已完全停止")
        TrayTip("系统状态", "POE2自动系统已停止", 1)
    }
}

; ===================================================================
; 统一主循环 - 优化性能版本
; ===================================================================
UnifiedMainLoop() {
    global isRunning, hpDetectionEnabled, lastHP
    static frameCounter := 0
    static lastGUIUpdate := 0
    static lastHPDetection := 0
    static lastKeyAction := 0
    static lastHeartbeat := 0
    static lastWindowCheck := 0
    static performanceMonitor := 0
    static lastPerformanceLog := 0
    
    loopStartTime := A_TickCount
    currentTime := A_TickCount
    frameCounter++
    
    ; 性能监控（每30秒一次）
    if ((currentTime - lastPerformanceLog) >= 30000) {
        avgLoopTime := performanceMonitor / 30  ; 平均每秒的循环耗时
        AddInfo("📊 性能监控: 平均循环耗时 " . Round(avgLoopTime, 2) . "ms/秒")
        performanceMonitor := 0
        lastPerformanceLog := currentTime
    }
    
    ; 检查POE2窗口状态（每2秒检查一次，降低频率）
    static currentPOE2Status := false
    if ((currentTime - lastWindowCheck) >= 2000) {
        newPOE2Status := IsPOE2ActiveFast()  ; 使用快速检测
        if (newPOE2Status != currentPOE2Status) {
            currentPOE2Status := newPOE2Status
            if (isRunning) {  ; 只有系统运行时才输出窗口状态变化
                AddInfo("🎮 POE2窗口状态: " . (currentPOE2Status ? "✅ 激活" : "❌ 未激活"))
            }
        }
        lastWindowCheck := currentTime
    }
    
    ; 血量检测（每150ms一次，降低频率以提升性能）
    if (hpDetectionEnabled && (currentTime - lastHPDetection) >= 150) {
        try {
            ; 根据性能模式选择检测方法
            global performanceMode
            if (performanceMode) {
                newHP := GetHPOptimized()  ; 使用优化版本
            } else {
                newHP := GetHP()  ; 使用完整版本
            }
            
            ; 只有变化超过1%才更新，减少无意义的更新
            if (Abs(newHP - lastHP) > 1.0) {
                lastHP := newHP
            }
            lastHPDetection := currentTime
        } catch Error as e {
            ; 减少错误输出频率
            static lastHPErrorTime := 0
            if (currentTime - lastHPErrorTime > 10000) {  ; 10秒内只输出一次错误
                AddInfo("❌ 血量检测失败: " . e.message)
                lastHPErrorTime := currentTime
            }
        }
    }
    
    ; 按键操作（每250ms一次，略微降低频率但保持响应性）
    if (isRunning && hpDetectionEnabled && currentPOE2Status && (currentTime - lastKeyAction) >= 250) {
        ProcessKeyActions()
        lastKeyAction := currentTime
    }
    
    ; GUI更新（每1000ms一次，大幅降低频率）
    if (hpDetectionEnabled && (currentTime - lastGUIUpdate) >= 1000) {
        UpdateHPDisplay(lastHP)
        lastGUIUpdate := currentTime
    }
    
    ; 心跳信息（每60秒一次，降低频率）
    if (isRunning && (currentTime - lastHeartbeat) >= 60000) {
        AddInfo("💓 系统运行正常 - 血量: " . lastHP . "% | POE2: " . (currentPOE2Status ? "激活" : "未激活"))
        lastHeartbeat := currentTime
    }
    
    ; 计算循环耗时并累积到性能监控
    loopEndTime := A_TickCount
    loopDuration := loopEndTime - loopStartTime
    performanceMonitor += loopDuration
}

; ===================================================================
; 状态机管理的按键处理逻辑
; ===================================================================
ProcessKeyActions() {
    global lastHP, skillReleaseActive, lastDrinkTime, lastSkillTime
    global cachedDrinkKey, cachedSkillKey, cachedDrinkCooldown
    global cachedSkillInterval, cachedSkillHoldTime, mainGui
    
    currentTime := A_TickCount
    
    ; 使用状态机管理按键操作
    static drinkState := "idle"  ; idle, pressing, cooldown
    static skillState := "idle"  ; idle, pressing, waiting
    static stateChangeTime := 0
    static skillStateChangeTime := 0
    static needsReset := false
    
    ; 检查是否需要重置状态（当系统重启时）
    static lastResetCheck := 0
    if (currentTime - lastResetCheck > 1000) {  ; 每秒检查一次
        global systemJustStarted
        if (IsSet(systemJustStarted) && systemJustStarted) {
            drinkState := "idle"
            skillState := "idle"
            stateChangeTime := 0
            skillStateChangeTime := 0
            systemJustStarted := false  ; 重置标志
            AddInfo("🔄 按键状态机已重置")
        }
        lastResetCheck := currentTime
    }
    
    ; 喝药状态机
    switch drinkState {
        case "idle":
            if (lastHP < 75 && (currentTime - lastDrinkTime) >= cachedDrinkCooldown) {
                ExecuteDrinkAction()
                drinkState := "pressing"
                stateChangeTime := currentTime
            }
        case "pressing":
            if ((currentTime - stateChangeTime) >= 80) {  ; 按下80ms后释放
                ReleaseDrinkKey(cachedDrinkKey)
                drinkState := "cooldown"
                stateChangeTime := currentTime
            }
        case "cooldown":
            if ((currentTime - stateChangeTime) >= 1000) {  ; 冷却1秒后回到idle
                drinkState := "idle"
                if (hpDetectionEnabled) {
                    mainGui.drinkStatusText.Text := "🟡 待命中"
                    mainGui.drinkStatusText.Opt("c0xFF8000")
                }
            }
    }
    
    ; 技能状态机
    switch skillState {
        case "idle":
            if (lastHP > 30 && !skillReleaseActive) {
                skillReleaseActive := true
                ExecuteSkillAction()
                skillState := "pressing"
                skillStateChangeTime := currentTime
            } else if (lastHP <= 30 && skillReleaseActive) {
                skillReleaseActive := false
                skillState := "idle"
                mainGui.skillStatusText.Text := "🔴 停止"
                mainGui.skillStatusText.Opt("cRed")
                AddInfo("🛡️ 停止技能释放: 血量" . lastHP . "% ≤ 30%")
            }
        case "pressing":
            if ((currentTime - skillStateChangeTime) >= cachedSkillHoldTime) {
                ReleaseSkillKey(cachedSkillKey)
                skillState := "waiting"
                skillStateChangeTime := currentTime
            }
        case "waiting":
            if (lastHP <= 30) {  ; 血量过低，停止技能
                skillReleaseActive := false
                skillState := "idle"
                mainGui.skillStatusText.Text := "🔴 停止"
                mainGui.skillStatusText.Opt("cRed")
                AddInfo("🛡️ 停止技能释放: 血量" . lastHP . "% ≤ 30%")
            } else if ((currentTime - skillStateChangeTime) >= cachedSkillInterval) {
                ExecuteSkillAction()
                skillState := "pressing"
                skillStateChangeTime := currentTime
            }
    }
}

; ===================================================================
; 执行喝药操作
; ===================================================================
ExecuteDrinkAction() {
    global cachedDrinkKey, lastDrinkTime, mainGui, lastHP
    
    try {
        IbSend("{" . cachedDrinkKey . " down}")
        lastDrinkTime := A_TickCount
        mainGui.drinkStatusText.Text := "🟢 已喝药"
        mainGui.drinkStatusText.Opt("cGreen")
        AddInfo("💊 自动喝药: 血量" . lastHP . "% < 75% | 按键: " . cachedDrinkKey)
    } catch Error as e {
        AddInfo("❌ 喝药失败: " . e.message)
    }
}

; ===================================================================
; 执行技能操作
; ===================================================================
ExecuteSkillAction() {
    global cachedSkillKey, lastSkillTime, mainGui, lastHP, skillKeyPressed
    
    try {
        IbSend("{" . cachedSkillKey . " down}")
        skillKeyPressed := true  ; 设置按键按下状态
        lastSkillTime := A_TickCount
        mainGui.skillStatusText.Text := "🟢 释放中"
        mainGui.skillStatusText.Opt("cGreen")
        AddInfo("⚔️ 技能释放: " . cachedSkillKey . " (血量: " . lastHP . "%)")
    } catch Error as e {
        AddInfo("❌ 技能释放失败: " . e.message)
    }
}

; ===================================================================
; 重置所有状态
; ===================================================================
ResetAllStates() {
    ; 重置静态变量中的状态
    ; 这需要在停止系统时调用，确保下次启动时状态干净
    static drinkState := "idle"
    static skillState := "idle"
    static stateChangeTime := 0
    static skillStateChangeTime := 0
    
    drinkState := "idle"
    skillState := "idle"
    stateChangeTime := 0
    skillStateChangeTime := 0
}

; ===================================================================
; 检测POE2窗口是否激活
; ===================================================================
IsPOE2Active() {
    ; 获取当前激活窗口的标题
    try {
        activeTitle := WinGetTitle("A")
        
        ; 更全面的POE2窗口标题检测
        poe2Patterns := [
            "Path of Exile 2",
            "POE2", 
            "PathOfExile2",
            "Path of Exile II",
            "PathOfExile II",
            "poe2",
            "path of exile 2",
            "exile 2",
            "exile ii"
        ]
        
        ; 检查每个模式
        for pattern in poe2Patterns {
            if (InStr(activeTitle, pattern)) {
                return true
            }
        }
        
        ; 检查进程名称（备用方法）
        try {
            processName := WinGetProcessName("A")
            if (InStr(processName, "poe2") || InStr(processName, "exile") || 
                InStr(processName, "PathOfExile")) {
                return true
            }
        } catch {
            ; 忽略进程名获取错误
        }
        
    } catch {
        ; 如果获取窗口标题失败，返回false
        return false
    }
    return false
}

; 调试窗口检测
DebugWindowDetection(*) {
    try {
        ; 获取当前激活窗口信息
        activeTitle := WinGetTitle("A")
        processName := WinGetProcessName("A")
        windowClass := WinGetClass("A")
        windowPID := WinGetPID("A")
        
        AddInfo("🔍 当前窗口调试信息:")
        AddInfo("   标题: " . activeTitle)
        AddInfo("   进程名: " . processName)
        AddInfo("   类名: " . windowClass)
        AddInfo("   PID: " . windowPID)
        
        ; 检测是否被识别为POE2
        isPOE2 := IsPOE2Active()
        AddInfo("   POE2识别: " . (isPOE2 ? "✅ 是" : "❌ 否"))
        
        ; 显示详细分析
        MsgBox("当前激活窗口信息:`n`n" .
               "窗口标题: " . activeTitle . "`n" .
               "进程名称: " . processName . "`n" .
               "窗口类名: " . windowClass . "`n" .
               "进程ID: " . windowPID . "`n`n" .
               "POE2识别状态: " . (isPOE2 ? "✅ 已识别为POE2" : "❌ 未识别为POE2"), 
               "窗口检测调试", 64)
        
    } catch Error as e {
        AddInfo("❌ 窗口检测调试失败: " . e.message)
    }
}

; 血量检测状态监控
HPDetectionStatus(*) {
    global hpDetectionEnabled, isRunning, lastHP
    
    try {
        isPOE2 := IsPOE2Active()
        currentHP := GetHP()
        
        AddInfo("🩸 血量检测状态监控:")
        AddInfo("   血量检测启用: " . (hpDetectionEnabled ? "✅ 是" : "❌ 否"))
        AddInfo("   系统运行: " . (isRunning ? "✅ 是" : "❌ 否"))
        AddInfo("   POE2窗口激活: " . (isPOE2 ? "✅ 是" : "❌ 否"))
        AddInfo("   当前血量: " . currentHP . "%")
        AddInfo("   上次血量: " . lastHP . "%")
        
        if (!isPOE2) {
            AddInfo("⚠️ 检测到POE2窗口未激活，这可能是血量检测不工作的原因")
            AddInfo("💡 请切换到POE2游戏窗口，然后重新测试")
        }
        
        if (!hpDetectionEnabled) {
            AddInfo("⚠️ 血量检测未启用，请先勾选'启用血量检测'")
        }
        
        if (!isRunning) {
            AddInfo("⚠️ 系统未运行，请点击'启动系统'按钮")
        }
        
    } catch Error as e {
        AddInfo("❌ 状态监控失败: " . e.message)
    }
}

; 技能释放时序分析
SkillTimingAnalysis(*) {
    global cachedSkillInterval, cachedSkillHoldTime, cachedSkillKey, mainGui
    global skillReleaseActive, lastSkillTime, skillKeyPressed
    
    try {
        AddInfo("⚔️ 技能释放时序详细分析:")
        AddInfo("   ==========================================")
        
        ; 读取当前配置值
        currentHoldTime := Integer(mainGui.skillHoldEdit.Text)
        currentInterval := Integer(mainGui.skillIntervalEdit.Text)
        
        AddInfo("📊 当前配置:")
        AddInfo("   技能按键: " . cachedSkillKey . " (主键盘数字键)")
        AddInfo("   按下时间: " . currentHoldTime . " ms")
        AddInfo("   释放间隔: " . currentInterval . " ms")
        AddInfo("   缓存按下时间: " . cachedSkillHoldTime . " ms")
        AddInfo("   缓存释放间隔: " . cachedSkillInterval . " ms")
        
        ; 计算实际的技能释放频率
        totalCycle := currentHoldTime + currentInterval
        skillsPerSecond := 1000.0 / totalCycle
        skillsPerMinute := skillsPerSecond * 60
        
        AddInfo("🕐 时序分析:")
        AddInfo("   单次循环时间: " . totalCycle . " ms")
        AddInfo("   理论每秒技能数: " . Round(skillsPerSecond, 2) . " 次")
        AddInfo("   理论每分钟技能数: " . Round(skillsPerMinute, 1) . " 次")
        AddInfo("   实际间隔 = 按下" . currentHoldTime . "ms + 等待" . currentInterval . "ms")
        
        ; 状态机分析
        AddInfo("🔄 状态机工作流程:")
        AddInfo("   1. idle → pressing: 检查血量 > 30%")
        AddInfo("   2. pressing (" . currentHoldTime . "ms): 按键按下状态")
        AddInfo("   3. waiting (" . currentInterval . "ms): 等待下次释放")
        AddInfo("   4. 循环: pressing → waiting → pressing...")
        
        ; 当前状态
        AddInfo("📍 当前状态:")
        AddInfo("   技能释放激活: " . (skillReleaseActive ? "✅ 是" : "❌ 否"))
        AddInfo("   按键按下状态: " . (skillKeyPressed ? "✅ 按下" : "❌ 释放"))
        AddInfo("   上次技能时间: " . (lastSkillTime > 0 ? (A_TickCount - lastSkillTime) . "ms 前" : "从未"))
        
        ; 性能分析
        AddInfo("⚡ 性能分析:")
        AddInfo("   按键检测频率: 每250ms (4次/秒)")
        AddInfo("   状态机响应时间: ≤250ms")
        AddInfo("   时序精度: ±100ms (受主循环频率影响)")
        
        ; 优化建议
        if (currentInterval < 500) {
            AddInfo("⚠️ 间隔较短 (<500ms)，可能过于频繁")
        }
        if (currentHoldTime < 50) {
            AddInfo("⚠️ 按下时间较短 (<50ms)，可能无法正确识别")
        }
        if (currentHoldTime > 200) {
            AddInfo("⚠️ 按下时间较长 (>200ms)，可能影响响应")
        }
        
        ; 实际测试建议
        AddInfo("🧪 测试建议:")
        AddInfo("   1. 启动系统后观察日志中的技能释放消息")
        AddInfo("   2. 血量需要 > 30% 才会开始技能释放")
        AddInfo("   3. POE2窗口必须处于激活状态")
        AddInfo("   4. 查看状态栏中的'技能释放'指示器")
        
        AddInfo("   ==========================================")
        
    } catch Error as e {
        AddInfo("❌ 技能时序分析失败: " . e.message)
    }
}

; 切换性能模式
TogglePerformanceMode(*) {
    global performanceMode, debugMode
    
    performanceMode := !performanceMode
    
    if (performanceMode) {
        debugMode := false
        AddInfo("🚀 性能模式已启用")
        AddInfo("📊 优化特性:")
        AddInfo("   ✅ 采样血量检测（跳跃扫描）")
        AddInfo("   ✅ 像素检测缓存")
        AddInfo("   ✅ 窗口检测缓存")
        AddInfo("   ✅ 日志批量更新")
        AddInfo("   ✅ 降低循环频率")
        AddInfo("   ❌ 调试日志输出")
    } else {
        debugMode := true
        AddInfo("🔍 调试模式已启用")
        AddInfo("📊 调试特性:")
        AddInfo("   ✅ 完整血量检测（逐像素扫描）")
        AddInfo("   ✅ 详细日志输出")
        AddInfo("   ✅ 实时性能监控")
        AddInfo("   ❌ 缓存优化")
        
        ; 立即刷新日志缓冲区
        FlushLogBuffer()
    }
    
    ; 重新加载循环以应用新设置
    global isRunning, hpDetectionEnabled
    if (isRunning || hpDetectionEnabled) {
        ; 重启循环以应用新的性能设置
        SetTimer(UnifiedMainLoop, 0)
        newInterval := performanceMode ? 100 : 50  ; 性能模式使用更低的频率
        SetTimer(UnifiedMainLoop, newInterval)
        AddInfo("🔄 主循环已重启，频率: " . newInterval . "ms")
    }
}

; ===================================================================
; 按键转换函数 - 确保发送主键盘数字键
; ===================================================================
ConvertToMainKeyboardKey(key) {
    ; 将数字键转换为主键盘数字键格式
    switch key {
        case "1": return "1"
        case "2": return "2"
        case "3": return "3"
        case "4": return "4"
        case "5": return "5"
        case "6": return "6"
        case "7": return "7"
        case "8": return "8"
        case "9": return "9"
        case "0": return "0"
        default: return key  ; 其他按键保持不变
    }
}

; ===================================================================
; 添加信息到状态显示 - 优化版本
; ===================================================================
AddInfo(text) {
    global mainGui
    static logBuffer := []
    static lastFlushTime := 0
    static maxBufferSize := 10
    
    ; 获取当前时间
    currentTime := FormatTime(, "HH:mm:ss")
    newText := "[" . currentTime . "] " . text
    
    ; 添加到缓冲区而不是立即更新GUI
    logBuffer.Push(newText)
    
    ; 当缓冲区满或距离上次刷新超过2秒时，批量更新GUI
    currentTick := A_TickCount
    if (logBuffer.Length >= maxBufferSize || (currentTick - lastFlushTime) >= 2000) {
        FlushLogBuffer()
        lastFlushTime := currentTick
    }
}

; 刷新日志缓冲区到GUI
FlushLogBuffer() {
    global mainGui
    static logBuffer := []
    
    if (logBuffer.Length = 0) {
        return
    }
    
    ; 批量更新GUI文本
    newLogText := ""
    for item in logBuffer {
        newLogText .= item . "`r`n"
    }
    
    ; 将新消息添加到顶部
    mainGui.infoText.Text := newLogText . mainGui.infoText.Text
    
    ; 限制日志长度，保留最新的100行
    lines := StrSplit(mainGui.infoText.Text, "`n")
    if (lines.Length > 100) {
        newLines := []
        Loop 100 {
            if (A_Index <= lines.Length) {
                newLines.Push(lines[A_Index])
            }
        }
        mainGui.infoText.Text := ""
        for line in newLines {
            mainGui.infoText.Text .= line . "`n"
        }
    }
    
    ; 清空缓冲区
    logBuffer := []
    
    ; 将光标移到顶部显示最新消息
    try {
        mainGui.infoText.Focus()
        Send("^{Home}")
    } catch {
        ; 忽略焦点错误
    }
}

; ===================================================================
; 显示状态信息
; ===================================================================
ShowStatus(*) {
    global ghubInitialized, isRunning, hpDetectionEnabled, lastHP, skillReleaseActive
    global drinkCooldown, sendInterval, mainGui

    statusText := "🎮 POE2自动喝药 + GHUB硬件按键发送器状态`n`n"
    statusText .= "系统状态:`n"
    statusText .= "• GHUB 驱动: " . (ghubInitialized ? "✅ 已初始化" : "❌ 未初始化") . "`n"
    statusText .= "• 系统运行状态: " . (isRunning ? "🟢 运行中" : "🔴 已停止") . "`n"
    statusText .= "• 血量检测: " . (hpDetectionEnabled ? "✅ 启用" : "❌ 禁用") . "`n"
    statusText .= "• 当前血量: " . lastHP . "%`n"
    statusText .= "• 技能释放: " . (skillReleaseActive ? "🟢 释放中" : "🔴 停止") . "`n`n"

    statusText .= "当前设置:`n"
    statusText .= "• 喝药按键: " . mainGui.drinkKeyEdit.Text . " (主键盘数字键)`n"
    statusText .= "• 技能按键: " . mainGui.skillKeyEdit.Text . " (主键盘数字键)`n"
    statusText .= "• 喝药冷却: " . drinkCooldown . " ms`n"
    statusText .= "• 检测间隔: " . sendInterval . " ms`n"
    statusText .= "• 技能按下: " . mainGui.skillHoldEdit.Text . " ms`n"
    statusText .= "• 技能间隔: " . mainGui.skillIntervalEdit.Text . " ms`n"

    statusText .= "自动逻辑:`n"
    statusText .= "• 血量 < 75%: 自动喝药 (按下80ms，释放)`n"
    statusText .= "• 血量 > 30%: 释放技能 (按下" . mainGui.skillHoldEdit.Text . "ms，间隔" . mainGui.skillIntervalEdit.Text . "ms)`n"
    statusText .= "• 血量 ≤ 30%: 停止技能释放`n"
    statusText .= "• 智能检测: 仅在POE2窗口激活时发送按键`n"
    statusText .= "• 统一主循环: 血量检测100ms，按键操作200ms，GUI更新500ms`n`n"

    statusText .= "快捷键:`n"
    statusText .= "• F1: 启动/停止系统`n"
    statusText .= "• F2: 测试按键输入`n"
    statusText .= "• F3: 重新初始化驱动`n"
    statusText .= "• F4: 显示状态信息`n"
    statusText .= "• F5: 鼠标位置检测`n"
    statusText .= "• F6: 技能时序分析`n"
    statusText .= "• F7: 切换性能模式`n"
    statusText .= "• F12: 退出程序`n`n"

    statusText .= "reWASD 配置建议:`n"
    statusText .= "• 在 reWASD 中选择 'Logitech Virtual G-series Keyboard'`n"
    statusText .= "• 将主键盘数字键映射为控制器按键`n"
    statusText .= "• 例如: 主键盘 '" . mainGui.drinkKeyEdit.Text . "' → 控制器按键 (喝药)`n"
    statusText .= "• 例如: 主键盘 '" . mainGui.skillKeyEdit.Text . "' → 控制器按键 (技能)`n"
    statusText .= "• 注意: 使用的是键盘上方一排的数字键，不是小键盘数字键`n"

    MsgBox(statusText, "系统状态", 64)
}

; ===================================================================
; 快捷键设置
; ===================================================================
F1::ToggleSystem()
F2::TestKeyInput()
F3::InitializeGHUB()
F4::ShowStatus()        ; 显示状态信息
F5::GetMousePosition()  ; 鼠标位置检测
F6::SkillTimingAnalysis()  ; 技能时序分析
F7::TogglePerformanceMode()  ; 切换性能模式
F12::ExitApp()

; ===================================================================
; 程序入口
; ===================================================================
; 检查管理员权限
if (!A_IsAdmin) {
    MsgBox("此程序需要管理员权限才能正常工作！`n`n请右键点击脚本文件，选择'以管理员身份运行'。", "需要管理员权限", 48)
    ExitApp()
}

; 加载配置文件
global appConfig := ConfigManager.LoadConfig()
appConfig := ConfigManager.AutoAdjustForResolution(appConfig)

; 根据配置初始化全局变量
hpDetectionEnabled := appConfig.hp_enabled
hpConfig.x := appConfig.hp_x
hpConfig.y := appConfig.hp_y
hpConfig.width := appConfig.hp_width
hpConfig.height := appConfig.hp_height
hpConfig.scanX := Round(appConfig.hp_x + (appConfig.hp_width / 2))
hpConfig.topY := appConfig.hp_y
hpConfig.bottomY := appConfig.hp_y + appConfig.hp_height

; 更新缓存配置
cachedDrinkKey := appConfig.drink_key
cachedSkillKey := appConfig.skill_key
cachedDrinkCooldown := appConfig.drink_cooldown
cachedSkillInterval := appConfig.skill_interval
cachedSkillHoldTime := appConfig.skill_hold_time
sendInterval := appConfig.detection_interval

; 创建并显示GUI
mainGui := CreateGUI()

; 根据加载的配置更新GUI显示
mainGui.hpEnableChk.Value := hpDetectionEnabled
mainGui.hpXEdit.Text := appConfig.hp_x
mainGui.hpYEdit.Text := appConfig.hp_y
mainGui.hpWEdit.Text := appConfig.hp_width
mainGui.hpHEdit.Text := appConfig.hp_height
mainGui.drinkKeyEdit.Text := appConfig.drink_key
mainGui.skillKeyEdit.Text := appConfig.skill_key
mainGui.cooldownEdit.Text := appConfig.drink_cooldown
mainGui.intervalEdit.Text := appConfig.detection_interval
mainGui.skillHoldEdit.Text := appConfig.skill_hold_time
mainGui.skillIntervalEdit.Text := appConfig.skill_interval

mainGui.Show("w750 h540")

; 显示启动信息
AddInfo("🚀 POE2自动喝药 + GHUB硬件按键发送器已启动 (性能优化版)")
AddInfo("📄 配置文件: " . ConfigManager.configFile)
AddInfo("📐 当前分辨率: " . A_ScreenWidth . "x" . A_ScreenHeight)
AddInfo("🎮 专为 reWASD 重映射设计")
AddInfo("🩸 集成血量检测，自动喝药和技能释放")
AddInfo("🧠 血量算法: 加权区域检测 (Python验证最佳，平均误差±12.7%)")
AddInfo("💊 喝药时序: 按下80ms，释放")
AddInfo("⚔️ 技能时序: 按下" . appConfig.skill_hold_time . "ms，释放，间隔" . appConfig.skill_interval . "ms")
AddInfo("🎯 智能窗口检测: 仅在POE2激活时发送按键")
AddInfo("🔧 统一主循环: 避免定时器冲突，提升稳定性")
AddInfo("⚡ 性能优化: " . (performanceMode ? "启用 (采样检测+缓存+批量更新)" : "禁用 (完整检测+实时日志)"))
AddInfo("🔧 主循环频率: " . (performanceMode ? "100ms" : "50ms") . " | 按F7切换性能模式")
AddInfo("💡 请先点击'初始化GHUB'按钮，然后启用血量检测")

; 设置托盘图标和菜单
A_IconTip := "POE2自动喝药 + GHUB硬件按键发送器"

; ===================================================================
; 血量检测核心功能
; ===================================================================

; 检测血量百分比
GetHP() {
    global hpConfig
    
    try {
        scanX := hpConfig.scanX
        topY := hpConfig.topY
        bottomY := hpConfig.bottomY
        
        ; 使用加权区域检测算法（经Python验证的最佳算法）
        ; 对边缘区域使用较低权重，减少装饰性红色像素的影响
        redWeightedCount := 0.0
        totalWeight := 0.0
        totalHeight := bottomY - topY + 1
        
        ; 从上到下扫描整个区域，使用加权计算
        y := topY
        while (y <= bottomY) {
            ; 计算当前像素的权重
            relativePosition := (y - topY) / totalHeight
            
            ; 边缘区域（顶部和底部10%）使用0.3权重，中间区域使用1.0权重
            if (relativePosition < 0.1 || relativePosition > 0.9) {
                weight := 0.3  ; 边缘权重低，减少装饰像素影响
            } else {
                weight := 1.0  ; 中间权重正常
            }
            
            totalWeight += weight
            
            if (IsRedColor(scanX, y)) {
                redWeightedCount += weight
            }
            
            y += 1
        }
        
        ; 计算加权血量百分比
        if (totalWeight <= 0) {
            return 0.0
        } else {
            hp := (redWeightedCount / totalWeight) * 100
            return Max(0, Min(100, Round(hp, 1)))
        }
        
    } catch Error as e {
        ; 减少错误输出频率，避免日志刷屏
        static lastErrorTime := 0
        currentTime := A_TickCount
        if (currentTime - lastErrorTime > 5000) {  ; 5秒内只输出一次错误
            AddInfo("❌ 血量检测出错: " . e.message)
            lastErrorTime := currentTime
        }
        return 0
    }
}

; 优化版血量检测 - 提升性能
GetHPOptimized() {
    global hpConfig
    static lastResult := 100
    static lastScanTime := 0
    static sampleCache := Map()
    
    try {
        currentTime := A_TickCount
        
        ; 如果距离上次扫描时间太短，返回缓存结果
        if ((currentTime - lastScanTime) < 100) {
            return lastResult
        }
        
        scanX := hpConfig.scanX
        topY := hpConfig.topY
        bottomY := hpConfig.bottomY
        totalHeight := bottomY - topY + 1
        
        ; 使用采样检测而不是逐像素扫描，大幅提升性能
        sampleStep := Max(2, Round(totalHeight / 50))  ; 最多采样50个点
        redWeightedCount := 0.0
        totalWeight := 0.0
        
        y := topY
        while (y <= bottomY) {
            ; 计算权重
            relativePosition := (y - topY) / totalHeight
            weight := (relativePosition < 0.1 || relativePosition > 0.9) ? 0.3 : 1.0
            totalWeight += weight
            
            ; 使用缓存优化红色检测
            if (IsRedColorFast(scanX, y)) {
                redWeightedCount += weight
            }
            
            y += sampleStep  ; 跳跃采样而不是逐像素
        }
        
        ; 计算血量百分比
        if (totalWeight <= 0) {
            lastResult := 0.0
        } else {
            hp := (redWeightedCount / totalWeight) * 100
            lastResult := Max(0, Min(100, Round(hp, 1)))
        }
        
        lastScanTime := currentTime
        return lastResult
        
    } catch Error as e {
        return lastResult  ; 出错时返回上次结果
    }
}

; RGB转HSV函数
RGBtoHSV(r, g, b) {
    ; 将RGB值标准化到0-1范围
    r := r / 255.0
    g := g / 255.0
    b := b / 255.0
    
    ; 找到最大值和最小值
    max_val := Max(r, g, b)
    min_val := Min(r, g, b)
    delta := max_val - min_val
    
    ; 计算V (明度)
    v := max_val
    
    ; 计算S (饱和度)
    if (max_val = 0) {
        s := 0
    } else {
        s := delta / max_val
    }
    
    ; 计算H (色相)
    if (delta = 0) {
        h := 0  ; 灰色
    } else if (max_val = r) {
        h := 60 * (((g - b) / delta) + (g < b ? 6 : 0))
    } else if (max_val = g) {
        h := 60 * (((b - r) / delta) + 2)
    } else {
        h := 60 * (((r - g) / delta) + 4)
    }
    
    ; 确保H在0-360范围内
    if (h < 0) {
        h += 360
    }
    
    ; 返回HSV值 (H: 0-360, S: 0-1, V: 0-1)
    return {h: h, s: s, v: v}
}

; 使用HSV的红色检测函数
IsRedColorHSV(x, y) {
    c := PixelGetColor(x, y)
    r := (c >> 16) & 0xFF
    g := (c >> 8) & 0xFF
    b := c & 0xFF
    
    ; 快速排除明显的非红色
    ; 1. 排除过暗的像素 (总亮度太低)
    totalBrightness := r + g + b
    if (totalBrightness <= 120) {
        return false
    }
    
    ; 2. 排除过亮的白色像素
    if (r >= 240 && g >= 240 && b >= 240) {
        return false
    }
    
    ; 3. 排除过暗的黑色像素
    if (r <= 15 && g <= 15 && b <= 15) {
        return false
    }
    
    ; 转换为HSV
    hsv := RGBtoHSV(r, g, b)
    h := hsv.h
    s := hsv.s
    v := hsv.v
    
    ; HSV红色检测条件
    ; 红色的色相范围：0-30度 和 330-360度
    ; 饱和度要求：至少0.3 (30%)
    ; 明度要求：至少0.2 (20%)
    
    isRedHue := (h >= 0 && h <= 30) || (h >= 330 && h <= 360)
    hasEnoughSaturation := s >= 0.3  ; 饱和度至少30%
    hasEnoughBrightness := v >= 0.2  ; 明度至少20%
    
    ; 橙红色范围 (30-60度，但饱和度要更高)
    isOrangeRed := (h >= 30 && h <= 60) && s >= 0.5 && v >= 0.3
    
    ; 深红色 (允许较低的明度但要求更高的饱和度)
    isDarkRed := isRedHue && s >= 0.4 && v >= 0.15
    
    return (isRedHue && hasEnoughSaturation && hasEnoughBrightness) || isOrangeRed || isDarkRed
}

; 红色检测 - 使用HSV和RGB双重验证
IsRedColor(x, y) {
    ; 首先使用HSV检测
    hsvResult := IsRedColorHSV(x, y)
    
    ; 如果HSV检测通过，直接返回true
    if (hsvResult) {
        return true
    }
    
    ; 如果HSV检测失败，使用原RGB方法作为备用
    c := PixelGetColor(x, y)
    r := (c >> 16) & 0xFF
    g := (c >> 8) & 0xFF
    b := c & 0xFF
    
    ; 排除明显非红色的情况
    if (r >= 240 && g >= 240 && b >= 240) {
        return false
    }
    
    if (r <= 15 && g <= 15 && b <= 15) {
        return false
    }
    
    totalBrightness := r + g + b
    if (totalBrightness <= 120) {
        return false
    }
    
    if (Abs(r - g) <= 8 && Abs(r - b) <= 8 && Abs(g - b) <= 8 && r <= g + 5) {
        return false
    }
    
    ; RGB备用检测条件 (更严格)
    redDominant := (r >= 60 && r > g + 15 && r > b + 15)
    traditionalRed := (r >= 80 && r <= 200 && g <= 80 && b <= 80 && r > g + 20 && r > b + 20)
    
    return redDominant || traditionalRed
}

; 快速红色检测 - 仅使用RGB，跳过HSV计算
IsRedColorFast(x, y) {
    static colorCache := Map()
    static cacheTime := Map()
    
    ; 简单的坐标缓存，避免重复检测相同位置
    cacheKey := x . "," . y
    currentTime := A_TickCount
    
    if (colorCache.Has(cacheKey) && (currentTime - cacheTime[cacheKey]) < 500) {
        return colorCache[cacheKey]
    }
    
    c := PixelGetColor(x, y)
    r := (c >> 16) & 0xFF
    g := (c >> 8) & 0xFF
    b := c & 0xFF
    
    ; 快速排除（简化逻辑）
    if (r < 45 || (r + g + b) <= 120 || (r >= 240 && g >= 240 && b >= 240)) {
        result := false
    } else {
        ; 简化的红色检测条件
        result := (r > g + 10 && r > b + 10 && r >= 50)
    }
    
    ; 缓存结果
    colorCache[cacheKey] := result
    cacheTime[cacheKey] := currentTime
    
    ; 清理过期缓存（每100次调用清理一次）
    static cleanupCounter := 0
    cleanupCounter++
    if (cleanupCounter >= 100) {
        for key, time in cacheTime {
            if ((currentTime - time) > 2000) {  ; 清理2秒前的缓存
                colorCache.Delete(key)
                cacheTime.Delete(key)
            }
        }
        cleanupCounter := 0
    }
    
    return result
}

; 快速POE2窗口检测 - 缓存结果
IsPOE2ActiveFast() {
    static lastResult := false
    static lastCheckTime := 0
    static cachedTitle := ""
    
    currentTime := A_TickCount
    
    ; 缓存1秒，避免频繁调用系统API
    if ((currentTime - lastCheckTime) < 1000) {
        return lastResult
    }
    
    try {
        ; 只检查窗口标题，跳过进程名检查以提升性能
        activeTitle := WinGetTitle("A")
        
        ; 如果标题相同，返回缓存结果
        if (activeTitle = cachedTitle && cachedTitle != "") {
            lastCheckTime := currentTime
            return lastResult
        }
        
        cachedTitle := activeTitle
        
        ; 简化的POE2检测（只检查关键词）
        result := (InStr(activeTitle, "Path of Exile") || InStr(activeTitle, "POE2") || InStr(activeTitle, "poe2"))
        
        lastResult := result
        lastCheckTime := currentTime
        return result
        
    } catch {
        ; 出错时返回上次结果
        return lastResult
    }
}

; 更新血量显示
UpdateHPDisplay(hp) {
    global mainGui
    
    ; 更新血量文本和颜色
    if (hp <= 15) {
        mainGui.hpText.Text := Format("⚠️ {:.1f}%", hp)
        mainGui.hpText.Opt("cRed")
    } else if (hp <= 30) {
        mainGui.hpText.Text := Format("⚡ {:.1f}%", hp)
        mainGui.hpText.Opt("c0xFF8000")
    } else if (hp <= 75) {
        mainGui.hpText.Text := Format("💛 {:.1f}%", hp)
        mainGui.hpText.Opt("c0xFF8000")
    } else {
        mainGui.hpText.Text := Format("💚 {:.1f}%", hp)
        mainGui.hpText.Opt("cGreen")
    }
}

; 释放技能按键
ReleaseSkillKey(key) {
    global skillKeyPressed
    
    try {
        IbSend("{" . key . " up}")
        skillKeyPressed := false  ; 重置按键状态
        AddInfo("⚔️ 技能按键释放: " . key)
    } catch Error as e {
        AddInfo("❌ 技能按键释放失败: " . e.message)
    }
}

; 释放喝药按键
ReleaseDrinkKey(key) {
    try {
        IbSend("{" . key . " up}")
        AddInfo("💊 喝药按键释放: " . key)
    } catch Error as e {
        AddInfo("❌ 喝药按键释放失败: " . e.message)
    }
}

; 快速血量检测
QuickHPTest(*) {
    global hpDetectionEnabled, hpConfig
    
    if (!hpDetectionEnabled) {
        MsgBox("请先启用血量检测！", "提示", 48)
        return
    }
    
    try {
        AddInfo("⚡ 开始快速血量检测 (加权区域算法 - 经Python验证最佳)...")
        
        ; 显示检测区域信息
        scanX := hpConfig.scanX
        topY := hpConfig.topY
        bottomY := hpConfig.bottomY
        totalHeight := bottomY - topY + 1
        
        AddInfo("📍 检测参数:")
        AddInfo("   扫描X坐标: " . scanX)
        AddInfo("   扫描范围: Y=" . topY . " 到 Y=" . bottomY . " (高度=" . totalHeight . ")")
        AddInfo("   算法: 加权区域检测 (边缘0.3权重，中间1.0权重)")
        
        ; 进行详细扫描 - 使用加权区域算法
        redWeightedCount := 0.0
        totalWeight := 0.0
        redPixelCount := 0
        firstRedY := -1
        lastRedY := -1
        
        ; 完整扫描并记录红色像素
        y := topY
        while (y <= bottomY) {
            ; 计算权重
            relativePosition := (y - topY) / totalHeight
            if (relativePosition < 0.1 || relativePosition > 0.9) {
                weight := 0.3  ; 边缘权重
            } else {
                weight := 1.0  ; 中间权重
            }
            
            totalWeight += weight
            
            if (IsRedColor(scanX, y)) {
                redWeightedCount += weight
                redPixelCount++
                if (firstRedY = -1) {
                    firstRedY := y
                }
                lastRedY := y
            }
            y += 1
        }
        
        AddInfo("🔍 扫描结果 (加权区域算法):")
        AddInfo("   总权重: " . Round(totalWeight, 1))
        AddInfo("   红色加权计数: " . Round(redWeightedCount, 1))
        AddInfo("   红色像素数: " . redPixelCount . "/" . totalHeight)
        AddInfo("   首个红色Y: " . (firstRedY = -1 ? "无" : firstRedY))
        AddInfo("   最后红色Y: " . (lastRedY = -1 ? "无" : lastRedY))
        
        ; 计算血量百分比
        if (totalWeight > 0) {
            calculatedHP := (redWeightedCount / totalWeight) * 100
            simpleHP := (redPixelCount / totalHeight) * 100
            
            AddInfo("   加权血量: " . Round(calculatedHP, 1) . "%")
            AddInfo("   简单血量: " . Round(simpleHP, 1) . "% (对比)")
            AddInfo("   算法优势: " . Round(Abs(calculatedHP - simpleHP), 1) . "% 差异")
            
            ; 分析红色像素分布
            if (redPixelCount > 0) {
                redSpan := lastRedY - firstRedY + 1
                density := redPixelCount / redSpan
                
                ; 分析边缘和中间区域的红色分布
                edgeRedCount := 0
                middleRedCount := 0
                
                y := topY
                while (y <= bottomY) {
                    relativePos := (y - topY) / totalHeight
                    if (IsRedColor(scanX, y)) {
                        if (relativePos < 0.1 || relativePos > 0.9) {
                            edgeRedCount++
                        } else {
                            middleRedCount++
                        }
                    }
                    y += 1
                }
                
                AddInfo("   红色区域跨度: " . redSpan . " 像素")
                AddInfo("   分布密度: " . Round(density, 2))
                AddInfo("   边缘红色像素: " . edgeRedCount . " (权重0.3)")
                AddInfo("   中间红色像素: " . middleRedCount . " (权重1.0)")
                AddInfo("   权重效果: 减少了 " . Round((edgeRedCount * 0.7), 1) . " 个边缘像素的影响")
            }
        } else {
            AddInfo("   ❌ 未检测到任何像素！")
            calculatedHP := 0
        }
        
        ; 使用原函数获取最终结果
        currentHP := GetHP()
        AddInfo("📈 最终检测结果: " . currentHP . "%")
        
        ; 检测关键点的颜色信息
        edgeY1 := topY + Round(totalHeight * 0.05)  ; 5%位置
        midY := Round((topY + bottomY) / 2)         ; 50%位置
        edgeY2 := bottomY - Round(totalHeight * 0.05)  ; 95%位置
        
        ; 获取关键点的具体颜色值
        edge1Color := PixelGetColor(scanX, edgeY1)
        midColor := PixelGetColor(scanX, midY)
        edge2Color := PixelGetColor(scanX, edgeY2)
        
        ; 解析RGB值
        edge1R := (edge1Color >> 16) & 0xFF
        edge1G := (edge1Color >> 8) & 0xFF
        edge1B := edge1Color & 0xFF
        
        midR := (midColor >> 16) & 0xFF
        midG := (midColor >> 8) & 0xFF
        midB := midColor & 0xFF
        
        edge2R := (edge2Color >> 16) & 0xFF
        edge2G := (edge2Color >> 8) & 0xFF
        edge2B := edge2Color & 0xFF
        
        AddInfo("🎨 关键点颜色分析:")
        AddInfo("   顶部边缘 (" . scanX . "," . edgeY1 . "): RGB(" . edge1R . "," . edge1G . "," . edge1B . ") " . (IsRedColor(scanX, edgeY1) ? "✅红色(权重0.3)" : "❌非红"))
        AddInfo("   中部核心 (" . scanX . "," . midY . "): RGB(" . midR . "," . midG . "," . midB . ") " . (IsRedColor(scanX, midY) ? "✅红色(权重1.0)" : "❌非红"))
        AddInfo("   底部边缘 (" . scanX . "," . edgeY2 . "): RGB(" . edge2R . "," . edge2G . "," . edge2B . ") " . (IsRedColor(scanX, edgeY2) ? "✅红色(权重0.3)" : "❌非红"))
        
        ; 更新血量显示
        UpdateHPDisplay(currentHP)
        
        ; 提供改进建议
        if (currentHP <= 0 && redPixelCount = 0) {
            AddInfo("💡 改进建议:")
            AddInfo("   1. 使用'🔲 绘制区域'确认检测位置是否正确")
            AddInfo("   2. 使用'🖱️ 鼠标位置'在血量条上检查颜色")
            AddInfo("   3. 尝试调整检测区域的位置和大小")
            AddInfo("   4. 确保游戏血量条可见且未被遮挡")
        } else if (redPixelCount > 0) {
            AddInfo("💡 算法分析:")
            AddInfo("   ✅ 加权区域算法工作正常 (Python验证最佳)")
            AddInfo("   📊 平均误差预期: ±12.7% (经验证)")
            AddInfo("   🎯 特别适合处理装饰性红色像素")
            AddInfo("   🔧 边缘权重降低70%，专注核心血量区域")
        }
        
    } catch Error as e {
        AddInfo("❌ 快速检测失败: " . e.message)
    }
}

; 清空日志
ClearLog(*) {
    global mainGui
    mainGui.infoText.Text := ""
    AddInfo("🗑️ 日志已清空")
}
