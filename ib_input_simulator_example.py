#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IbInputSimulator Python版本使用示例
演示如何使用键盘输入功能
"""

import time
import logging
from ib_input_simulator import IbInputSimulator, send_key, send_keys, send_special_key

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def basic_usage_example():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建IbInputSimulator实例
    sim = IbInputSimulator(driver="AnyDriver")
    
    try:
        # 检查状态
        status = sim.get_status()
        print(f"驱动状态: {status}")
        
        if not sim.is_available():
            print("警告: 没有可用的输入驱动")
            return
        
        print("\n请在5秒内切换到目标窗口...")
        time.sleep(5)
        
        # 发送单个按键
        print("发送按键 '1'")
        sim.send_key("1")
        time.sleep(0.5)
        
        # 发送字符串
        print("发送字符串 'Hello'")
        sim.send_keys("Hello")
        time.sleep(0.5)
        
        # 发送特殊按键
        print("发送空格键")
        sim.send_special_key("space")
        time.sleep(0.5)
        
        print("发送回车键")
        sim.send_special_key("enter")
        
    finally:
        sim.cleanup()

def context_manager_example():
    """上下文管理器使用示例"""
    print("\n=== 上下文管理器示例 ===")
    
    print("请在3秒内切换到目标窗口...")
    time.sleep(3)
    
    # 使用上下文管理器，自动清理资源
    with IbInputSimulator(driver="Logitech") as sim:
        if sim.is_available():
            print("发送数字序列 '12345'")
            sim.send_keys("12345", interval=0.2)
        else:
            print("Logitech驱动不可用")

def convenience_functions_example():
    """便捷函数使用示例"""
    print("\n=== 便捷函数示例 ===")
    
    print("请在3秒内切换到目标窗口...")
    time.sleep(3)
    
    # 使用便捷函数，无需手动管理实例
    print("使用便捷函数发送按键")
    send_key("a")
    time.sleep(0.3)
    
    send_keys("bc", interval=0.3)
    time.sleep(0.3)
    
    send_special_key("space")
    time.sleep(0.3)
    
    send_keys("test", interval=0.2)

def gaming_example():
    """游戏应用示例"""
    print("\n=== 游戏应用示例 ===")
    
    print("模拟游戏按键序列...")
    print("请在5秒内切换到游戏窗口...")
    time.sleep(5)
    
    with IbInputSimulator(driver="Logitech") as sim:
        if not sim.is_available():
            print("Logitech驱动不可用，尝试其他驱动")
            sim = IbInputSimulator(driver="AnyDriver")
        
        if sim.is_available():
            # 模拟游戏操作序列
            actions = [
                ("1", "使用技能1"),
                ("2", "使用技能2"), 
                ("space", "跳跃"),
                ("3", "使用技能3"),
                ("4", "使用药水")
            ]
            
            for key, description in actions:
                print(f"执行: {description} (按键: {key})")
                if key in ['space']:
                    sim.send_special_key(key)
                else:
                    sim.send_key(key)
                time.sleep(0.8)  # 技能冷却时间
        else:
            print("没有可用的输入驱动")

def test_all_drivers():
    """测试所有驱动"""
    print("\n=== 驱动兼容性测试 ===")
    
    drivers = [
        "AnyDriver",
        "Logitech", 
        "LogitechGHubNew",
        "Razer",
        "SendInput"
    ]
    
    for driver in drivers:
        print(f"\n测试驱动: {driver}")
        try:
            with IbInputSimulator(driver=driver) as sim:
                status = sim.get_status()
                print(f"  初始化状态: {status['available']}")
                print(f"  IbSimulator: {status['ib_simulator_initialized']}")
                print(f"  Logitech GHUB: {status['logitech_ghub_initialized']}")
                
                if status['available']:
                    print(f"  {driver} 驱动可用 ✓")
                else:
                    print(f"  {driver} 驱动不可用 ✗")
                    
        except Exception as e:
            print(f"  {driver} 驱动测试失败: {e}")

def interactive_test():
    """交互式测试"""
    print("\n=== 交互式测试 ===")
    print("输入要发送的按键，输入 'quit' 退出")
    
    with IbInputSimulator() as sim:
        if not sim.is_available():
            print("错误: 没有可用的输入驱动")
            return
        
        print(f"当前驱动状态: {sim.get_status()}")
        print("请切换到目标窗口，然后回到这里输入按键...")
        
        while True:
            try:
                user_input = input("\n输入按键 (或 'quit' 退出): ").strip()
                
                if user_input.lower() == 'quit':
                    break
                
                if not user_input:
                    continue
                
                print(f"发送按键: {user_input}")
                
                # 给用户时间切换窗口
                print("3秒后发送...")
                time.sleep(3)
                
                if len(user_input) == 1:
                    result = sim.send_key(user_input)
                else:
                    # 尝试作为特殊键
                    result = sim.send_special_key(user_input)
                    if not result:
                        # 作为字符串发送
                        result = sim.send_keys(user_input)
                
                print(f"发送结果: {'成功' if result else '失败'}")
                
            except KeyboardInterrupt:
                print("\n用户中断")
                break
            except Exception as e:
                print(f"错误: {e}")

if __name__ == "__main__":
    print("IbInputSimulator Python版本 - 使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        basic_usage_example()
        context_manager_example() 
        convenience_functions_example()
        test_all_drivers()
        gaming_example()
        
        # 交互式测试（可选）
        response = input("\n是否运行交互式测试? (y/n): ")
        if response.lower() == 'y':
            interactive_test()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
    
    print("\n示例程序结束")