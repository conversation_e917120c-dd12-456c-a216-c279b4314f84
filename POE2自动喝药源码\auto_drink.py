# -*- coding: utf-8 -*-

import cv2
import numpy as np
import yaml
import time
import mss
import pyautogui
import threading
from pynput import keyboard
import ctypes
from contextlib import contextmanager
import win32gui
import win32ui
import win32con
import keyboard
import logging
import os

# 全局变量
running = False
exit_program = False
health_last_drink = 0
mana_last_drink = 0
config = None

# 添加Windows API的鼠标位置保护
user32 = ctypes.windll.user32
mouse_position_backup = (0, 0)

# 日志配置
LOG_FILE = 'auto_drink.log'
LOG_MAX_LINES = 1000

def setup_logger():
    # 检查并重置log文件
    try:
        if os.path.exists(LOG_FILE):
            with open(LOG_FILE, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            if len(lines) > LOG_MAX_LINES:
                with open(LOG_FILE, 'w', encoding='utf-8') as f:
                    f.write('')
    except Exception as e:
        print(f"日志文件检查失败: {e}")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s %(levelname)s: %(message)s',
        handlers=[
            logging.FileHandler(LOG_FILE, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

setup_logger()
log = logging.getLogger(__name__)

@contextmanager
def protect_mouse_position():
    """保护鼠标位置，防止截图和其他操作导致鼠标闪烁"""
    global mouse_position_backup
    
    # 检查是否需要鼠标位置保护
    if config.get('anti_flicker', True):
        # 备份当前鼠标位置
        mouse_position_backup = pyautogui.position()
        
        try:
            # 执行被包装的代码
            yield
        finally:
            # 恢复鼠标位置
            if config.get('anti_flicker', True):
                pyautogui.moveTo(mouse_position_backup[0], mouse_position_backup[1], duration=0)
    else:
        # 不需要鼠标保护，直接执行
        yield

def load_config():
    """加载配置文件"""
    try:
        with open('config.yaml', 'r', encoding='utf-8') as f:
            cfg = yaml.safe_load(f)
            
            # 添加防闪烁默认配置
            if 'anti_flicker' not in cfg:
                cfg['anti_flicker'] = True
                
            # 添加直接读取屏幕像素配置
            if 'direct_pixel_read' not in cfg:
                cfg['direct_pixel_read'] = False
                
            return cfg
    except Exception as e:
        log.error(f"加载配置文件失败: {e}")
        exit(1)

def get_screen_region():
    """获取屏幕区域"""
    if config['screen']['fullscreen']:
        # 使用配置文件中设置的分辨率，而不是系统分辨率
        width = config['screen']['resolution']['width']
        height = config['screen']['resolution']['height']
        return {"top": 0, "left": 0, "width": width, "height": height}
    else:
        region = config['screen']['region']
        return {"top": region['y'], "left": region['x'], "width": region['width'], "height": region['height']}

def capture_screen():
    """截取屏幕"""
    # 如果启用了直接读取像素模式，返回None表示使用直接像素读取
    if config.get('direct_pixel_read', False):
        return None
        
    # 使用鼠标位置保护功能包装截图操作
    with protect_mouse_position():
        with mss.mss() as sct:
            # 如果是全屏模式，使用配置的分辨率
            if config['screen']['fullscreen']:
                # 获取显示器索引（如果配置中存在）
                monitor_index = config['screen'].get('monitor_index', 1)  # 默认主显示器
                
                # 获取对应显示器信息
                if monitor_index < len(sct.monitors):
                    monitor = sct.monitors[monitor_index]
                    
                    # 使用配置的分辨率或显示器的原始分辨率
                    width = config['screen']['resolution']['width']
                    height = config['screen']['resolution']['height']
                    
                    # 创建截取区域
                    screen_region = {
                        "top": monitor["top"],
                        "left": monitor["left"],
                        "width": width,
                        "height": height
                    }
                    
                    # 捕获屏幕
                    sct_img = sct.grab(screen_region)
                    return np.array(sct_img)
                else:
                    # 显示器索引无效，使用默认分辨率
                    width = config['screen']['resolution']['width']
                    height = config['screen']['resolution']['height']
                    screen_region = {"top": 0, "left": 0, "width": width, "height": height}
                    sct_img = sct.grab(screen_region)
                    return np.array(sct_img)
            else:
                # 自定义区域模式，使用配置中的区域
                region = config['screen']['region']
                screen_region = {"top": region['y'], "left": region['x'], 
                              "width": region['width'], "height": region['height']}
                sct_img = sct.grab(screen_region)
                return np.array(sct_img)

def direct_grab_area(area):
    """直接抓取屏幕特定区域，不使用截图，避免鼠标闪烁"""
    x, y, w, h = area['x'], area['y'], area['width'], area['height']
    
    # 获取桌面DC
    hdc = win32gui.GetDC(0)
    
    # 创建兼容DC和位图
    dc_obj = win32ui.CreateDCFromHandle(hdc)
    compatible_dc = dc_obj.CreateCompatibleDC()
    
    # 创建位图对象
    bitmap = win32ui.CreateBitmap()
    bitmap.CreateCompatibleBitmap(dc_obj, w, h)
    
    # 选择位图到兼容DC
    old_obj = compatible_dc.SelectObject(bitmap)
    
    # 复制屏幕内容到位图
    compatible_dc.BitBlt((0, 0), (w, h), dc_obj, (x, y), win32con.SRCCOPY)
    
    # 获取位图信息
    bitmap_bits = bitmap.GetBitmapBits(True)
    img = np.frombuffer(bitmap_bits, dtype=np.uint8)
    img.shape = (h, w, 4)  # RGBA格式
    
    # 清理资源
    compatible_dc.SelectObject(old_obj)
    compatible_dc.DeleteDC()
    dc_obj.DeleteDC()
    win32gui.ReleaseDC(0, hdc)
    win32gui.DeleteObject(bitmap.GetHandle())
    
    # 转换为BGR格式以便与OpenCV兼容
    return cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)

def detect_resource_percentage(img, resource_type):
    """检测资源(血量/蓝量)百分比，最大连续有效行段法"""
    try:
        resource_config = config[resource_type]
        detection_area = direct_grab_area(resource_config['detection_area']) if img is None else img
        h, w = detection_area.shape[:2]
        hsv = cv2.cvtColor(detection_area, cv2.COLOR_BGR2HSV)
        lower = np.array(resource_config['color_lower'])
        upper = np.array(resource_config['color_upper'])
        mask = cv2.inRange(hsv, lower, upper)
        vertical_sum = np.sum(mask > 0, axis=1)
        threshold = w * 0.6
        is_filled = vertical_sum > threshold
        max_len = 0
        current_len = 0
        for i in range(h-1, -1, -1):
            if is_filled[i]:
                current_len += 1
                if current_len > max_len:
                    max_len = current_len
            else:
                current_len = 0
        progress = max_len / h * 100 if h > 0 else 0
        return progress
    except Exception as e:
        log.error(f"检测{resource_type}时发生错误: {e}")
        return 0

def use_potion(potion_key):
    """使用药剂"""
    with protect_mouse_position():
        try:
            # 使用直接按键而不是pyautogui，减少对鼠标的影响
            if config.get('anti_flicker', True):
                if len(potion_key) == 1:
                    vk_code = ord(potion_key.upper())
                    user32.keybd_event(vk_code, 0, 0, 0)
                    time.sleep(0.03)
                    user32.keybd_event(vk_code, 0, 2, 0)
                else:
                    pyautogui.press(potion_key)
            else:
                pyautogui.press(potion_key)
            log.info(f"使用药剂: {potion_key}")
        except Exception as e:
            log.error(f"使用药剂失败: {e}")

def on_f5():
    global running
    running = not running
    log.info(f"自动喝药已{'启动' if running else '停止'}（F5）")
    # winsound.MessageBeep(winsound.MB_ICONASTERISK)

def on_f6():
    global exit_program
    log.info("退出程序（F6）")
    exit_program = True
    # winsound.MessageBeep(winsound.MB_ICONASTERISK)

keyboard.add_hotkey('f5', on_f5)
keyboard.add_hotkey('f6', on_f6)

def main_loop():
    """主循环"""
    global health_last_drink, mana_last_drink
    shield_last_drink = 0
    while not exit_program:
        if not running:
            time.sleep(0.1)
            continue
        try:
            # 检测血量、蓝量、护盾
            health_percent = detect_resource_percentage(None, 'health')
            mana_percent = detect_resource_percentage(None, 'mana')
            shield_percent = detect_resource_percentage(None, 'shield')
            current_time = time.time()
            # 检查血量
            if config['health'].get('enable', True):
                if health_percent < config['health']['threshold']:
                    if current_time - health_last_drink >= config['control']['health_potion_cooldown']:
                        use_potion(config['health']['potion_key'])
                        health_last_drink = current_time
            # 检查护盾
            if config['shield'].get('enable', False):
                if shield_percent < config['shield']['threshold']:
                    if current_time - shield_last_drink >= config['control']['health_potion_cooldown']:
                        use_potion(config['shield']['potion_key'])
                        shield_last_drink = current_time
            # 检查蓝量
            if config['mana'].get('enable', True):
                if mana_percent < config['mana']['threshold']:
                    if current_time - mana_last_drink >= config['control']['mana_potion_cooldown']:
                        use_potion(config['mana']['potion_key'])
                        mana_last_drink = current_time
            # 短暂休眠以减少CPU使用
            time.sleep(0.1)
        except Exception as e:
            log.error(f"发生错误: {e}")
            time.sleep(1)

if __name__ == '__main__':
    # 加载配置
    config = load_config()
    
    # 自动补全shield字段
    if config is not None and 'shield' not in config:
        config['shield'] = {
            'detection_area': config['health']['detection_area'].copy(),
            'color_lower': config['health']['color_lower'].copy(),
            'color_upper': config['health']['color_upper'].copy(),
            'threshold': 60,
            'potion_key': config['health']['potion_key'],
            'enable': False
        }
    
    # 启动主循环
    main_thread = threading.Thread(target=main_loop)
    main_thread.start()
    # 保持脚本运行，直到F6退出
    keyboard.wait('f6') 