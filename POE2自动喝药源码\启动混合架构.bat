@echo off
chcp 65001 >nul
cd /d "%~dp0"

echo ================================================================
echo                POE2 自动喝药 - 混合架构启动器
echo                Python检测 + AHK硬件输入 = 最高性能
echo ================================================================
echo.

echo [信息] 正在启动混合架构系统...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Python，请先安装Python 3.7+
    echo [提示] 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查必要文件
if not exist "start_hybrid.py" (
    echo [错误] 未找到启动脚本 start_hybrid.py
    pause
    exit /b 1
)

if not exist "hybrid_gui.py" (
    echo [错误] 未找到控制界面 hybrid_gui.py
    pause
    exit /b 1
)

if not exist "detection_engine.py" (
    echo [错误] 未找到检测引擎 detection_engine.py
    pause
    exit /b 1
)

if not exist "hardware_input.ahk" (
    echo [错误] 未找到硬件输入脚本 hardware_input.ahk
    pause
    exit /b 1
)

if not exist "hybrid_config.yaml" (
    echo [错误] 未找到配置文件 hybrid_config.yaml
    pause
    exit /b 1
)

echo [信息] 所有必要文件检查完成
echo [信息] 启动Python混合架构系统...
echo.

REM 启动Python脚本
python start_hybrid.py

if errorlevel 1 (
    echo.
    echo [错误] 系统启动失败
    echo [提示] 请检查错误信息并确保所有依赖项已正确安装
    pause
)

echo.
echo [信息] 系统已退出
pause