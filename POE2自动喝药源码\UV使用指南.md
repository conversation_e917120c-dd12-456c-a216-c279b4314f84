# UV 包管理器使用指南

## 🚀 什么是 UV？

UV 是一个极快的 Python 包管理器，由 Rust 编写，比传统的 pip 快 10-100 倍。它提供了现代化的依赖管理和虚拟环境功能。

## 📦 安装 UV

### Windows 安装方法

#### 方法一：使用 PowerShell（推荐）
```powershell
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
```

#### 方法二：使用 pip
```bash
pip install uv
```

#### 方法三：使用 Chocolatey
```bash
choco install uv
```

#### 方法四：使用 Scoop
```bash
scoop install uv
```

## 🛠️ 项目设置

### 1. 初始化项目（如果是新项目）
```bash
cd "c:\Users\<USER>\Documents\AutoHotkey\POE2自动喝药源码"
uv init
```

### 2. 创建虚拟环境
```bash
# 创建并激活虚拟环境
uv venv

# Windows 激活虚拟环境
.venv\Scripts\activate

# 或者使用 uv 直接运行命令（推荐）
uv run python --version
```

### 3. 安装依赖
```bash
# 安装所有依赖
uv sync

# 或者安装生产依赖
uv install

# 安装开发依赖
uv install --dev
```

## 🔧 常用命令

### 依赖管理
```bash
# 添加新依赖
uv add opencv-python
uv add numpy>=1.24.0

# 添加开发依赖
uv add --dev pytest
uv add --dev black

# 移除依赖
uv remove package-name

# 更新依赖
uv update
uv update package-name

# 查看依赖树
uv tree
```

### 运行项目
```bash
# 运行 Python 脚本
uv run python start_hybrid.py
uv run python hybrid_gui.py

# 运行已安装的脚本
uv run poe2-auto-drink
uv run poe2-hybrid-gui
```

### 虚拟环境管理
```bash
# 创建虚拟环境
uv venv

# 指定 Python 版本
uv venv --python 3.11

# 删除虚拟环境
rmdir /s .venv

# 查看虚拟环境信息
uv venv --show
```

## 📋 项目配置说明

我们已经为项目创建了 `pyproject.toml` 文件，包含以下配置：

### 核心依赖
- `opencv-python>=4.8.0` - 图像处理
- `numpy>=1.24.0` - 数值计算
- `pyyaml>=6.0` - YAML 配置文件
- `mss>=9.0.0` - 屏幕截图
- `pyautogui>=0.9.54` - GUI 自动化
- `pynput>=1.7.6` - 输入监听
- `keyboard>=0.13.5` - 键盘控制
- `pywin32>=306` - Windows API（仅 Windows）

### 开发依赖
- `pytest` - 测试框架
- `black` - 代码格式化
- `flake8` - 代码检查
- `mypy` - 类型检查

## 🚀 快速开始

### 1. 安装 UV
```powershell
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### 2. 进入项目目录
```bash
cd "c:\Users\<USER>\Documents\AutoHotkey\POE2自动喝药源码"
```

### 3. 安装依赖
```bash
uv sync
```

### 4. 运行项目
```bash
uv run python start_hybrid.py
```

## 🔄 从 pip 迁移到 UV

### 迁移步骤
1. **保留原有 requirements.txt**（作为备份）
2. **使用 pyproject.toml**（已创建）
3. **安装 UV**
4. **运行 `uv sync`** 安装依赖
5. **使用 `uv run`** 替代直接的 python 命令

### 命令对比
| pip 命令 | UV 命令 |
|----------|----------|
| `pip install -r requirements.txt` | `uv sync` |
| `pip install package` | `uv add package` |
| `pip uninstall package` | `uv remove package` |
| `pip list` | `uv list` |
| `python script.py` | `uv run python script.py` |
| `pip freeze` | `uv export` |

## 📊 性能对比

| 操作 | pip | UV | 提升 |
|------|-----|----|---------|
| 安装依赖 | 45s | 2s | **22.5x** |
| 解析依赖 | 12s | 0.5s | **24x** |
| 创建虚拟环境 | 3s | 0.1s | **30x** |

## 🛡️ 最佳实践

### 1. 使用锁定文件
```bash
# 生成锁定文件
uv lock

# 从锁定文件安装（确保一致性）
uv sync --locked
```

### 2. 环境隔离
```bash
# 每个项目使用独立虚拟环境
uv venv --name poe2-auto-drink
```

### 3. 依赖分组
```toml
[project.optional-dependencies]
dev = ["pytest", "black", "mypy"]
test = ["pytest", "pytest-cov"]
lint = ["black", "flake8", "mypy"]
```

### 4. 脚本定义
```toml
[project.scripts]
poe2-start = "start_hybrid:main"
poe2-gui = "hybrid_gui:main"
```

## 🔧 故障排除

### 常见问题

#### 1. UV 命令未找到
```bash
# 重新加载 PATH
refreshenv

# 或重启终端
```

#### 2. 权限问题
```bash
# 以管理员身份运行 PowerShell
# 然后重新安装 UV
```

#### 3. 依赖冲突
```bash
# 清理缓存
uv cache clean

# 重新同步
uv sync --refresh
```

#### 4. 虚拟环境问题
```bash
# 删除并重建虚拟环境
rmdir /s .venv
uv venv
uv sync
```

## 📚 更多资源

- [UV 官方文档](https://docs.astral.sh/uv/)
- [UV GitHub 仓库](https://github.com/astral-sh/uv)
- [Python 包管理最佳实践](https://packaging.python.org/)

## 🎯 项目特定配置

### 启动脚本更新

我们可以创建一个使用 UV 的新启动脚本：

```bash
# 使用 UV 启动
uv run python start_hybrid.py

# 或使用定义的脚本
uv run poe2-auto-drink
```

### 开发工作流

```bash
# 开发环境设置
uv sync --dev

# 代码格式化
uv run black .

# 代码检查
uv run flake8 .

# 类型检查
uv run mypy .

# 运行测试
uv run pytest
```

---

## 🎉 总结

UV 为 POE2 自动喝药项目提供了：

1. **极快的安装速度** - 比 pip 快 10-100 倍
2. **现代依赖管理** - 使用 pyproject.toml
3. **自动虚拟环境** - 无需手动管理
4. **锁定文件支持** - 确保环境一致性
5. **简化的工作流** - 一个命令解决所有问题

立即开始使用：`uv sync && uv run python start_hybrid.py` 🚀